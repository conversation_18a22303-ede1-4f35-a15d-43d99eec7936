<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<!-- <key>CFBundleDisplayName</key>
	<string>Korrency</string> -->
	<key>CFBundleDisplayName</key>
	<string>$(APP_DISPLAY_NAME)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>korrency</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Korrency uses your location to keep your account secure and enable faster verification when needed.</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Korrency uses your location to keep your account secure and enable faster verification when needed.</string>
	<key>NSContactsUsageDescription</key>
	<string>Quickly send money to friends on Korrency by connecting with your contacts.</string>
	<key>NSFaceIDUsageDescription</key>
	<string>Korrency needs Face ID for secure identity verification.</string>
	<key>NSCameraUsageDescription</key>
	<string>Korrency needs access to your camera for identity verification and document capture. The images will be used only for verification.</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>Korrency needs access to your microphone for a liveliness check during identity verification. The audio ensures your presence and security.</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Allow Korrency to save images to your gallery?</string>
	<key>FirebaseAppDelegateProxyEnabled</key>
	<false/>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>App needs access to photo lib for profile images</string>
	<key>UIBackgroundModes</key>
	<array>
		<string>fetch</string>
		<string>remote-notification</string>
	</array>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>sms</string>
		<string>tel</string>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
	</array>
	<key>FacebookAppID</key>
	<string>1794408288137731</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>facebook</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>fb1794408288137731</string>
			</array>
		</dict>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>com.apple.developer.associated-domains</key>
	<array>
		<string>applinks:test.onelink.me</string>
	</array>
</dict>
</plist>
