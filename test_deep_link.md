# Deep Link Testing Guide

## Test Commands

### 1. Test with <PERSON><PERSON> (Android Debug Bridge)
```bash
# Test the OneLink URL
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "https://korrency.onelink.me/9BIc?referrer=BER58NY88" \
  korrency.mobile.com

# Test the custom scheme
adb shell am start \
  -W -a android.intent.action.VIEW \
  -d "korrency.com://mainactivity" \
  korrency.mobile.com
```

### 2. Test in Browser
Open these URLs in Chrome on your Android device:
- `https://korrency.onelink.me/9BIc?referrer=BER58NY88`
- `https://korrency.onelink.me/9BIc`

### 3. Check App Link Verification
```bash
# Check if your app is verified for the domain
adb shell pm get-app-links korrency.mobile.com
```

## Expected Behavior
- If app is installed: Should open the app directly
- If app is not installed: Should redirect to Play Store
- Check logs for deep link callback messages

## Troubleshooting
1. Ensure app is installed and not in "Don't open by default" mode
2. Clear app defaults: Settings > Apps > Korrency > Open by default > Clear defaults
3. Check if domain verification is successful
4. Verify SHA256 fingerprint matches in assetlinks.json
