# Analytics Integration Guide

## Overview

This document provides comprehensive guidance for the multi-platform analytics integration in the Korrency Flutter app. The integration includes Firebase Analytics, Facebook Analytics, AppsFlyer, and Mixpanel for comprehensive event tracking, conversion optimization, and cross-platform marketing attribution.

## Table of Contents

1. [Setup and Configuration](#setup-and-configuration)
2. [Firebase Analytics](#firebase-analytics)
3. [Facebook Analytics](#facebook-analytics)
4. [Event Tracking](#event-tracking)
5. [Conversion Events](#conversion-events)
6. [Google Analytics 4 (GA4) Integration](#google-analytics-4-ga4-integration)
7. [Google Ads Integration](#google-ads-integration)
8. [Facebook Ads Integration](#facebook-ads-integration)
9. [Testing and Debugging](#testing-and-debugging)
10. [Best Practices](#best-practices)

## Setup and Configuration

### Dependencies

The following analytics dependencies are included in `pubspec.yaml`:

```yaml
dependencies:
  firebase_core: ^3.12.1
  firebase_analytics: ^11.4.0
  firebase_messaging: ^15.2.4
  facebook_app_events: ^0.19.2
  appsflyer_sdk: ^6.15.2
  mixpanel_flutter: ^2.4.0
```

### Platform Configuration

#### iOS Configuration

- `ios/Runner/GoogleService-Info.plist` is configured with `IS_ANALYTICS_ENABLED` set to `true`
- Firebase Analytics is automatically initialized with the app

#### Android Configuration

- `android/app/google-services.json` contains the Firebase project configuration
- Firebase Analytics is automatically initialized with the app

### Service Initialization

Firebase Analytics is initialized in `AppInitService` alongside other analytics services:

```dart
// Initialize Firebase Analytics
await FirebaseAnalyticsService.instance.initialize(debugMode: true);

// Initialize Facebook Analytics
await FacebookAnalyticsService.instance.initialize(debugMode: true);
```

## Firebase Analytics

Firebase Analytics provides comprehensive event tracking and integrates seamlessly with Google Analytics 4 and Google Ads for conversion optimization.

## Facebook Analytics

Facebook Analytics provides powerful event tracking optimized for Facebook and Instagram advertising campaigns. The integration uses Facebook's standard events for optimal ad delivery and conversion tracking.

### Environment Configuration

Facebook App credentials are configured in environment files:

```bash
# .env, .env.dev, .env.staging, .env.prod
FACEBOOK_APP_ID=****************
FACEBOOK_CLIENT_TOKEN=********************************
```

### Platform Configuration

#### Android Configuration

- `android/app/src/main/res/values/strings.xml` contains Facebook App ID and Client Token
- Facebook SDK is automatically configured

#### iOS Configuration

- `ios/Runner/Info.plist` contains Facebook App ID, Client Token, and URL schemes
- Facebook SDK deep linking is configured with `fb****************` URL scheme

### Facebook Standard Events

The integration uses Facebook's standard events for optimal ad optimization:

- **`fb_mobile_complete_registration`**: User account creation
- **`fb_mobile_achievement_unlocked`**: KYC completion
- **`fb_mobile_add_payment_info`**: Deposit/payment setup
- **`fb_mobile_purchase`**: Money transfer completion

### Service Usage

```dart
// Basic event logging
await FacebookAnalyticsService.instance.logEvent('event_name', {
  'parameter_key': 'parameter_value',
  'timestamp': DateTime.now().toIso8601String(),
});

// User identification
await FacebookAnalyticsService.instance.setUserId('user_id');

// User data for advanced matching
await FacebookAnalyticsService.instance.setUserData(
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  phone: '+**********',
  country: 'CA',
);

// Purchase event with value
await FacebookAnalyticsService.instance.logPurchase(
  amount: 100.0,
  currency: 'CAD',
  parameters: {'transaction_id': 'tx_123'},
);
```

## Event Tracking

### Core Service

The `FirebaseAnalyticsService` provides a comprehensive interface for event tracking:

```dart
// Basic event logging
await FirebaseAnalyticsService.instance.logEvent('event_name', {
  'parameter_key': 'parameter_value',
  'timestamp': DateTime.now().toIso8601String(),
});

// Screen view tracking
await FirebaseAnalyticsService.instance.logScreenView('screen_name');

// User identification
await FirebaseAnalyticsService.instance.setUserId('user_id');

// User properties
await FirebaseAnalyticsService.instance.setUserProperty('property_name', 'value');
```

### Event Constants

All event names and parameters are defined in `lib/core/constants/analytics_constants.dart`:

- `AnalyticsEvents`: Event name constants
- `AnalyticsParameters`: Parameter key constants
- `AnalyticsValues`: Predefined parameter values
- `AnalyticsScreens`: Screen name constants

## Conversion Events

### 1. Account Creation (`create_account`)

**Triggered:** When user successfully completes registration
**Location:** `lib/core/vm/auth/onboard_vm.dart` - `signUp()` method

```dart
await FirebaseAnalyticsService.instance.trackCreateAccount(
  userId: userData["id"]?.toString() ?? "",
  registrationMethod: AnalyticsValues.registrationEmail,
  userCountry: _country?.name,
  userTier: AnalyticsValues.tierBasic,
);
```

**Parameters:**

- `user_id`: Unique user identifier
- `registration_method`: email/phone/social
- `user_country`: User's country
- `user_tier`: basic/premium/vip
- `timestamp`: Event timestamp

### 2. KYC Completion (`complete_kyc`)

**Triggered:** When user completes identity verification via Onfido
**Location:** `lib/core/vm/auth/kyc_vm.dart` - `updateOnfidoUser()` method

```dart
await FirebaseAnalyticsService.instance.trackCompleteKyc(
  userId: user.id ?? "",
  kycLevel: AnalyticsValues.kycAdvanced,
  verificationMethod: 'onfido',
  completionTimeSeconds: 0,
  userTier: AnalyticsValues.tierPremium,
);
```

**Parameters:**

- `user_id`: Unique user identifier
- `kyc_level`: basic/advanced/premium
- `verification_method`: onfido/manual/other
- `completion_time`: Time taken to complete KYC (seconds)
- `user_tier`: Updated user tier after KYC

### 3. Money Deposit (`deposit_money`)

**Triggered:** When user creates a virtual account for deposits
**Location:** `lib/core/vm/wallet/wallet_vm.dart` - `createVirtualAccount()` method

```dart
await FirebaseAnalyticsService.instance.trackDepositMoney(
  userId: user.id ?? "",
  amount: 0.0, // Virtual account creation
  currency: currency?.code ?? "Unknown",
  paymentMethod: AnalyticsValues.paymentBankTransfer,
  transactionId: "virtual_account_${DateTime.now().millisecondsSinceEpoch}",
  corridor: "${user.country}-${currency?.code}",
);
```

**Parameters:**

- `user_id`: Unique user identifier
- `amount`: Deposit amount (numeric)
- `currency`: 3-letter currency code
- `payment_method`: card/bank_transfer/wallet/crypto
- `transaction_id`: Unique transaction identifier
- `corridor`: source_country-destination_country
- `value`: Amount for GA4 conversion value

### 4. Money Transfer (`send_money`)

**Triggered:** When user successfully completes a money transfer
**Location:** `lib/core/vm/core/send_money_vm.dart` - `transfer()` method

```dart
await FirebaseAnalyticsService.instance.trackSendMoney(
  userId: user.id ?? "",
  amount: amount,
  currency: _fromConvertWallet!.currency?.code ?? "Unknown",
  senderCountry: user.country ?? "Unknown",
  recipientCountry: _recipientCurrency!.country ?? "Unknown",
  transactionId: transactionData?["id"]?.toString() ?? "unknown_${DateTime.now().millisecondsSinceEpoch}",
  transferMethod: _transferMethod?.name ?? AnalyticsValues.transferStandard,
  corridor: "${user.country}-${_recipientCurrency!.country}",
);
```

**Parameters:**

- `user_id`: Unique user identifier
- `amount`: Transfer amount (numeric)
- `currency`: Source currency code
- `sender_country`: Sender's country
- `recipient_country`: Recipient's country
- `transaction_id`: Unique transaction identifier
- `transfer_method`: instant/standard/express
- `corridor`: source_country-destination_country
- `value`: Amount for GA4 conversion value

## Google Analytics 4 (GA4) Integration

### Linking Firebase to GA4

1. **Firebase Console Setup:**

   - Go to Firebase Console > Project Settings > Integrations
   - Click "Link" next to Google Analytics
   - Select or create a GA4 property
   - Enable data sharing settings

2. **Data Streams Configuration:**

   - In GA4, go to Admin > Data Streams
   - Configure iOS and Android app streams
   - Enable Enhanced Measurement for automatic events

3. **Custom Events in GA4:**
   - Events automatically flow from Firebase to GA4
   - Custom parameters are available in GA4 reports
   - Events appear in GA4 within 24-48 hours

### Conversion Configuration in GA4

1. **Mark Events as Conversions:**

   - Go to GA4 > Configure > Conversions
   - Toggle on the following events as conversions:
     - `create_account`
     - `complete_kyc`
     - `deposit_money`
     - `send_money`

2. **Set Conversion Values:**

   - For monetary events (`deposit_money`, `send_money`), GA4 automatically uses the `value` parameter
   - Configure custom conversion values if needed

3. **Audience Creation:**
   - Create audiences based on conversion events
   - Use for remarketing and optimization

## Google Ads Integration

### Linking Google Ads to Firebase

1. **Firebase Console:**

   - Go to Project Settings > Integrations
   - Click "Link" next to Google Ads
   - Select your Google Ads account

2. **Import Conversions to Google Ads:**

   - In Google Ads, go to Tools & Settings > Conversions
   - Click "+" to create new conversion
   - Select "Import" > "Firebase"
   - Import the conversion events:
     - `create_account`
     - `complete_kyc`
     - `deposit_money`
     - `send_money`

3. **Campaign Optimization:**
   - Use imported conversions for Smart Bidding
   - Set up conversion tracking for app install campaigns
   - Configure attribution models (last-click, data-driven, etc.)

### Conversion Values and Attribution

- **Monetary Events:** Use actual transaction amounts
- **Non-Monetary Events:** Assign estimated values based on business impact
- **Attribution Window:** Configure based on user journey length (typically 30 days)

## Facebook Ads Integration

### Linking Facebook Analytics to Facebook Ads Manager

1. **Facebook Business Manager Setup:**

   - Ensure your Facebook App (ID: ****************) is connected to your Business Manager
   - Verify that the app has proper permissions for analytics and advertising

2. **Events Manager Configuration:**

   - Go to Facebook Business Manager > Events Manager
   - Select your app and verify that events are being received
   - The four standard events should appear automatically:
     - `fb_mobile_complete_registration`
     - `fb_mobile_achievement_unlocked`
     - `fb_mobile_add_payment_info`
     - `fb_mobile_purchase`

3. **Custom Conversions Setup:**
   - Create custom conversions based on the standard events
   - Set conversion values for monetary events (`fb_mobile_purchase`, `fb_mobile_add_payment_info`)
   - Configure attribution windows (1-day view, 7-day click is recommended for financial apps)

### Facebook Ads Campaign Optimization

1. **App Install Campaigns:**

   - Use `fb_mobile_complete_registration` as the optimization event
   - Target users likely to complete registration
   - Set up lookalike audiences based on registered users

2. **Engagement Campaigns:**

   - Use `fb_mobile_achievement_unlocked` (KYC completion) for user quality optimization
   - Target users who are likely to complete verification
   - Create custom audiences of verified users for retention campaigns

3. **Revenue Campaigns:**

   - Use `fb_mobile_purchase` (money transfers) as the primary conversion event
   - Optimize for purchase value to maximize transaction amounts
   - Set up dynamic product ads for cross-border money transfer corridors

4. **Advanced Matching:**
   - The integration includes user data hashing for improved attribution
   - Email, phone, and location data are automatically hashed and sent to Facebook
   - This improves conversion tracking accuracy by 10-15%

### Facebook Pixel Integration (Web)

If you have a web version of Korrency, ensure the Facebook Pixel is configured to track the same events:

```javascript
// Web equivalent events
fbq("track", "CompleteRegistration");
fbq("track", "AchievementUnlocked", { content_name: "KYC Completion" });
fbq("track", "AddPaymentInfo");
fbq("track", "Purchase", { value: amount, currency: "CAD" });
```

### Cross-Platform Attribution

The Facebook Analytics integration works seamlessly with:

- **Firebase Analytics**: Events are tracked in both systems for comprehensive analysis
- **AppsFlyer**: Deep linking and attribution data is shared
- **Mixpanel**: User journey analysis across all touchpoints

### Privacy and Compliance

1. **iOS 14.5+ ATT (App Tracking Transparency):**

   - The integration automatically handles ATT consent
   - Events are still tracked for optimization even without IDFA
   - Advanced matching helps maintain attribution accuracy

2. **GDPR Compliance:**

   - User data can be cleared using `clearUserData()` method
   - Event tracking can be disabled per user preferences
   - Data minimization is implemented by default

3. **Data Processing:**
   - All user data is hashed before sending to Facebook
   - No PII (Personally Identifiable Information) is sent in plain text
   - Events are batched and sent securely via HTTPS

## Testing and Debugging

### Debug Mode

Both Firebase and Facebook Analytics include debug mode for development:

```dart
// Enable debug mode during initialization
await FirebaseAnalyticsService.instance.initialize(debugMode: true);
await FacebookAnalyticsService.instance.initialize(debugMode: true);

// Test Firebase Analytics integration
await FirebaseAnalyticsService.instance.testAnalyticsIntegration();
await FirebaseAnalyticsService.instance.testConversionEvents();

// Test Facebook Analytics integration
await FacebookAnalyticsService.instance.testAnalyticsIntegration();
await FacebookAnalyticsService.instance.testConversionEvents();
```

### Firebase Analytics DebugView

1. **Enable DebugView:**

   - In Firebase Console, go to Analytics > DebugView
   - Events from debug builds appear in real-time

2. **Testing Events:**
   - Use the test methods in `FirebaseAnalyticsService`
   - Verify events appear in DebugView
   - Check parameter values and data types

### Facebook Analytics Testing

1. **Events Manager Testing:**

   - Go to Facebook Business Manager > Events Manager
   - Select your app and go to Test Events
   - Events from debug builds appear in real-time

2. **Testing Events:**
   - Use the test methods in `FacebookAnalyticsService`
   - Verify events appear in Events Manager
   - Check parameter values and standard event mapping

### Verification Checklist

**Firebase Analytics:**

- [ ] Firebase Analytics initialized successfully
- [ ] Debug events appear in Firebase DebugView
- [ ] Events flow to GA4 (24-48 hour delay)
- [ ] Conversion events marked in GA4
- [ ] Google Ads conversions imported

**Facebook Analytics:**

- [ ] Facebook Analytics initialized successfully
- [ ] Debug events appear in Facebook Events Manager
- [ ] Standard events are properly mapped
- [ ] Advanced matching is working
- [ ] Facebook Ads conversions are tracking

**General:**

- [ ] Test all four conversion events (both platforms)
- [ ] Verify user properties are set
- [ ] Check screen view tracking
- [ ] Cross-platform event consistency verified

## Best Practices

### Event Naming and Parameters

1. **Consistent Naming:** Use the constants from `analytics_constants.dart`
2. **Parameter Limits:**
   - Firebase: 40 chars for parameter names, 100 chars for values
   - Facebook: 40 chars for parameter names, 1000 chars for values
3. **Data Types:** Ensure parameters are strings, numbers, or booleans
4. **Required Parameters:** Always include `user_id` and `timestamp`
5. **Standard Events:** Use Facebook standard events when possible for optimal ad optimization

### Privacy and Compliance

1. **User Consent:** Ensure proper consent for analytics tracking
2. **Data Minimization:** Only track necessary data
3. **PII Protection:** Avoid logging personally identifiable information
4. **GDPR Compliance:** Implement data deletion capabilities

### Performance Considerations

1. **Batch Events:** Both Firebase and Facebook automatically batch events for efficiency
2. **Offline Support:** Events are cached and sent when connectivity returns
3. **Error Handling:** All tracking methods include proper error handling
4. **Debug Logging:** Disable debug mode in production builds
5. **Event Frequency:** Avoid excessive event logging to prevent rate limiting
6. **Memory Usage:** Both services manage memory efficiently with automatic cleanup

### Monitoring and Maintenance

1. **Regular Testing:** Use test methods to verify integration for both platforms
2. **Event Validation:** Monitor both Firebase and Facebook Analytics reports for data quality
3. **Conversion Tracking:** Regularly check Google Ads and Facebook Ads conversion data
4. **Performance Monitoring:** Track analytics service performance and errors
5. **Cross-Platform Consistency:** Ensure events are tracked consistently across all platforms
6. **Attribution Analysis:** Compare attribution data between Firebase and Facebook for insights

## Troubleshooting

### Common Issues

1. **Events Not Appearing:**

   - Check Firebase project configuration
   - Verify app bundle ID matches Firebase configuration
   - Ensure analytics collection is enabled

2. **GA4 Integration Issues:**

   - Verify Firebase-GA4 linking
   - Check data stream configuration
   - Allow 24-48 hours for data to appear

3. **Google Ads Conversion Issues:**

   - Verify Firebase-Google Ads linking
   - Check conversion import settings
   - Ensure proper attribution windows

4. **Facebook Analytics Issues:**

   - Check Facebook App ID and Client Token configuration
   - Verify app is connected to Facebook Business Manager
   - Ensure proper URL schemes are configured for iOS

5. **Facebook Ads Conversion Issues:**
   - Verify app is connected to Facebook Business Manager
   - Check Events Manager for event reception
   - Ensure standard events are properly mapped
   - Verify advanced matching is enabled

### Debug Commands

```dart
// Firebase Analytics
FirebaseAnalyticsService.instance.logAnalyticsConfig();
await FirebaseAnalyticsService.instance.testAnalyticsIntegration();
FirebaseAnalyticsService.instance.enableDebugLogging();
FirebaseAnalyticsService.instance.disableDebugLogging();

// Facebook Analytics
FacebookAnalyticsService.instance.logAnalyticsConfig();
await FacebookAnalyticsService.instance.testAnalyticsIntegration();
FacebookAnalyticsService.instance.enableDebugLogging();
FacebookAnalyticsService.instance.disableDebugLogging();

// Test all conversion events
await FirebaseAnalyticsService.instance.testConversionEvents();
await FacebookAnalyticsService.instance.testConversionEvents();
```

## Support and Resources

### Firebase Analytics

- [Firebase Analytics Documentation](https://firebase.google.com/docs/analytics)
- [GA4 Integration Guide](https://support.google.com/analytics/answer/9289234)
- [Google Ads Conversion Tracking](https://support.google.com/google-ads/answer/1722022)
- [Flutter Firebase Analytics Plugin](https://pub.dev/packages/firebase_analytics)

### Facebook Analytics

- [Facebook Analytics for Apps Documentation](https://developers.facebook.com/docs/app-events)
- [Facebook App Events Best Practices](https://developers.facebook.com/docs/app-events/best-practices)
- [Facebook Ads Manager](https://www.facebook.com/business/tools/ads-manager)
- [Flutter Facebook App Events Plugin](https://pub.dev/packages/facebook_app_events)

### Cross-Platform Analytics

- [AppsFlyer Documentation](https://support.appsflyer.com/hc/en-us)
- [Mixpanel Documentation](https://developer.mixpanel.com/)
- [Mobile App Attribution Best Practices](https://blog.appsflyer.com/mobile-attribution-guide/)
