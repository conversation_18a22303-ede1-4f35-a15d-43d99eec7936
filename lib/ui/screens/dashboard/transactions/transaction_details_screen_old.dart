// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:flutter/services.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/screens.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:screenshot/screenshot.dart';

class TransactionArg {
  TransactionArg({required this.transaction});

  final Transaction transaction;
}

class TransactionDetailsScreen extends StatefulWidget {
  const TransactionDetailsScreen({
    super.key,
    required this.transactionArg,
  });

  final TransactionArg transactionArg;

  @override
  State<TransactionDetailsScreen> createState() =>
      _TransactionDetailsScreenState();
}

class _TransactionDetailsScreenState extends State<TransactionDetailsScreen> {
  ScreenshotController screenshotController = ScreenshotController();

  @override
  Widget build(BuildContext context) {
    // printty(widget.transactionArg.transaction.type,
    //     logLevel: 'transaction type');
    // printty(widget.transactionArg.transaction.convertedCurrency?.code,
    //     logLevel: 'transaction code');
    // printty(widget.transactionArg.transaction.category,
    //     logLevel: 'transaction category');
    Transaction transaction = widget.transactionArg.transaction;
    printty(transaction, level: 'transaction yes');
    return Scaffold(
      backgroundColor: AppColors.primaryBlue,
      body: SafeArea(
        bottom: false,
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const CustomHeader(
                    showHeader: true,
                    color: AppColors.white,
                    headerText: 'Transaction Details',
                  ),
                  const YBox(30),
                  Container(
                    height: Sizer.height(40),
                    width: Sizer.width(40),
                    padding: EdgeInsets.all(Sizer.radius(4)),
                    decoration: BoxDecoration(
                      color: AppColors.white,
                      borderRadius: BorderRadius.circular(30),
                    ),
                    child: svgHelper(_getSvg()),
                  ),
                  const YBox(20),
                  Text(
                    "${transaction.type?.toLowerCase() == "credit" ? "+" : "-"}${AppUtils.formatAmountDoubleString(transaction.amount ?? "")} ${transaction.currency?.code ?? ""}",
                    style: AppTypography.text28.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const YBox(4),
                  if (transaction.convertedAmount != null)
                    Text(
                      "${transaction.type?.toLowerCase() == "credit" ? "-" : "+"}${AppUtils.formatAmountDoubleString(transaction.convertedAmount ?? "")} ${transaction.convertedCurrency?.code ?? ""}",
                      style: AppTypography.text16.copyWith(
                        color: AppColors.darkBlue101,
                      ),
                    ),
                  const YBox(30),
                ],
              ),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(24),
                ).copyWith(
                  top: Sizer.height(30),
                ),
                width: Sizer.screenWidth,
                decoration: BoxDecoration(
                  color: AppColors.white,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(Sizer.radius(20)),
                    topRight: Radius.circular(Sizer.radius(20)),
                  ),
                ),
                child: ListView(
                  children: [
                    DottedBorder(
                      dashPattern: const [8, 5],
                      strokeWidth: 2,
                      borderType: BorderType.RRect,
                      radius: const Radius.circular(4),
                      color: AppColors.dottedColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(16),
                        vertical: Sizer.height(16),
                      ),
                      child: SizedBox(
                        width: Sizer.screenWidth,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomListViews.currencyListText(
                              leftText: 'Description',
                              rightText: transaction.description ?? '',
                              rightFontWeight: FontWeight.w500,
                              rightTextColor: AppColors.textGray,
                            ),
                            CustomListViews.currencyListText(
                              leftText: 'Status',
                              rightText:
                                  widget.transactionArg.transaction.status ??
                                      "",
                              rightTextColor: _getTextColor(
                                  (widget.transactionArg.transaction.status ??
                                          "")
                                      .toLowerCase()),
                              rightFontWeight: FontWeight.w600,
                            ),
                            CustomListViews.currencyListText(
                              leftText: 'Transaction Type',
                              rightText:
                                  widget.transactionArg.transaction.category ??
                                      "",
                              rightFontWeight: FontWeight.w700,
                            ),
                            CustomListViews.currencyListText(
                              margin: EdgeInsets.zero,
                              leftText: 'Fees',
                              rightText:
                                  "${transaction.fees ?? ""} ${transaction.currency?.code ?? ""}",
                              rightFontWeight: FontWeight.w500,
                              rightTextColor: AppColors.textGray,
                            ),
                            if (transaction.rateFormat != null) const YBox(24),
                            if (transaction.rateFormat != null)
                              CustomListViews.currencyListText(
                                margin: EdgeInsets.zero,
                                leftText: 'Exchange Rate',
                                rightText: transaction.rateFormat ?? '',
                                rightFontWeight: FontWeight.w500,
                                rightTextColor: AppColors.textGray,
                              ),
                          ],
                        ),
                      ),
                    ),
                    const YBox(24),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(16),
                        vertical: Sizer.height(16),
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.blu000,
                        borderRadius: BorderRadius.circular(Sizer.radius(4)),
                      ),
                      child: Column(
                        children: [
                          CustomListViews.currencyListText(
                            leftText: 'Date/Time',
                            rightText: AppUtils.formatDateTime(
                                (widget.transactionArg.transaction.createdAt ??
                                        DateTime.now())
                                    .toLocal()
                                    .toString()),
                            rightFontWeight: FontWeight.w500,
                            rightTextColor: AppColors.textGray,
                          ),
                          if (transaction.category?.toLowerCase() == "transfer")
                            CustomListViews.currencyListText(
                              leftText: 'Recipient',
                              rightText: widget
                                      .transactionArg.transaction.destination ??
                                  "",
                              rightFontWeight: FontWeight.w500,
                              rightTextColor: AppColors.textGray,
                            ),
                          if (transaction.source != null)
                            CustomListViews.currencyListText(
                              leftText: 'Source',
                              rightText:
                                  widget.transactionArg.transaction.source ??
                                      "",
                              rightFontWeight: FontWeight.w500,
                              rightTextColor: AppColors.textGray,
                            ),
                          if (transaction.category?.toLowerCase() == "transfer")
                            CustomListViews.currencyListText(
                              leftText: 'Sender',
                              rightText:
                                  context.read<AuthUserVM>().user?.fullName ??
                                      '',
                              rightFontWeight: FontWeight.w500,
                              rightTextColor: AppColors.textGray,
                            ),
                          if (transaction.destination != null &&
                              transaction.category?.toLowerCase() != "transfer")
                            CustomListViews.currencyListText(
                              leftText: 'Destination',
                              rightText: widget
                                      .transactionArg.transaction.destination ??
                                  "",
                              rightFontWeight: FontWeight.w500,
                              rightTextColor: AppColors.textGray,
                            ),
                          CustomListViews.currencyListText(
                              leftText: 'Reference',
                              rightText:
                                  widget.transactionArg.transaction.reference ??
                                      "",
                              rightFontWeight: FontWeight.w500,
                              rightTextColor: AppColors.textGray,
                              showCopyIcon: true,
                              onCopy: () {
                                Clipboard.setData(ClipboardData(
                                    text: widget.transactionArg.transaction
                                            .reference ??
                                        ""));

                                FlushBarToast.fLSnackBar(
                                  message: "Transaction reference copied",
                                  snackBarType: SnackBarType.success,
                                );
                              }),
                          if (transaction.interacSecurityAnswer != null)
                            CustomListViews.currencyListText(
                                margin: EdgeInsets.zero,
                                leftText: 'Interac Security Answer',
                                rightText: widget.transactionArg.transaction
                                        .interacSecurityAnswer ??
                                    "",
                                rightFontWeight: FontWeight.w500,
                                rightTextColor: AppColors.textGray,
                                showCopyIcon: true,
                                onCopy: () {
                                  Clipboard.setData(
                                    ClipboardData(
                                      text: widget.transactionArg.transaction
                                              .interacSecurityAnswer ??
                                          "",
                                    ),
                                  );

                                  FlushBarToast.fLSnackBar(
                                    message: "Interac security answer copied",
                                    snackBarType: SnackBarType.success,
                                  );
                                }),
                        ],
                      ),
                    ),
                    const YBox(25),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CurrencyCard(
                          svgPath: AppSvgs.message,
                          title: "Report",
                          bgColor: AppColors.opacityRed100,
                          onTap: () {
                            Navigator.pushNamed(
                              context,
                              RoutePath.createFreshDeskTicketWebview,
                              arguments: WebViewArg(
                                appBarText: "Create Ticket",
                                webURL: AppUtils.korrencyCreateTicket,
                              ),
                            );
                          },
                        ),
                        const XBox(30),
                        if (transaction.status?.toLowerCase() == "successful")
                          CurrencyCard(
                            svgPath: AppSvgs.share,
                            title: "Share",
                            onTap: () {
                              // Navigator.push(
                              //     context,
                              //     MaterialPageRoute(
                              //         builder: (context) => TransactionReceipt(
                              //               transactionArg: widget.transactionArg,
                              //             )));
                              _shareReceipt();
                            },
                          ),
                        // if (transaction.status?.toLowerCase() == "successful")
                        //   CurrencyCard(
                        //     svgPath: AppSvgs.download,
                        //     title: "Download",
                        //     bgColor: AppColors.litGrey,
                        //     onTap: () async {
                        //       final hasPermission = await _requestPermissions();
                        //       if (hasPermission) {
                        //         await _downloadReceiptToDevice();
                        //       } else {
                        //         _showErrorSnackBar(
                        //             "Permission required to save receipt");
                        //       }
                        //     },
                        //   ),
                      ],
                    ),
                    const YBox(60),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _downloadReceiptToDevice() async {
    try {
      printty('Preparing to save receipt');

      // Show loading indicator
      if (context.mounted) {
        showDialog(
          context: context,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return const AlertDialog(
              content: Row(
                children: [
                  CircularProgressIndicator(),
                  SizedBox(width: 20),
                  Text("Preparing receipt..."),
                ],
              ),
            );
          },
        );
      }

      // Capture the screenshot
      final screenshotValue = await _captureScreenshot();
      if (screenshotValue == null) {
        throw Exception('Failed to capture receipt image');
      }

      // Generate unique filename
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final fileName = 'transaction_receipt_$timestamp.png';

      // Dismiss loading dialog
      if (context.mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      if (Platform.isAndroid) {
        // For Android, use Storage Access Framework via image_gallery_saver
        // This is privacy-friendly and doesn't require MANAGE_EXTERNAL_STORAGE
        final result = await ImageGallerySaver.saveImage(
            Uint8List.fromList(screenshotValue),
            quality: 100,
            name: fileName);

        if (result['isSuccess']) {
          if (context.mounted) {
            FlushBarToast.fLSnackBar(
                message: "Receipt saved to gallery",
                snackBarType: SnackBarType.success);
          }
          printty('Receipt saved to gallery', level: "success");
        } else {
          throw Exception('Failed to save receipt: ${result['error']}');
        }
      } else if (Platform.isIOS) {
        // For iOS, use photos permission and save to gallery
        final result = await ImageGallerySaver.saveImage(
            Uint8List.fromList(screenshotValue),
            quality: 100,
            name: fileName);

        if (result['isSuccess']) {
          if (context.mounted) {
            FlushBarToast.fLSnackBar(
                message: "Receipt saved to photos",
                snackBarType: SnackBarType.success);
          }
          printty('Receipt saved to photos', level: "success");
        } else {
          throw Exception('Failed to save receipt: ${result['error']}');
        }
      }
    } catch (e) {
      // Dismiss loading dialog if open
      if (context.mounted && Navigator.canPop(context)) {
        Navigator.pop(context);
      }

      printty(e, level: "Download error");
      _showErrorSnackBar("Failed to save receipt: ${e.toString()}");
    }
  }

  // Future<void> _downloadReceiptToDevice() async {
  //   try {
  //     printty('downloading receipt to device');

  //     // For Android 10+ (API 29+), we need to adapt our approach
  //     if (Platform.isAndroid) {
  //       final androidInfo = await DeviceInfoPlugin().androidInfo;
  //       final sdkInt = androidInfo.version.sdkInt;

  //       if (sdkInt >= 30) {
  //         // Android 11+
  //         // Check for manage external storage permission (needed for Android 11+)
  //         var status = await Permission.manageExternalStorage.status;
  //         if (!status.isGranted) {
  //           status = await Permission.manageExternalStorage.request();
  //           if (!status.isGranted) {
  //             throw Exception(
  //                 'Storage permission is required to download receipts');
  //           }
  //         }
  //       } else {
  //         // For Android 10 and below
  //         var status = await Permission.storage.status;
  //         if (!status.isGranted) {
  //           status = await Permission.storage.request();
  //           if (!status.isGranted) {
  //             throw Exception(
  //                 'Storage permission is required to download receipts');
  //           }
  //         }
  //       }
  //     }

  //     // Show loading indicator
  //     showDialog(
  //       context: context,
  //       barrierDismissible: false,
  //       builder: (BuildContext context) {
  //         return const AlertDialog(
  //           content: Row(
  //             children: [
  //               CircularProgressIndicator(),
  //               SizedBox(width: 20),
  //               Text("Saving receipt..."),
  //             ],
  //           ),
  //         );
  //       },
  //     );

  //     // Capture the screenshot
  //     final screenshotValue = await _captureScreenshot();
  //     if (screenshotValue == null) {
  //       throw Exception('Failed to capture screenshot');
  //     }

  //     // Generate unique filename
  //     final timestamp = DateTime.now().millisecondsSinceEpoch;
  //     final fileName = 'transaction_receipt_$timestamp.png';

  //     // Save the file
  //     final result = await ImageGallerySaver.saveImage(
  //         Uint8List.fromList(screenshotValue),
  //         quality: 100,
  //         name: fileName);

  //     // Dismiss loading dialog
  //     if (context.mounted) Navigator.pop(context);

  //     if (result['isSuccess']) {
  //       // Show success message
  //       if (context.mounted) {
  //         FlushBarToast.fLSnackBar(
  //             message: "Receipt saved to gallery",
  //             snackBarType: SnackBarType.success);
  //       }

  //       printty('Receipt saved to gallery', level: "success");
  //     } else {
  //       throw Exception('Failed to save to gallery: ${result['error']}');
  //     }
  //   } catch (e) {
  //     // Dismiss loading dialog if open
  //     if (context.mounted && Navigator.canPop(context)) {
  //       Navigator.pop(context);
  //     }

  //     printty(e, level: "Download error");
  //     _showErrorSnackBar("Failed to download receipt: ${e.toString()}");
  //   }
  // }

  Future<void> _shareReceipt() async {
    try {
      printty('share receipt');
      final screenshotValue = await _captureScreenshot();
      final directory = await getApplicationDocumentsDirectory();
      final imagePath = await _saveImageToFile(screenshotValue, directory);
      printty(imagePath?.path, level: "image path");
      printty(imagePath, level: "image");
      _showBottomSheet(imagePath!, directory);
    } catch (e) {
      printty(e, level: "Screenshot error");
      _showErrorSnackBar(e.toString());
    }
  }

  _captureScreenshot() async {}

  //TODO: Uncomment later incase its needed

  // Future<Uint8List?> _captureScreenshot() async {
  //   return screenshotController.captureFromLongWidget(
  //     InheritedTheme.captureAll(
  //       context,
  //       Material(
  //         child: TransactionReceipt(
  //           transactionArg: widget.transactionArg,
  //           sender: context.read<AuthUserVM>().user?.fullName ?? '',
  //         ),
  //       ),
  //     ),
  //     delay: const Duration(milliseconds: 100),
  //     context: context,
  //     constraints: BoxConstraints(
  //       maxWidth: Sizer.screenWidth,
  //       maxHeight: Sizer.screenHeight,
  //     ),
  //   );
  // }

  Future<File?> _saveImageToFile(Uint8List? value, Directory directory) async {
    if (value == null) throw ArgumentError('Image data cannot be null.');
    // Generate a unique filename
    final now = DateTime.now();
    final uniqueFileName =
        'transaction_receipt-${now.millisecondsSinceEpoch}.png';

    // Create the file path directly (no nested folders)
    final file = File('${directory.path}/$uniqueFileName');

    try {
      // Create parent directories if needed
      if (!await directory.exists()) {
        await directory.create(recursive: true);
      }

      // Write the image bytes to the file
      await file.writeAsBytes(value);
      return file;
    } catch (e) {
      printty("Failed to save image: $e", level: "error");
      return null;
    }
  }

  void _showBottomSheet(File imagePath, Directory directory) {
    BsWrapper.bottomSheet(
      context: context,
      widget: TransactionReceiptSheet(
        imageScreenshot: imagePath,
        directoryPath: directory.path,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    FlushBarToast.fLSnackBar(
      message: message,
    );
  }

  _getSvg() {
    var category = widget.transactionArg.transaction.category;
    var type = widget.transactionArg.transaction.type;
    switch (type) {
      case "credit":
        if (category == "p2p" || category == "conversion") {
          return AppSvgs.arrowSwap;
        }
        return AppSvgs.received;
      case "debit":
        if (category == "p2p" || category == "conversion") {
          return AppSvgs.arrowSwap;
        }
        return AppSvgs.send;
      default:
        return AppSvgs.arrowSwap;
    }
  }

  _getTextColor(String status) {
    switch (status) {
      case "successful":
        return AppColors.iconGreen;
      case "pending":
        return AppColors.pending;
      default:
        return AppColors.failed;
    }
  }

  Future<bool> _requestPermissions() async {
    if (Platform.isAndroid) {
      final androidInfo = await DeviceInfoPlugin().androidInfo;
      final sdkInt = androidInfo.version.sdkInt;

      if (sdkInt >= 33) {
        // Android 13+
        final status = await Permission.photos.request();
        return status.isGranted;
      } else if (sdkInt >= 30) {
        // Android 11-12
        final status = await Permission.storage.request();
        return status.isGranted;
      } else {
        // Android 10 and below
        final status = await Permission.storage.request();
        return status.isGranted;
      }
    } else if (Platform.isIOS) {
      final status = await Permission.photos.request();
      return status.isGranted;
    }

    return false;
  }
}
