import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/referrals/invite_others_and_earn_screen.dart';
import 'package:korrency/ui/screens/screens.dart';

class DashboardArg {
  final int index;
  DashboardArg({this.index = 0});
}

class DashboardNav extends StatefulWidget {
  const DashboardNav({
    super.key,
    this.index,
  });

  final int? index;

  @override
  State<DashboardNav> createState() => _DashboardNavState();
}

class _DashboardNavState extends State<DashboardNav> {
  List screens = [
    const HomeScreen(),
    const TransactionsScreen(),
    const ExchangeScreen(),
    // const NavigateP2pMarketplace(),
    const InviteOthersAndEarnScreen(),
    const MenuScreen(),
    const YourOfferScreen(),
  ];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.index != null) {
        context.read<DashboardVM>().changeScreenIndex(widget.index ?? 0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Consumer<DashboardVM>(builder: (context, vm, _) {
        return Scaffold(
          body: screens[vm.currentIndex],
          backgroundColor: AppColors.bgWhite,
          bottomNavigationBar: Container(
            height: Sizer.height(84),
            padding: EdgeInsets.only(
              bottom: Sizer.height(10),
            ),
            decoration: BoxDecoration(
              color: AppColors.bgWhite,
              boxShadow: [
                BoxShadow(
                  color: Colors.black12.withOpacity(0.1),
                  blurRadius: 10,
                  spreadRadius: 0,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Consumer<AuthUserVM>(builder: (context, authVM, _) {
              return Padding(
                padding: EdgeInsets.symmetric(
                  horizontal: Sizer.width(20),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    BottomNavColumn(
                      icon: vm.currentIndex == 0 ? AppSvgs.homeB : AppSvgs.home,
                      labelText: 'Home',
                      fontWeight: vm.currentIndex == 0
                          ? FontWeight.w700
                          : FontWeight.normal,
                      onPressed: () {
                        vm.changeScreenIndex(0);
                      },
                    ),
                    BottomNavColumn(
                      icon: vm.currentIndex == 1
                          ? AppSvgs.receiptB
                          : AppSvgs.receipt,
                      labelText: 'Transactions',
                      fontWeight: vm.currentIndex == 1
                          ? FontWeight.w700
                          : FontWeight.normal,
                      onPressed: () {
                        vm.changeScreenIndex(1);
                      },
                    ),
                    // BottomNavColumn(
                    //   icon: vm.currentIndex == 2
                    //       ? AppSvgs.shareB
                    //       : AppSvgs.exchange,
                    //   labelText: 'Exchange',
                    //   fontWeight: vm.currentIndex == 2
                    //       ? FontWeight.w700
                    //       : FontWeight.normal,
                    //   onPressed: () {
                    //     if (authVM.statusProcessing) {
                    //       FlushBarToast.fLSnackBar(
                    //         message: 'Your ID verification is in progress',
                    //         snackBarType: SnackBarType.success,
                    //       );
                    //       return;
                    //     }
                    //     vm.changeScreenIndex(2);
                    //   },
                    // ),
                    // BottomNavColumn(
                    //   icon: vm.currentIndex == 3 ? AppSvgs.shopB : AppSvgs.shop,
                    //   labelText: 'Marketplace',
                    //   fontWeight: vm.currentIndex == 3
                    //       ? FontWeight.w700
                    //       : FontWeight.normal,
                    //   onPressed: () {
                    //     FlushBarToast.fLSnackBar(
                    //       message: 'Marketplace is coming soon',
                    //       snackBarType: SnackBarType.success,
                    //     );
                    //     // if (!authVM.userIsVerified) {
                    //     //   return BsWrapper.bottomSheet(
                    //     //     canDismiss: false,
                    //     //     context: context,
                    //     //     widget: const CompleteKycScreen(),
                    //     //   );
                    //     // }
                    //     // vm.changeScreenIndex(3);
                    //   },
                    // ),
                    BottomNavColumn(
                      icon: vm.currentIndex == 3
                          ? AppSvgs.referral
                          : AppSvgs.referral,
                      labelText: 'Referrals',
                      fontWeight: vm.currentIndex == 3
                          ? FontWeight.w700
                          : FontWeight.normal,
                      onPressed: () {
                        vm.changeScreenIndex(3);
                      },
                    ),
                    BottomNavColumn(
                      icon: vm.currentIndex == 4 ? AppSvgs.menuB : AppSvgs.menu,
                      labelText: 'Menu',
                      fontWeight: vm.currentIndex == 4
                          ? FontWeight.w700
                          : FontWeight.normal,
                      onPressed: () {
                        vm.changeScreenIndex(4);
                      },
                    ),
                  ],
                ),
              );
            }),
          ),
        );
      }),
    );
  }
}
