import 'package:dotted_border/dotted_border.dart';
import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class P2pTransferArg {
  final Offers offer;
  P2pTransferArg({
    required this.offer,
  });
}

class P2PTransferScreen extends StatefulWidget {
  const P2PTransferScreen({
    Key? key,
    required this.arg,
  }) : super(key: key);

  final P2pTransferArg arg;

  @override
  State<P2PTransferScreen> createState() => _P2PTransferScreenState();
}

class _P2PTransferScreenState extends State<P2PTransferScreen> {
  final FocusNode _amtFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _amtFocusNode.requestFocus();
    printty(widget.arg.offer, level: 'P2PTransferScreen');
  }

  @override
  void dispose() {
    _amtFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var walletVM = context.read<WalletVM>();
    var walletType =
        walletVM.getWalletType(widget.arg.offer.type?.toLowerCase() ?? "");
    return Consumer<MarketPlaceOfferVM>(
      builder: (context, vm, _) {
        var offer = widget.arg.offer;
        printty(offer, level: 'P2PTransferScreen');
        return BusyOverlay(
          child: Scaffold(
            body: SafeArea(
              bottom: false,
              child: SingleChildScrollView(
                child: Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const YBox(20),
                      const CustomHeader(
                        // showBackBtn: true,
                        showHeader: true,
                        headerText: 'P2P Transfer',
                      ),
                      const YBox(26),
                      DottedBorder(
                        dashPattern: const [8, 5],
                        strokeWidth: 2,
                        borderType: BorderType.RRect,
                        radius: const Radius.circular(4),
                        color: AppColors.dottedColor,
                        padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(16),
                            vertical: Sizer.height(10)),
                        child: SizedBox(
                          width: Sizer.screenWidth,
                          child: Row(
                            children: [
                              Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Enter amount',
                                    style: AppTypography.text12.copyWith(
                                      color: AppColors.textGray,
                                    ),
                                  ),
                                  const YBox(4),
                                  Container(
                                    height: Sizer.height(30),
                                    color: AppColors.white,
                                    width: Sizer.width(150),
                                    child: ConvertTextfield(
                                      hintText: "10,000.00",
                                      focusNode: _amtFocusNode,
                                      textAlign: TextAlign.start,
                                      inputFormatters: null,
                                      controller: vm.amtC,
                                      onChanged: (val) {
                                        var replace = val.replaceAll(',', '');
                                        vm.offerAmountForExchange(
                                          replace,
                                          offer,
                                          walletType?.balance ?? "0",
                                        );
                                      },
                                    ),
                                  ),
                                ],
                              ),
                              const Spacer(),
                              const CurrencyCards(
                                showArrowIcon: false,
                                bgColor: AppColors.transparent,
                                flagIconPath: AppImages.cad,
                              )
                            ],
                          ),
                        ),
                      ),
                      const YBox(8),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: (offer.allowsPartialTrade ?? false)
                            ? MainAxisAlignment.spaceBetween
                            : MainAxisAlignment.end,
                        children: [
                          if (offer.allowsPartialTrade ?? false)
                            Text(
                              'Minimum Amount = ${AppUtils.formatAmountDoubleString(offer.minimumPartialTradeAmount ?? "0")} ${offer.tradingCurrency?.code}',
                              style: AppTypography.text12.copyWith(
                                color: AppColors.textBlack2000,
                              ),
                            ),
                          IconBtn(
                            onTap: () {},
                            icon: Icons.add,
                            btnText: "${offer.acceptingTradeText} All",
                          )
                        ],
                      ),
                      const YBox(40),
                      ContainerWithBluewishBg(
                        bgColor: AppColors.blue300,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Amount you need for this exchange',
                              style: AppTypography.text12.copyWith(
                                color: AppColors.textGray,
                              ),
                            ),
                            const YBox(4),
                            if (offer.type?.toLowerCase() ==
                                OfferConst.sellOffer)
                              _amtForExcahngeText(vm.amountForThisExchange,
                                  offer.askingCurrency?.code ?? "",
                                  isGreaterThanBalance:
                                      vm.exchangeAmtGreaterThanWalletBalance),
                            if (offer.type?.toLowerCase() ==
                                OfferConst.buyOffer)
                              _amtForExcahngeText(vm.amountForThisExchange,
                                  offer.tradingCurrency?.code ?? "",
                                  isGreaterThanBalance:
                                      vm.exchangeAmtGreaterThanWalletBalance),
                          ],
                        ),
                      ),
                      const YBox(4),
                      Builder(builder: (context) {
                        return _walletBalText(walletType?.balance ?? "0",
                            walletType?.currency?.code ?? "");
                      }),
                      const YBox(165),
                      SizedBox(
                        height: Sizer.height(150),
                        // color: AppColors.red,
                        child: Stack(
                          children: [
                            Positioned(
                              top: 30,
                              right: 0,
                              left: 0,
                              child: Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(10),
                                  vertical: Sizer.height(18),
                                ),
                                decoration: BoxDecoration(
                                  color: AppColors.opacityGreen200,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    RowText(
                                      leftText: 'Buyer’s rate',
                                      rightText: offer.rate ?? '',
                                    ),
                                    const YBox(16),
                                    // if (offer.type?.toLowerCase() ==
                                    //     "sell offer")
                                    RowText(
                                      leftText: 'Fees',
                                      rightText: vm.fees,
                                    ),
                                    // if (offer.type?.toLowerCase() ==
                                    //     "buy offer")
                                    //   RowText(
                                    //     leftText: 'Fees',
                                    //     rightText:
                                    //         '${offer.tradingFees ?? ''} ${offer.tradingCurrency?.code}',
                                    //   ),
                                    const YBox(16),
                                    const RowText(
                                      isBold: true,
                                      leftText: 'Estimated Delivery',
                                      rightText: 'Instant',
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Positioned(
                              right: Sizer.screenWidth / 2 - Sizer.width(50),
                              top: 5,
                              child: Container(
                                padding: EdgeInsets.all(Sizer.radius(10)),
                                decoration: BoxDecoration(
                                  color: AppColors.blue100,
                                  borderRadius: BorderRadius.circular(20),
                                ),
                                child: const Icon(
                                  Iconsax.information5,
                                  color: AppColors.blue800,
                                  size: 26,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const YBox(27),
                      CustomBtn.solid(
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.p2pReviewScreen);
                        },
                        online: vm.isBtnActive,
                        text: "Continue",
                      ),
                      const YBox(150),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Text _amtForExcahngeText(String amount, String currencyCode,
      {bool isGreaterThanBalance = false}) {
    return Text(
      '$amount $currencyCode',
      style: AppTypography.text20.copyWith(
        fontWeight: FontWeight.w700,
        color: isGreaterThanBalance ? AppColors.red : AppColors.baseBlack,
      ),
    );
  }

  Row _walletBalText(String text, String currencyCode) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        Text(
          'Wallet Balance = ${AppUtils.formatAmountDoubleString(text)} $currencyCode',
          style: AppTypography.text12.copyWith(
            color: AppColors.textBlack2000,
          ),
        ),
      ],
    );
  }
}
