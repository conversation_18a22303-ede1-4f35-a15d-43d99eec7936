import 'package:dotted_border/dotted_border.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyReviewScreen extends StatefulWidget {
  const SendMoneyReviewScreen({
    super.key,
    this.isKorrencyUser = false,
  });

  final bool isKorrencyUser;

  @override
  State<SendMoneyReviewScreen> createState() => _SendMoneyReviewScreenState();
}

class _SendMoneyReviewScreenState extends State<SendMoneyReviewScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      // printty('SendMoneyReviewScreen Vm fromC ${vm.fromC.text}');
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const YBox(20),
                    const CustomHeader(
                      // showBackBtn: true,
                      showHeader: true,
                      headerText: 'Review',
                    ),
                    const YBox(26),
                    ContainerWithBluewishBg(
                      padding: EdgeInsets.symmetric(
                        vertical: Sizer.height(20),
                      ).copyWith(
                        left: Sizer.width(30),
                        right: Sizer.width(19),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          ColumnText(
                            showImage: true,
                            isSvgNetwork:
                                vm.fromConvertWallet?.currency?.flag != null,
                            text: 'You Send',
                            amount: vm.reviewAmtYouSend,
                            image: vm.fromConvertWallet?.currency?.flag ??
                                AppImages.ngn,
                          ),
                          Text(
                            "=>",
                            style: AppTypography.text28.copyWith(
                              color: AppColors.blue800,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          ColumnText(
                            showImage: true,
                            isSvgNetwork:
                                vm.fromConvertWallet?.currency?.flag != null,
                            text: 'Recipient Gets',
                            amount: AppUtils.formatAmountDoubleString(
                              vm.recipientC.text.trim().replaceAll(",", ""),
                            ),
                            image: vm.recipientCurrency?.flag ?? AppImages.ngn,
                          ),
                        ],
                      ),
                    ),
                    const YBox(24),
                    DottedBorder(
                      dashPattern: const [8, 5],
                      strokeWidth: 2,
                      borderType: BorderType.RRect,
                      radius: const Radius.circular(4),
                      color: AppColors.dottedColor,
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(16),
                        vertical: Sizer.height(26),
                      ),
                      child: SizedBox(
                        width: Sizer.screenWidth,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CustomListViews.currencyListText(
                              margin: EdgeInsets.zero,
                              leftText: 'Our Fee',
                              rightText: vm.transferFee,
                              rightFontWeight: FontWeight.w600,
                            ),
                            const YBox(30),
                            CustomListViews.currencyListText(
                              margin: EdgeInsets.zero,
                              leftText: 'Exchange Rate',
                              rightText: vm.rateFormat,
                              rightFontWeight: FontWeight.w600,
                            ),
                            const YBox(30),
                            CustomListViews.currencyListText(
                              margin: EdgeInsets.zero,
                              leftText: 'Delivery Method',
                              rightText: vm.transferMethod?.text ?? '',
                              rightFontWeight: FontWeight.w600,
                            ),
                            const YBox(30),
                            if (vm.transferMethod ==
                                TransferMethodType.korrency)
                              CustomListViews.currencyListText(
                                margin: EdgeInsets.zero,
                                leftText: 'Username',
                                rightText: (vm.accountName ?? ''),
                                rightTextColor: AppColors.textGray,
                              ),
                            if (vm.transferMethod ==
                                TransferMethodType.bankTransfer)
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CustomListViews.currencyListText(
                                    margin: EdgeInsets.zero,
                                    leftText: 'Beneficiary Name',
                                    rightText:
                                        (vm.accountName ?? '').toUpperCase(),
                                    rightTextColor: AppColors.textGray,
                                  ),
                                  const YBox(30),
                                  CustomListViews.currencyListText(
                                    margin: EdgeInsets.zero,
                                    leftText: 'Account Number',
                                    rightText: vm.accountNumC.text.trim(),
                                    rightFontWeight: FontWeight.w600,
                                  ),
                                  const YBox(30),
                                  CustomListViews.currencyListText(
                                    margin: EdgeInsets.zero,
                                    leftText: 'Bank Name',
                                    rightText: vm.bankNameC.text,
                                    rightFontWeight: FontWeight.w600,
                                  ),
                                ],
                              ),
                            if (vm.transferMethod == TransferMethodType.interac)
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CustomListViews.currencyListText(
                                    margin: EdgeInsets.zero,
                                    leftText: 'Recipient\'s Name',
                                    rightText:
                                        '${vm.interacFNameC.text} ${vm.interacLNameC.text}',
                                    rightTextColor: AppColors.textGray,
                                  ),
                                  const YBox(30),
                                  CustomListViews.currencyListText(
                                    margin: EdgeInsets.zero,
                                    leftText: 'Interac Email',
                                    rightText: vm.interacEmailC.text,
                                    rightFontWeight: FontWeight.w600,
                                  ),
                                ],
                              ),
                            if (vm.transferMethod ==
                                TransferMethodType.mobileMoney)
                              Column(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  CustomListViews.currencyListText(
                                    margin: EdgeInsets.zero,
                                    leftText: 'Mobile Network',
                                    rightText: vm.mobileMoney?.name ?? '',
                                    rightTextColor: AppColors.textGray,
                                  ),
                                  const YBox(30),
                                  CustomListViews.currencyListText(
                                    margin: EdgeInsets.zero,
                                    leftText: 'Mobile Number',
                                    rightText:
                                        vm.mobileMoney?.mobileNumber ?? '',
                                    rightFontWeight: FontWeight.w600,
                                  ),
                                  const YBox(30),
                                  CustomListViews.currencyListText(
                                    margin: EdgeInsets.zero,
                                    leftText: 'Recipient\'s Name',
                                    rightText:
                                        vm.mobileMoney?.recipientName ?? '',
                                    rightFontWeight: FontWeight.w600,
                                  ),
                                ],
                              ),
                            const YBox(16),
                            Text(
                              '- - - - - - - - - - - - - - - - - - - - - - - - - - - - ',
                              style: AppTypography.text14.copyWith(
                                color: AppColors.textGray,
                              ),
                            ),
                            const YBox(8),
                            CustomListViews.currencyListText(
                              margin: EdgeInsets.zero,
                              leftText: 'Total Amount',
                              rightText: vm.total,
                              rightTextColor: AppColors.iconGreen,
                              rightFontWeight: FontWeight.w600,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const YBox(120),
                    CustomBtn.solid(
                      onTap: () {
                        // BsWrapper.bottomSheet(
                        //   context: context,
                        //   widget: SendMoneyPinSheet(
                        //     onTransfer: (pin) => _transfer(pin),
                        //   ),
                        // );
                        Navigator.pushNamed(
                          context,
                          RoutePath.sendMoneyAuthorizeScreen,
                        );
                      },
                      online: true,
                      text: "Continue",
                    ),
                    const YBox(50),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }

  // _gotoTransactionInProcess() {
  //   Navigator.pushReplacementNamed(
  //     context,
  //     RoutePath.transactionStatusScreen,
  //     arguments: ConfirmationArg(
  //       title: "Transaction in Progress",
  //       imagePath: AppImages.transaction,
  //       imageHeight: 270,
  //       imageWidth: 270,
  //       subtitle: const ConfirmationSubtitleText(
  //         startText: "The recipient will",
  //         endText: " receive the money in a few minutes",
  //       ),
  //       buttonText: 'Send more money',
  //       onBtnTap: () {
  //         _pop();
  //         _pop();
  //         _pop();
  //         _pop();
  //         Navigator.pushNamed(
  //           NavigatorKeys.appNavigatorKey.currentContext!,
  //           RoutePath.sendMoneyScreen,
  //         );
  //       },
  //       outlineBtnText: "Home",
  //       outlineBtn: () {
  //         Navigator.pushNamedAndRemoveUntil(
  //           NavigatorKeys.appNavigatorKey.currentContext!,
  //           RoutePath.dashboardNav,
  //           (route) => false,
  //         );
  //       },
  //     ),
  //   );
  // }
}
