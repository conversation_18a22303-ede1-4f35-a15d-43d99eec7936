import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/helpsupport/create_freshdesk_ticket_webview.dart';

class InteracTransferScreenOLD extends StatelessWidget {
  const InteracTransferScreenOLD({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.bgWhite,
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: ListView(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                const CustomHeader(
                  showHeader: true,
                  headerText: 'Interac e-Transfer',
                ),
                const <PERSON><PERSON><PERSON>(40),
                image<PERSON>elper(
                  AppImages.interac,
                  height: Sizer.height(150),
                ),
                const YBox(30),
                Container(
                  alignment: Alignment.centerLeft,
                  child: Text(
                    "e-Transfer Steps",
                    style: AppTypography.text16.copyWith(
                      fontWeight: FontWeight.w700,
                    ),
                  ),
                ),
                const YBox(23),
                const InteracTimeline(
                  textOne:
                      "Sign in to your financial institution’s online banking service",
                  step: "1",
                ),
                const YBox(5),
                const InteracTimeline(
                  textOne:
                      "Navigate to the option to send money using Interac e-Transfer",
                  step: "2",
                ),
                const YBox(5),
                Consumer<ConfigVM>(builder: (context, configVM, _) {
                  return InteracTimeline(
                    textOne: "Send an e-Transfer to",
                    textBoldTwo: configVM.interacDepositEmail,
                    textItalicThree: "(It is setup with auto-deposit).",
                    onTapTextBoldTwo: () {
                      Clipboard.setData(
                        ClipboardData(
                          text: configVM.interacDepositEmail ?? '',
                        ),
                      );
                      FlushBarToast.fLSnackBar(
                        message: "${configVM.interacDepositEmail} copied",
                        snackBarType: SnackBarType.success,
                      );
                    },
                    step: "3",
                  );
                }),
                const YBox(5),
                InteracTimeline(
                  textOne: "Ensure you send with your Korrency Interac email ",
                  textBoldTwo: vm.userInteracMail,
                  textBoldColor: AppColors.textBlack800,
                  step: "4",
                ),
                const YBox(5),
                const InteracTimeline(
                  step: "5",
                  isEnd: true,
                  textOne: "Your account will be credited within minutes.",
                ),
                const YBox(30),
                RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(
                        text: "Note: If",
                        style: AppTypography.text13.copyWith(
                          color: AppColors.gray500,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          height: 1.2,
                        ),
                      ),
                      TextSpan(
                        text: " ${vm.userInteracMail} ",
                        style: AppTypography.text13.copyWith(
                          color: AppColors.gray500,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w700,
                          height: 1.2,
                        ),
                      ),
                      TextSpan(
                        text: "is not your interac email,",
                        style: AppTypography.text13.copyWith(
                          color: AppColors.gray500,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          height: 1.2,
                        ),
                      ),
                      TextSpan(
                        text:
                            " click here to update with your preferred Interac email",
                        style: AppTypography.text13.copyWith(
                          color: AppColors.lightBlue,
                          fontFamily: 'Inter',
                          fontWeight: FontWeight.w400,
                          height: 1.4,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            BsWrapper.bottomSheet(
                              context: context,
                              widget: const InteracTransferSheet(),
                            );

                            return;
                          },
                      ),
                    ],
                  ),
                ),
                const YBox(30),
                Consumer<ConfigVM>(builder: (context, configVM, _) {
                  return RichText(
                    textAlign: TextAlign.center,
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text:
                              "If your account has not been credited, kindly contact us at ",
                          style: AppTypography.text12.copyWith(
                            color: AppColors.gray500,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            height: 1.2,
                          ),
                        ),
                        TextSpan(
                          text: configVM.emailAddress,
                          style: AppTypography.text12.copyWith(
                            color: AppColors.lightBlue,
                            fontFamily: 'Inter',
                            fontWeight: FontWeight.w400,
                            height: 1.2,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              Navigator.pushNamed(
                                context,
                                RoutePath.createFreshDeskTicketWebview,
                                arguments: WebViewArg(
                                  webURL: AppUtils.korrencyCreateTicket,
                                ),
                              );
                            },
                        ),
                      ],
                    ),
                  );
                }),
                // const YBox(40)
              ],
            ),
          ),
        ),
      );
    });
  }
}

class InteracTimeline extends StatelessWidget {
  const InteracTimeline({
    Key? key,
    this.isEnd = false,
    required this.textOne,
    this.textBoldTwo,
    this.textBoldColor,
    this.textItalicThree,
    this.onTapTextBoldTwo,
    required this.step,
  }) : super(key: key);

  final bool isEnd;
  final String textOne;
  final String? textBoldTwo;
  final VoidCallback? onTapTextBoldTwo;
  final Color? textBoldColor;
  final String? textItalicThree;
  final String step;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: Sizer.width(25),
              height: Sizer.height(25),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
              ),
              child: Center(
                child: Text(
                  step,
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
            if (!isEnd) const YBox(5),
            if (!isEnd)
              Container(
                width: Sizer.width(2),
                height: Sizer.height(34),
                color: AppColors.primaryBlue,
              ),
          ],
        ),
        const XBox(9),
        Expanded(
            child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: textOne,
                style: AppTypography.text14.copyWith(
                  color: AppColors.textBlack800,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                ),
              ),
              if (textBoldTwo != null)
                TextSpan(
                  text: " $textBoldTwo",
                  style: AppTypography.text13.copyWith(
                    color: textBoldColor ?? AppColors.lightBlue,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'Inter',
                  ),
                  recognizer: TapGestureRecognizer()..onTap = onTapTextBoldTwo,
                ),
              if (textItalicThree != null)
                TextSpan(
                  text: " $textItalicThree",
                  style: AppTypography.text12.copyWith(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    height: 1.5,
                    fontStyle: FontStyle.italic,
                    color: AppColors.textBlack800,
                  ),
                ),
            ],
          ),
        )),
      ],
    );
  }
}
