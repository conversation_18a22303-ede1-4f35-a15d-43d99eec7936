import 'package:korrency/core/core.dart';

class NotificationCard extends StatelessWidget {
  const NotificationCard({
    super.key,
    required this.title,
    required this.subTitle,
    required this.date,
    this.icon,
    this.isRead = false,
    this.onTap,
  });

  final String title;
  final String subTitle;
  final String date;
  final String? icon;
  final bool isRead;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(12),
        ),
        child: Row(
          children: [
            Expanded(
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Skeleton.replace(
                    width: 35, // width of replacement
                    height: 31,
                    replacement: const Bone.circle(size: 35),
                    child: svgHelper(
                      icon ??
                          (isRead
                              ? AppSvgs.debitKorrency
                              : AppSvgs.creditKorrency),
                    ),
                  ),
                  // svgHelper(
                  //   isCredit ? AppSvgs.credit : AppSvgs.debit,
                  // ),
                  const XBox(8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: AppTypography.text16.copyWith(
                            fontWeight: FontWeight.w700,
                            color: isRead
                                ? AppColors.textGray500
                                : AppColors.blue600,
                          ),
                        ),
                        const YBox(4),
                        Text(
                          subTitle,
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          style: AppTypography.text14.copyWith(
                            color: AppColors.textGray,
                          ),
                        ),
                        const YBox(4),
                        Text(
                          date,
                          style: AppTypography.text14.copyWith(
                            color: AppColors.lightTextGray,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Iconsax.arrow_right_3,
                color: AppColors.lightTextGray,
                size: Sizer.radius(24),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// class NotificationCardLoadingShimmer extends StatelessWidget {
//   const NotificationCardLoadingShimmer({super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Row(
//       children: [
//         const CardLoading(
//           height: 30,
//           borderRadius: BorderRadius.all(Radius.circular(15)),
//           width: 30,
//           margin: EdgeInsets.only(bottom: 10),
//         ),
//         const XBox(8),
//         Expanded(
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               const CardLoading(
//                 height: 30,
//                 borderRadius: BorderRadius.all(Radius.circular(15)),
//                 width: 100,
//                 margin: EdgeInsets.only(bottom: 10),
//               ),
//               const YBox(4),
//               CardLoading(
//                 height: 30,
//                 borderRadius: const BorderRadius.all(Radius.circular(15)),
//                 width: Sizer.screenWidth,
//                 margin: const EdgeInsets.only(bottom: 10),
//               ),
//               const YBox(4),
//               CardLoading(
//                 height: 30,
//                 borderRadius: const BorderRadius.all(Radius.circular(15)),
//                 width: Sizer.screenWidth,
//                 margin: const EdgeInsets.only(bottom: 10),
//               ),
//             ],
//           ),
//         ),
//          CardLoading(
//                 height: 30,
//                 borderRadius: const BorderRadius.all(Radius.circular(15)),
//                 width: Sizer.screenWidth,
//                 margin: const EdgeInsets.only(bottom: 10),
//               ),
//       ],
//     );
//   }
// }
