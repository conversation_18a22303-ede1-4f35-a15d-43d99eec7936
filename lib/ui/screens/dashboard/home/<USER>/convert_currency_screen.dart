import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class ConvertCurrencyScreen extends StatefulWidget {
  const ConvertCurrencyScreen({super.key, this.convertArg});

  final ConvertArg? convertArg;

  @override
  State<ConvertCurrencyScreen> createState() => _ConvertCurrencyScreenState();
}

class _ConvertCurrencyScreenState extends State<ConvertCurrencyScreen> {
  final FocusNode _fromFocusNode = FocusNode();
  final FocusNode _toFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _fromFocusNode);
    _fromFocusNode.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
      _showNewCustomerOfferModal();
    });
  }

  _init() async {
    final walletVM = context.read<WalletVM>();
    final convertMoneyVM = context.read<ConvertMoneyVM>();
    final userVM = context.read<AuthUserVM>();
    final configVM = context.read<ConfigVM>();
    await convertMoneyVM.init(
      walletProvider: walletVM,
      userHasTransaction: userVM.user?.hasTransactions,
      freqDestinationCurrencyCode:
          userVM.user?.frequentDestinationCurrency?.code,
      newCustomersRateMinimumAmount: configVM.newCustomersRateMinimumAmount,

      // For coming from transaction details screen for convert again
      currencyCodesFromTransactionDetails: widget.convertArg,
    );

    // Pre-populate with ConvertArg data if provided
    if (widget.convertArg != null) {
      _prePopulateFromConvertArg(convertMoneyVM, walletVM);
    }

    convertMoneyVM.getConversionRate();
  }

  void _prePopulateFromConvertArg(
      ConvertMoneyVM convertMoneyVM, WalletVM walletVM) {
    final convertArg = widget.convertArg!;

    // Set amount if provided
    if (convertArg.fromAmount != null) {
      convertMoneyVM.fromC.text = convertArg.fromAmount!;
      convertMoneyVM
          .convertAmount(double.tryParse(convertArg.fromAmount!) ?? 0);
    }

    setState(() {});
  }

  _showNewCustomerOfferModal() {
    final userVM = context.read<AuthUserVM>();
    if (userVM.user?.hasTransactions == false) {
      return BsWrapper.bottomSheet(
        context: context,
        widget: const NewCustomerOfferModal(),
      );
    }
  }

  @override
  void dispose() {
    _fromFocusNode.dispose();
    _toFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var walletVM = context.read<WalletVM>();
    return Consumer<ConvertMoneyVM>(builder: (context, vm, _) {
      // printty(vm.fromConvertWallet, level: "convert from");
      // printty(vm.toConvertWallet, level: "convert to");

      return BusyOverlay(
        show: vm.busy(convertMoneyLoading),
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: SafeArea(
            bottom: false,
            child: Container(
              height: Sizer.screenHeight,
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const YBox(20),
                    ArrowBack(
                      onTap: () {
                        vm.resetData();
                        Navigator.pop(context);
                      },
                    ),
                    const YBox(30),
                    const AuthTextSubTitle(
                      title: "Convert Money",
                      subtitle: "Wallet to Wallet conversion",
                    ),
                    const YBox(25),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(14),
                      ),
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.grayE9),
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: SizedBox(
                        width: Sizer.screenWidth,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Align(
                              alignment: Alignment.center,
                              child: Container(
                                width: Sizer.width(202),
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(16),
                                  vertical: Sizer.height(4),
                                ),
                                decoration: BoxDecoration(
                                    color: vm.setRateBannerToGreen
                                        ? AppColors.transparent
                                        : AppColors.primaryBlue,
                                    image: vm.setRateBannerToGreen
                                        ? const DecorationImage(
                                            image: AssetImage(
                                              AppImages.smGradient,
                                            ),
                                            fit: BoxFit.cover,
                                          )
                                        : null,
                                    borderRadius: const BorderRadius.only(
                                      bottomLeft: Radius.circular(8),
                                      bottomRight: Radius.circular(8),
                                    )),
                                child: Center(
                                  child: Text(
                                    vm.rateFormat,
                                    style: AppTypography.text12.copyWith(
                                      color: AppColors.white,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const YBox(30),
                            Row(
                              children: [
                                Text(
                                  'You are converting',
                                  style: AppTypography.text11.copyWith(
                                    color: AppColors.textGray,
                                  ),
                                ),
                                const Spacer(),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      'Balance: ${AppUtils.formatAmountDoubleString(vm.fromConvertWallet?.balance ?? "0")}',
                                      style: AppTypography.text11.copyWith(
                                        color: AppColors.textGray,
                                      ),
                                    ),
                                    const XBox(4),
                                    InkWell(
                                      onTap: () => vm.addAllFromWalletBalance(),
                                      child: Container(
                                        padding: EdgeInsets.symmetric(
                                          horizontal: Sizer.width(6),
                                          vertical: Sizer.height(4),
                                        ),
                                        decoration: const BoxDecoration(
                                          color: AppColors.blueFF,
                                          borderRadius: BorderRadius.all(
                                            Radius.circular(4),
                                          ),
                                        ),
                                        child: Text(
                                          'Add All',
                                          style: AppTypography.text10.copyWith(
                                            color: AppColors.primaryBlue,
                                            fontWeight: FontWeight.w600,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            const YBox(8),
                            Row(
                              children: [
                                Container(
                                  height: Sizer.height(30),
                                  color: AppColors.white,
                                  width: Sizer.width(150),
                                  child: ConvertTextfield(
                                    hintText: "10,000.00",
                                    focusNode: _fromFocusNode,
                                    textAlign: TextAlign.left,
                                    color: vm.notEnoughBalanceFrom
                                        ? AppColors.red
                                        : AppColors.baseBlack,
                                    controller: vm.fromC,
                                    onChanged: (val) {
                                      //
                                      var replace = val.replaceAll(',', '');
                                      vm.convertAmount(
                                          double.tryParse(replace) ?? 0);
                                      // ..setFromAmount(replace);
                                    },
                                  ),
                                ),
                                const Spacer(),
                                _currencyFlag(
                                    iconPath: vm.fromConvertWallet?.currency
                                            ?.flag ??
                                        walletVM.activeWallet?.currency?.flag ??
                                        "",
                                    text:
                                        vm.fromConvertWallet?.currency?.code ??
                                            "",
                                    onTap: () {
                                      BsWrapper.bottomSheet(
                                        context: context,
                                        widget: const SelectWalletCurrencySheet(
                                          fromConvert: true,
                                        ),
                                      );
                                    }),
                              ],
                            ),
                            const YBox(20),
                            Row(
                              children: [
                                const Expanded(
                                  child: Divider(
                                    color: AppColors.primaryLightBlue,
                                  ),
                                ),
                                Center(
                                  child: InkWell(
                                    onTap: () {
                                      vm.swapWallets();
                                    },
                                    child: Container(
                                      padding: EdgeInsets.all(Sizer.radius(8)),
                                      decoration: BoxDecoration(
                                        color: AppColors.blue100,
                                        borderRadius: BorderRadius.circular(30),
                                      ),
                                      // alignment: Alignment.center,
                                      child: const Icon(Iconsax.arrow_swap),
                                    ),
                                  ),
                                ),
                                const Expanded(
                                  child: Divider(
                                    color: AppColors.primaryLightBlue,
                                  ),
                                ),
                              ],
                            ),
                            const YBox(20),
                            Column(
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      'To',
                                      style: AppTypography.text12.copyWith(
                                        color: AppColors.textGray,
                                      ),
                                    ),
                                    const Spacer(),
                                    Text(
                                      'Balance: ${AppUtils.formatAmountDoubleString(vm.toConvertWallet?.balance ?? "0")}',
                                      style: AppTypography.text12.copyWith(
                                        color: AppColors.textGray,
                                      ),
                                    ),
                                  ],
                                ),
                                const YBox(16),
                                Row(
                                  children: [
                                    SizedBox(
                                      height: Sizer.height(30),
                                      // color: AppColors.red,
                                      width: Sizer.width(130),
                                      child: ConvertTextfield(
                                        hintText: "10,000.00",
                                        focusNode: _toFocusNode,
                                        textAlign: TextAlign.left,
                                        controller: vm.toC,
                                        onChanged: (val) {
                                          var replace = val.replaceAll(',', '');
                                          vm.convertAmountTo(
                                              double.tryParse(replace) ?? 0);
                                        },
                                      ),
                                    ),
                                    const Spacer(),
                                    _currencyFlag(
                                      iconPath:
                                          vm.toConvertWallet?.currency?.flag ??
                                              walletVM.activeWallet?.currency
                                                  ?.flag ??
                                              "",
                                      text:
                                          vm.toConvertWallet?.currency?.code ??
                                              "",
                                      onTap: () {
                                        BsWrapper.bottomSheet(
                                          context: context,
                                          widget:
                                              const SelectWalletCurrencySheet(),
                                        );
                                      },
                                    ),
                                  ],
                                ),
                                const YBox(20),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    const YBox(32),
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(12),
                        vertical: Sizer.height(8),
                      ),
                      decoration: BoxDecoration(
                        color: AppColors.blueFA,
                        border: Border.all(
                          color: AppColors.grayE9,
                        ),
                        borderRadius: BorderRadius.circular(Sizer.radius(4)),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Iconsax.information5,
                            size: Sizer.radius(24),
                            color: AppColors.primaryBlue,
                          ),
                          const XBox(8),
                          RichText(
                            text: TextSpan(
                              text: 'Delivered ',
                              style: AppTypography.text12.copyWith(
                                color: AppColors.textGray,
                              ),
                              children: [
                                TextSpan(
                                  text: 'Instantly',
                                  style: AppTypography.text12.copyWith(
                                    color: AppColors.primaryBlue,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ],
                            ),
                          )
                        ],
                      ),
                    ),
                    const YBox(170),
                    CustomBtn.solid(
                      onTap: () {
                        BsWrapper.bottomSheet(
                          context: context,
                          widget: ReviewTransactionSheet(
                            onConvert: () {
                              Navigator.pop(context);
                              _convert();
                            },
                          ),
                        );
                      },
                      online: vm.convertBtnEnabled,
                      text: "Convert",
                    ),
                    const YBox(50),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _currencyFlag({
    required String text,
    required String iconPath,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.network(
            iconPath,
            height: Sizer.height(16),
            width: Sizer.width(16),
          ),
          const XBox(3),
          Text(
            text,
            style: AppTypography.text12.copyWith(
                color: AppColors.primaryBlue, fontWeight: FontWeight.w500),
          ),
          const Icon(
            Icons.expand_more,
            color: AppColors.primaryBlue,
            size: 25,
          ),
        ],
      ),
    );
  }

  _convert() {
    var convertMoneyVM = context.read<ConvertMoneyVM>();
    convertMoneyVM.convertMoney().then((value) {
      if (value.success) {
        convertMoneyVM.resetData();
        _showComfirmationScreen(msg: value.message.toString());
        _getWalletCredential();
      } else {
        convertMoneyVM.resetData();
        _showComfirmationScreen(msg: value.message.toString(), isFailed: true);
      }
    });
  }

  _showComfirmationScreen({String? msg, bool isFailed = false}) {
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ??
            (isFailed ? 'Failed To Convert' : 'Money Converted\n Successfully'),
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
        },
      ),
    );
  }

  _getWalletCredential() {
    context.read<WalletVM>().getWallets();
    context.read<AuthUserVM>().getAuthUser();
    context.read<CurrencyVM>().getCurrencies();
    context.read<TransactionVM>().getTransactions();
  }
}
