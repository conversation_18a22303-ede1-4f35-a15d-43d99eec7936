import 'package:flutter_svg/flutter_svg.dart';
import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/home/<USER>/send_money_screen.dart';

enum CurrencyType { all, selected }

class ExchangeRateScreen extends StatefulWidget {
  const ExchangeRateScreen({Key? key}) : super(key: key);

  @override
  State<ExchangeRateScreen> createState() => _ExchangeRateScreenState();
}

class _ExchangeRateScreenState extends State<ExchangeRateScreen> {
  int _selectedIndex = -1;
  CurrencyType _currencyType = CurrencyType.all;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CurrencyVM>().getCurrenciesRates();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CurrencyVM>(builder: (context, vm, _) {
      printty('currenciesRates ${vm.isCreatableCurrenciesRates.length}');
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const YBox(20),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  alignment: Alignment.centerLeft,
                  child: const ArrowBack(),
                ),
                const YBox(30),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: const AuthTextSubTitle(
                    title: "Exchange Rates",
                    subtitle: "View all current exchange rates",
                  ),
                ),
                const YBox(24),
                SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  child: Container(
                    padding: EdgeInsets.only(
                      left: Sizer.width(24),
                    ),
                    child: Row(
                      children: [
                        TextWithFlagBtn(
                          text: "All",
                          horizontalPadding: 24,
                          isSelected: _selectedIndex == -1,
                          onNavigate: () {
                            setState(() {
                              _selectedIndex = -1;
                              _currencyType = CurrencyType.all;
                            });
                          },
                        ),
                        ...List.generate(vm.isCreatableCurrenciesRates.length,
                            (index) {
                          var currencyRate =
                              vm.isCreatableCurrenciesRates[index];
                          return TextWithFlagBtn(
                            text: currencyRate.code ?? '',
                            imgPath: currencyRate.flag ?? '',
                            isSelected: _selectedIndex == index,
                            onNavigate: () {
                              setState(() {
                                _selectedIndex = index;
                                _currencyType = CurrencyType.selected;
                              });
                              vm.setSelectedCurrencyRate(
                                  currencyRate.code ?? '');
                            },
                          );
                        }),
                      ],
                    ),
                  ),
                ),
                const YBox(30),
                if (_currencyType == CurrencyType.all)
                  Expanded(
                    child: ListView.separated(
                      shrinkWrap: true,
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(24),
                      ),
                      itemBuilder: (ctx, i) {
                        var currencyRate = vm.isCreatableCurrenciesRates[i];
                        return Column(
                          children: List.generate(
                            currencyRate.rates.length,
                            (i) {
                              return Container(
                                padding: EdgeInsets.only(
                                  bottom: Sizer.height(30),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Row(
                                        children: [
                                          _currencyText(
                                            imgPath: currencyRate.flag,
                                            text: currencyRate.code,
                                          ),
                                          const XBox(2),
                                          const Icon(Iconsax.arrow_right_1),
                                          const XBox(4),
                                          _currencyText(
                                            imgPath: currencyRate.rates[i].flag,
                                            text: currencyRate.rates[i].to,
                                          ),
                                        ],
                                      ),
                                    ),
                                    const XBox(20),
                                    Expanded(
                                      child: actionText(
                                        amount:
                                            AppUtils.formatAmountDoubleString(
                                                currencyRate.rates[i].rate ??
                                                    "0"),
                                        onNavigate: () {
                                          Navigator.pushNamed(
                                            context,
                                            RoutePath.sendMoneyScreen,
                                            arguments: SendMoneyArg(
                                              fromCurrencyCode:
                                                  currencyRate.code ?? '',
                                              toCurrencyCode:
                                                  currencyRate.rates[i].to ??
                                                      '',
                                            ),
                                          );
                                        },
                                      ),
                                    ),
                                  ],
                                ),
                              );
                            },
                          ),
                        );
                      },
                      separatorBuilder: (ctx, _) => const SizedBox.shrink(),
                      itemCount: vm.isCreatableCurrenciesRates.length,
                    ),
                  ),
                if (_currencyType == CurrencyType.selected &&
                    vm.selectedCurrencyRate != null)
                  Expanded(
                    child: ListView.separated(
                      shrinkWrap: true,
                      padding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(24),
                      ),
                      itemBuilder: (ctx, i) {
                        var selectedRate = vm.selectedCurrencyRate;
                        return Row(
                          children: [
                            Expanded(
                              child: Row(
                                children: [
                                  _currencyText(
                                    imgPath: selectedRate?.flag,
                                    text: selectedRate?.code,
                                  ),
                                  const XBox(2),
                                  const Icon(Iconsax.arrow_right_1),
                                  const XBox(4),
                                  _currencyText(
                                    imgPath: selectedRate?.rates[i].flag,
                                    text: selectedRate?.rates[i].to,
                                  ),
                                ],
                              ),
                            ),
                            const XBox(20),
                            Expanded(
                              child: actionText(
                                amount: AppUtils.formatAmountDoubleString(
                                    selectedRate?.rates[i].rate ?? '0'),
                                onNavigate: () {
                                  Navigator.pushNamed(
                                    context,
                                    RoutePath.sendMoneyScreen,
                                    arguments: SendMoneyArg(
                                      fromCurrencyCode:
                                          selectedRate?.code ?? '',
                                      toCurrencyCode:
                                          selectedRate?.rates[i].to ?? '',
                                    ),
                                  );
                                },
                              ),
                            ),
                          ],
                        );
                      },
                      separatorBuilder: (ctx, _) => const YBox(30),
                      itemCount: vm.selectedCurrencyRate?.rates.length ?? 0,
                    ),
                  ),
              ],
            ),
          ),
        ),
      );
    });
  }

  Row actionText({
    VoidCallback? onNavigate,
    required String amount,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          amount,
          style: AppTypography.text16.copyWith(
            color: AppColors.blue800,
            fontWeight: FontWeight.w700,
          ),
        ),
        TextWithFlagBtn(
          text: 'Send',
          verticalPadding: 8,
          horizontalPadding: 16,
          borderRadius: 30,
          addMargin: false,
          isSelected: true,
          onNavigate: onNavigate,
        ),
      ],
    );
  }

  Row _currencyText({
    String? text,
    String? imgPath,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SvgPicture.network(
          imgPath ?? '',
          height: Sizer.height(14),
          width: Sizer.width(20),
        ),
        const XBox(8),
        Text(
          text ?? "",
          style: AppTypography.text14.copyWith(
            color: AppColors.textBlack700,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

class TextWithFlagBtn extends StatelessWidget {
  const TextWithFlagBtn({
    Key? key,
    required this.text,
    this.imgPath,
    this.horizontalPadding,
    this.verticalPadding,
    this.borderRadius,
    this.isSelected = false,
    this.addMargin = true,
    this.onNavigate,
  }) : super(key: key);

  final String text;
  final String? imgPath;
  final double? horizontalPadding;
  final double? verticalPadding;
  final double? borderRadius;
  final bool isSelected;
  final bool addMargin;
  final VoidCallback? onNavigate;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onNavigate,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(horizontalPadding ?? 16),
          vertical: Sizer.height(verticalPadding ?? 10),
        ),
        margin: EdgeInsets.only(
          right: Sizer.width(addMargin ? 16 : 0),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryBlue : AppColors.gray100,
          borderRadius: BorderRadius.circular(borderRadius ?? 4),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text,
              style: AppTypography.text12.copyWith(
                color: isSelected ? AppColors.bgWhite : AppColors.textBlack700,
                fontWeight: FontWeight.w500,
              ),
            ),
            if (imgPath != null) const XBox(8),
            if (imgPath != null)
              SvgPicture.network(
                imgPath!,
                height: Sizer.height(14),
                width: Sizer.width(20),
              ),
          ],
        ),
      ),
    );
  }
}
