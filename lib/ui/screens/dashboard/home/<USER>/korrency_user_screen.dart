// ignore_for_file: use_build_context_synchronously

import 'package:flutter/services.dart';
import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:skeletonizer/skeletonizer.dart';

class KorrencyUserScreen extends StatefulWidget {
  const KorrencyUserScreen({Key? key}) : super(key: key);

  @override
  State<KorrencyUserScreen> createState() => _KorrencyUserScreenState();
}

class _KorrencyUserScreenState extends State<KorrencyUserScreen> {
  final TextEditingController _searchController = TextEditingController();
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  _init() {
    context.read<PhoneBookVM>().fetchContacts();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PhoneBookVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.bgWhite,
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: Column(
              children: [
                const YBox(20),
                const CustomHeader(
                  // showBackBtn: true,
                  showHeader: true,
                  headerText: 'Send to Korrency user',
                ),
                const YBox(40),
                CustomTextField(
                  borderRadius: Sizer.height(4),
                  controller: _searchController,
                  prefixIcon: Icon(
                    Iconsax.search_normal_1,
                    color: AppColors.black600,
                    size: Sizer.radius(20),
                  ),
                  hintText: 'Search...',
                  suffixIcon: CopyWithIcon(
                    text: 'Paste',
                    margin: EdgeInsets.only(
                      top: Sizer.height(11),
                      bottom: Sizer.height(11),
                      right: Sizer.width(16),
                    ),
                    onPressed: () async {
                      final data = await Clipboard.getData('text/plain');
                      if (data != null) {
                        _searchController.text = data.text ?? "";
                        vm.getPhoneBooks(searchQuery: data.text ?? "");
                      }
                    },
                  ),
                  onChanged: (val) {
                    printty(val);
                    vm.getPhoneBooks(searchQuery: val.trim());
                  },
                ),
                const YBox(12),
                Row(
                  children: [
                    Icon(
                      Iconsax.info_circle5,
                      color: AppColors.primaryBlue,
                      size: Sizer.radius(24),
                    ),
                    const XBox(6),
                    Expanded(
                      child: Text(
                        "You can search for a username or paste one here if they have sent it to you.",
                        style: AppTypography.text12.copyWith(
                          color: AppColors.primaryBlue,
                        ),
                      ),
                    ),
                  ],
                ),
                Expanded(
                  child: Builder(builder: (context) {
                    if (vm.permissionDenied) {
                      return const CheckContactContent();
                    }
                    if (vm.busy(fetchContactsState)) {
                      return ListView.separated(
                        padding: const EdgeInsets.only(top: 40),
                        itemBuilder: (ctx, i) {
                          return Skeletonizer(
                            child: UserListTile(
                              name: BoneMock.words(5),
                              showTrailing: true,
                              showSubTitle: true,
                              subTitle: BoneMock.date,
                              onTap: () {},
                            ),
                          );
                        },
                        separatorBuilder: (ctx, _) => const YBox(26),
                        itemCount: 10,
                      );
                    }
                    if (vm.korrencyUsers.isEmpty && !vm.isBusy) {
                      return Center(
                        child: Text(
                          "No Korrency user found",
                          style: AppTypography.text16.copyWith(
                            color: AppColors.textBlack600,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      );
                    }
                    return RefreshIndicator(
                      onRefresh: () async {
                        _init();
                      },
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            ...vm.korrencyUsers.entries.map((e) {
                              return ContactListWidget(
                                title: e.key,
                                korrencyUsers: e.value,
                              );
                            }).toList(),
                            const YBox(100)
                          ],
                        ),
                      ),
                    );
                  }),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}

class ContactListWidget extends StatelessWidget {
  const ContactListWidget({
    Key? key,
    required this.title,
    required this.korrencyUsers,
  }) : super(key: key);

  final String title;
  final List<KorrencyUser> korrencyUsers;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const YBox(30),
        Text(
          title,
          style: AppTypography.text14,
        ),
        const YBox(16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (ctx, i) {
            return UserListTile(
              name: korrencyUsers[i].fullName ?? "",
              showTrailing: true,
              showSubTitle: true,
              subTitle: korrencyUsers[i].userName ?? "",
              onTap: () {
                context
                    .read<SendMoneyVM>()
                    .setKorrencyUsername(korrencyUsers[i].userName);
                Navigator.pushNamed(context, RoutePath.reviewScreen,
                    arguments: true);
              },
            );
          },
          separatorBuilder: (ctx, i) => const YBox(26),
          itemCount: korrencyUsers.length,
        ),
      ],
    );
  }
}

class CheckContactContent extends StatelessWidget {
  const CheckContactContent({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          const YBox(80),
          SizedBox(
            height: Sizer.height(236),
            width: Sizer.width(236),
            child: imageHelper(AppImages.phoneUser),
          ),
          const YBox(16),
          Text(
            "You can also check your contact list for a user who is on the platform.",
            textAlign: TextAlign.center,
            style: AppTypography.text16.copyWith(
              color: AppColors.textBlack600,
            ),
          ),
          const YBox(120),
          CustomBtn.solid(
            onTap: () async {
              // context.read<PhoneBookVM>().fetchContacts();
              final res = await Permission.contacts.status;
              if (res.isDenied) {
                await openAppSettings();
              }
              context.read<PhoneBookVM>().fetchContacts();
            },
            online: true,
            text: "Give Permission",
          ),
          const YBox(80),
        ],
      ),
    );
  }
}
