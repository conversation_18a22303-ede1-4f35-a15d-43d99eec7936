import 'package:flutter_svg/svg.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

import 'statement_confirmation_screen.dart';

class AccountStatementScreen extends StatefulWidget {
  const AccountStatementScreen({Key? key}) : super(key: key);

  @override
  State<AccountStatementScreen> createState() => _AccountStatementScreenState();
}

class _AccountStatementScreenState extends State<AccountStatementScreen> {
  final TextEditingController _startDateC = TextEditingController();
  final TextEditingController _endDateC = TextEditingController();
  final TextEditingController _currencyC = TextEditingController();

  DateTime startDate = DateTime.now();
  DateTime endDate = DateTime.now();
  Wallet? selectedWallet;

  @override
  void initState() {
    super.initState();
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   selectedWallet = context.read<WalletVM>().activeWallet;
    //   _currencyC.text = selectedWallet?.currency?.name ?? "";
    //   setState(() {});
    // });
  }

  @override
  void dispose() {
    _startDateC.dispose();
    _endDateC.dispose();
    _currencyC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    selectedWallet = context.read<WalletVM>().activeWallet;
    _currencyC.text = selectedWallet?.currency?.name ?? "";
    printty("Flag ${selectedWallet?.currency}");
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: SafeArea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const YBox(20),
                    CustomHeader(
                      onBackBtnTap: () {
                        Navigator.pop(context);
                      },
                      showHeader: true,
                      headerText: 'Download Account Statement',
                    ),
                    const YBox(50),
                    Container(
                      alignment: Alignment.centerLeft,
                      child: const AuthTextSubTitle(
                        title: "Download Statement",
                        subtitle: "Download your transaction report activity",
                      ),
                    ),
                    const YBox(30),
                    CustomTextField(
                      isReadOnly: true,
                      height: Sizer.height(50),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(10),
                        vertical: Sizer.height(20),
                      ),
                      labelText: "Starting From",
                      hintText: '17 Feb 2024',
                      showLabelHeader: true,
                      borderRadius: Sizer.height(4),
                      showSuffixIcon: true,
                      suffixIcon: Icon(
                        Icons.event,
                        color: AppColors.gray500,
                        size: Sizer.height(20),
                      ),
                      controller: _startDateC,
                      onTap: () {
                        showCupertinoDatePicker(
                          context,
                          onDateTimeChanged: (val) {
                            // Check if val is greater now
                            if (val.isAfter(DateTime.now())) {
                              startDate = DateTime.now();
                              return;
                            }

                            startDate = val;
                          },
                          onDone: () {
                            _startDateC.text =
                                AppUtils.dayWithSuffixMonthAndYear(startDate);
                            vm.reBuildUI();
                            Navigator.pop(context);
                          },
                        );
                      },
                      onChanged: (val) {},
                    ),
                    const YBox(20),
                    CustomTextField(
                      isReadOnly: true,
                      height: Sizer.height(50),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: Sizer.width(10),
                        vertical: Sizer.height(20),
                      ),
                      labelText: "Ending On",
                      hintText: '17 Feb 2024',
                      showLabelHeader: true,
                      borderRadius: Sizer.height(4),
                      showSuffixIcon: true,
                      suffixIcon: Icon(
                        Icons.event,
                        color: AppColors.gray500,
                        size: Sizer.height(20),
                      ),
                      controller: _endDateC,
                      onTap: () {
                        showCupertinoDatePicker(
                          context,
                          minimumDate: startDate,
                          onDateTimeChanged: (val) {
                            if (val.isAfter(DateTime.now())) {
                              endDate = DateTime.now();
                              return;
                            }
                            endDate = val;
                          },
                          onDone: () {
                            _endDateC.text =
                                AppUtils.dayWithSuffixMonthAndYear(endDate);
                            vm.reBuildUI();
                            Navigator.pop(context);
                          },
                        );
                      },
                      onChanged: (val) {},
                    ),
                    const YBox(20),
                    CustomTextField(
                      labelText: "Select Wallet",
                      showLabelHeader: true,
                      isReadOnly: true,
                      controller: _currencyC,
                      borderRadius: Sizer.height(4),
                      hintText: 'Select Currency',
                      showSuffixIcon: true,
                      prefixIcon: Container(
                          padding: EdgeInsets.all(Sizer.radius(10)),
                          child: SvgPicture.network(
                            selectedWallet?.currency?.flag ?? '',
                            width: Sizer.width(20),
                            height: Sizer.height(14),
                          )),
                      suffixIcon: Icon(
                        Icons.expand_more,
                        color: AppColors.gray500,
                        size: Sizer.height(26),
                      ),
                      onTap: () {
                        BsWrapper.bottomSheet(
                          context: context,
                          widget:
                              CurrencyWalletSheet(onWalletSelected: (wallet) {
                            selectedWallet = wallet;
                            _currencyC.text = wallet.currency?.name ?? '';
                            vm.reBuildUI();
                          }),
                        );
                      },
                    ),
                    const YBox(20),
                    CustomTextField(
                      labelText: "Email",
                      isReadOnly: true,
                      fillColor: AppColors.litGrey100,
                      showLabelHeader: true,
                      hideBorder: true,
                      borderRadius: Sizer.height(4),
                      hintText: vm.user?.email,
                      onChanged: (val) {},
                    ),
                  ],
                ),
              ),
            ),
          ),
          bottomSheet: Container(
            color: AppColors.bgWhite,
            padding: EdgeInsets.only(
              left: Sizer.width(24),
              right: Sizer.width(24),
              bottom: Sizer.height(40),
            ),
            child: CustomBtn.solid(
              onTap: () {
                _downloadAccountStatement(vm);
              },
              online: selectedWallet != null &&
                  _startDateC.text.trim().isNotEmpty &&
                  _endDateC.text.trim().isNotEmpty &&
                  _currencyC.text.trim().isNotEmpty,
              text: "Continue",
            ),
          ),
        ),
      );
    });
  }

  _downloadAccountStatement(AuthUserVM vm) {
    vm
        .downloadAccountStatement(
      startDate: startDate.toIso8601String().split("T").first,
      endDate: endDate.toIso8601String().split("T").first,
      currencyId: selectedWallet?.currency?.id ?? 0,
    )
        .then((value) {
      if (value.success) {
        Navigator.pushNamed(context, RoutePath.interacEmailConfirmationScreen,
            arguments: StatementConfirmScreenArgs(
              title: "Your statement is on the way",
              message: value.message ?? "Your statement is on the way",
            ));
      } else {
        _showErrorConfirmationScreen(msg: value.message);
      }
    });
  }

  _showErrorConfirmationScreen({String? msg}) {
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ?? "Something went wrong",
        imgPath: AppGifs.failure,
        btnText: "Continue",
        btnTap: () {
          _pop();
        },
      ),
    );
  }

  _showSuccessConfirmationScreen(String msg) {
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg,
        btnText: "Home",
        btnTap: () {
          _pop();
          _pop();
        },
      ),
    );
  }

  _pop() {
    Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
  }
}
