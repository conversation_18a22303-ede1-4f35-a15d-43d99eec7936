import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AnotherInteracEmailScreen extends StatefulWidget {
  const AnotherInteracEmailScreen({Key? key}) : super(key: key);

  @override
  State<AnotherInteracEmailScreen> createState() =>
      _AnotherInteracEmailScreenState();
}

class _AnotherInteracEmailScreenState extends State<AnotherInteracEmailScreen> {
  TextEditingController interacEmailC = TextEditingController();
  FocusNode focusNode = FocusNode();

  @override
  void dispose() {
    interacEmailC.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InteracVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: Safe<PERSON>rea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                children: [
                  const CustomHeader(),
                  const YBox(50),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: const AuthTextSubTitle(
                      title: "Add another Interac email",
                      subtitle: "Link another Interac email to your CAD wallet",
                    ),
                  ),
                  const YBox(32),
                  CustomTextField(
                    controller: interacEmailC,
                    focusNode: focusNode,
                    labelText: "Interac email",
                    showLabelHeader: true,
                    borderRadius: Sizer.height(4),
                    hintText: 'Enter Interac email',
                    errorText: !_emailIsValid(interacEmailC.text.trim()) &&
                            interacEmailC.text.trim().isNotEmpty
                        ? "Invalid Email"
                        : null,
                    onChanged: (val) {
                      vm.reBuildUI();
                    },
                  ),
                  const Spacer(),
                  CustomBtn.solid(
                    online: _emailIsValid(interacEmailC.text.trim()),
                    onTap: () {
                      _requestInteracEmail();
                    },
                    text: "Continue",
                  ),
                  const YBox(40),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  bool _emailIsValid(String email) {
    return email.trim().isNotEmpty &&
        email.contains('@') &&
        email.contains('.');
  }

  _requestInteracEmail() {
    context
        .read<InteracVM>()
        .requestInteracEmail(
          interacEmailC.text.trim(),
        )
        .then((value) {
      if (value.success) {
        Navigator.pushNamed(
          context,
          RoutePath.verifyInteracEmailScreen,
          arguments: interacEmailC.text.trim(),
        );
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
          snackBarType: SnackBarType.success,
        );
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }
}
