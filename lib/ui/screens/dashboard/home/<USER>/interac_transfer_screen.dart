import 'package:flutter/gestures.dart';
import 'package:flutter/services.dart';
import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class InteracTransferScreen extends StatefulWidget {
  const InteracTransferScreen({Key? key}) : super(key: key);

  @override
  State<InteracTransferScreen> createState() => _InteracTransferScreenState();
}

class _InteracTransferScreenState extends State<InteracTransferScreen> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<InteracVM>().getInteracEmails();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InteracVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: SafeArea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: ListView(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  const CustomHeader(
                    showHeader: false,
                    headerText: 'Interac e-Transfer',
                  ),
                  const YBox(40),
                  imageHelper(
                    AppImages.interacK,
                    height: Sizer.height(75),
                  ),
                  const YBox(47),
                  Consumer<ConfigVM>(builder: (context, configVM, _) {
                    return CustomTextField(
                      labelText: "Copy and Paste this e-mail",
                      isReadOnly: true,
                      showLabelHeader: true,
                      borderRadius: Sizer.height(4),
                      hintText: configVM.interacDepositEmail,
                      hintStyle: TextStyle(
                        fontSize: Sizer.text(12),
                        fontWeight: FontWeight.w400,
                        color: AppColors.baseBlack,
                      ),
                      showSuffixIcon: true,
                      suffixIcon: CopyWithIcon(
                        margin: EdgeInsets.only(
                          top: Sizer.height(11),
                          bottom: Sizer.height(11),
                          right: Sizer.width(16),
                        ),
                        onPressed: () {
                          Clipboard.setData(
                            ClipboardData(
                              text: configVM.interacDepositEmail ?? '',
                            ),
                          );
                          FlushBarToast.fLSnackBar(
                            message: "Copied to clipboard",
                            snackBarType: SnackBarType.success,
                          );
                        },
                      ),
                      onChanged: (val) {},
                    );
                  }),
                  const YBox(40),
                  Stack(
                    children: [
                      Container(
                        width: Sizer.width(28),
                        height: Sizer.height(196),
                        decoration: BoxDecoration(
                          color: AppColors.blue300,
                          borderRadius: BorderRadius.circular(Sizer.height(20)),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.only(
                            top: Sizer.height(5), left: Sizer.width(1)),
                        height: Sizer.height(200),
                        child: const Column(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            InteracTimeline(
                              textOne:
                                  "Login to your bank app and send money with interac e-transfer to the above email",
                              step: "1",
                            ),
                            InteracTimeline(
                              textOne:
                                  "Please ensure that your transfer is sent from your verified Interac email",
                              step: "2",
                            ),
                            InteracTimeline(
                              textOne:
                                  "Your funds will typically appear in your CAD wallet within minutes",
                              step: "3",
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const YBox(38),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Your verified interac emails",
                        style: AppTypography.text12.copyWith(
                          color: AppColors.textBlack800,
                        ),
                      ),
                      InkWell(
                        onTap: () {
                          if (vm.interacEmails.length > 2) {
                            FlushBarToast.fLSnackBar(
                              message:
                                  'You\'ve hit the limit on Interac emails you can add. Need help or an update? Contact support!',
                            );
                            return;
                          }
                          Navigator.pushNamed(
                              context, RoutePath.anotherInteracEmailScreen);
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(10),
                            vertical: Sizer.height(2),
                          ),
                          decoration: BoxDecoration(
                            color: AppColors.primaryBlue,
                            borderRadius:
                                BorderRadius.circular(Sizer.radius(10)),
                          ),
                          child: Text(
                            'Add Email',
                            style: AppTypography.text12.copyWith(
                              color: AppColors.white,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      )
                    ],
                  ),
                  const YBox(3),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(12),
                      vertical: Sizer.height(8),
                    ),
                    decoration: const BoxDecoration(color: AppColors.blu000),
                    child: Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: List.generate(
                              vm.interacEmails.length,
                              (index) {
                                return Container(
                                  padding: vm.interacEmails.length == index + 1
                                      ? EdgeInsets.zero
                                      : EdgeInsets.only(
                                          bottom: Sizer.height(2),
                                        ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Text(
                                        'Email ${index + 1}',
                                        style: AppTypography.text12.copyWith(
                                          color: AppColors.textBlack800,
                                        ),
                                      ),
                                      Text(
                                        vm.interacEmails[index],
                                        style: AppTypography.text12.copyWith(
                                          color: AppColors.blue800,
                                        ),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const YBox(7),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Icon(
                        Iconsax.info_circle5,
                        color: AppColors.iconBlack800,
                        size: Sizer.radius(20),
                      ),
                      const XBox(4),
                      Text(
                        "Max of 3 emails allowed",
                        style: AppTypography.text12.copyWith(
                          color: AppColors.iconBlack800,
                        ),
                      )
                    ],
                  )
                  // CustomBtn.solid(
                  //   onTap: () {
                  //     if (vm.interacEmails.length > 2) {
                  //       FlushBarToast.fLSnackBar(
                  //         message:
                  //             'You\'ve hit the limit on Interac emails you can add. Need help or an update? Contact support!',
                  //       );
                  //       return;
                  //     }
                  //     Navigator.pushNamed(
                  //         context, RoutePath.anotherInteracEmailScreen);
                  //   },
                  //   text: "Add Interac email",
                  // ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }
}

class InteracTimeline extends StatelessWidget {
  const InteracTimeline({
    Key? key,
    this.isEnd = false,
    required this.textOne,
    this.textBoldTwo,
    this.textBoldColor,
    this.textItalicThree,
    this.onTapTextBoldTwo,
    required this.step,
  }) : super(key: key);

  final bool isEnd;
  final String textOne;
  final String? textBoldTwo;
  final VoidCallback? onTapTextBoldTwo;
  final Color? textBoldColor;
  final String? textItalicThree;
  final String step;

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Column(
          children: [
            Container(
              width: Sizer.width(25),
              height: Sizer.height(25),
              decoration: BoxDecoration(
                color: AppColors.primaryBlue,
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
              ),
              child: Center(
                child: Text(
                  step,
                  style: AppTypography.text14.copyWith(
                    fontWeight: FontWeight.w700,
                    color: AppColors.white,
                  ),
                ),
              ),
            ),
          ],
        ),
        const XBox(9),
        Expanded(
            child: RichText(
          text: TextSpan(
            children: [
              TextSpan(
                text: textOne,
                style: AppTypography.text14.copyWith(
                  color: AppColors.textBlack800,
                  fontWeight: FontWeight.w400,
                  fontFamily: 'Inter',
                ),
              ),
              if (textBoldTwo != null)
                TextSpan(
                  text: " $textBoldTwo",
                  style: AppTypography.text13.copyWith(
                    color: textBoldColor ?? AppColors.lightBlue,
                    fontWeight: FontWeight.w700,
                    fontFamily: 'Inter',
                  ),
                  recognizer: TapGestureRecognizer()..onTap = onTapTextBoldTwo,
                ),
              if (textItalicThree != null)
                TextSpan(
                  text: " $textItalicThree",
                  style: AppTypography.text12.copyWith(
                    fontWeight: FontWeight.w400,
                    fontFamily: 'Inter',
                    height: 1.5,
                    fontStyle: FontStyle.italic,
                    color: AppColors.textBlack800,
                  ),
                ),
            ],
          ),
        )),
      ],
    );
  }
}
