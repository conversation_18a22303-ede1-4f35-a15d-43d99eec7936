import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendMoneyMethodScreen extends StatefulWidget {
  const SendMoneyMethodScreen({super.key});

  @override
  State<SendMoneyMethodScreen> createState() => _SendMoneyMethodScreenState();
}

class _SendMoneyMethodScreenState extends State<SendMoneyMethodScreen> {
  @override
  Widget build(BuildContext context) {
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.bgWhite,
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: ListView(
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: const ArrowBack(),
                ),
                const YBox(30),
                const AuthTextSubTitle(
                  title: "Choose preferred method",
                  subtitle: "Select how you would like to pay",
                ),
                const YBox(24),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (ctx, i) {
                    return CustomListViews.view(
                        // icon: Iconsax.bank,
                        isSvg: true,
                        isNetworkSvg: true,
                        icon: vm.paymentMethods[i].icon ?? "",
                        title: vm.paymentMethods[i].name ?? "",
                        onTap: () {
                          printty('Single item ${vm.paymentMethods[i]}');
                          _switchMethod(vm.paymentMethods[i]);
                        });
                  },
                  separatorBuilder: (_, __) => const YBox(24),
                  itemCount: vm.paymentMethods.length,
                )
              ],
            ),
          ),
        ),
      );
    });
  }

  /// Here we pass the payment method to validation
  /// To use namecheck
  _switchMethod(PaymentMethod method) {
    final vm = context.read<SendMoneyVM>();
    if (method.id?.toLowerCase() == 'bank_transfer') {
      vm.setTransferMethodType(TransferMethodType.bankTransfer);
      Navigator.pushNamed(context, RoutePath.sendToNgnScreen,
          arguments: method);
    } else if (method.id?.toLowerCase() == 'interac') {
      vm.setTransferMethodType(TransferMethodType.interac);
      Navigator.pushNamed(context, RoutePath.sendToCadScreen,
          arguments: method);
    } else if (method.id?.toLowerCase() == 'mobile_money') {
      vm.setTransferMethodType(TransferMethodType.mobileMoney);
      Navigator.pushNamed(context, RoutePath.sendMoneyMobileMoney,
          arguments: method);
    } else {
      vm.setTransferMethodType(TransferMethodType.korrency);
      Navigator.pushNamed(context, RoutePath.sendToKorrencyUser,
          arguments: method);
    }
  }
}
