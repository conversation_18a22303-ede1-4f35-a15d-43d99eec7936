import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class AccountDetailsScreen extends StatelessWidget {
  const AccountDetailsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, vm, _) {
      return Scaffold(
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: const ArrowBack(),
                  ),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Your Account Details",
                    subtitle:
                        "Transfer funds directly to your wallet using these account details.",
                  ),
                  const YBox(30),
                  Builder(builder: (context) {
                    var virtualAcct = vm.activeWallet?.virtualAccounts;
                    if ((virtualAcct?.isEmpty ?? true) ||
                        vm.activeWallet?.currency?.code != "NGN") {
                      return _buildNoAccountDetails(context);
                    }
                    return _buildAccountDetailsList(context, virtualAcct);
                  }),
                  const YBox(30),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  Widget _buildNoAccountDetails(BuildContext context) {
    return SizedBox(
      height: Sizer.height(200),
      child: Center(
        child: Text(
          "No Account Details",
          style: AppTypography.text16.copyWith(
            fontWeight: FontWeight.w600,
            color: AppColors.gray500,
          ),
        ),
      ),
    );
  }

  Widget _buildAccountDetailsList(
      BuildContext context, List<VirtualAccount>? virtualAcct) {
    if (virtualAcct == null || virtualAcct.isEmpty) {
      return _buildNoAccountDetails(context);
    }
    return ListView.separated(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (ctx, i) {
        return _buildDottedDetailCard(context, virtualAcct[i]);
      },
      separatorBuilder: (ctx, _) => const YBox(20),
      itemCount: virtualAcct.length,
    );
  }

  Widget _buildDottedDetailCard(BuildContext context, VirtualAccount account) {
    return DottedDetailCard(
      virtualAcctBank: account.bankName ?? "",
      virtualAcctName: account.accountName ?? "",
      virtualAcctNumber: account.accountNumber ?? "",
      onCopy: () {
        Clipboard.setData(
          ClipboardData(
            text: account.accountNumber ?? "",
          ),
        );
        FlushBarToast.fLSnackBar(
          message: "Copied to clipboard",
          snackBarType: SnackBarType.success,
        );
      },
      onShare: () {
        Share.share(
          "Account Number: ${account.accountNumber} \nAccount Name: ${account.accountName} \nBank Name: ${account.bankName}",
          subject: "Korrency",
        );
      },
    );
  }
}
