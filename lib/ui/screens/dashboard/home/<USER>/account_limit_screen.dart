import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AccounntLimitScreen extends StatefulWidget {
  const AccounntLimitScreen({super.key});

  @override
  State<AccounntLimitScreen> createState() => _AccounntLimitScreenState();
}

class _AccounntLimitScreenState extends State<AccounntLimitScreen> {
  int _selectedIndex = 0;

  @override
  initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CurrencyVM>().getCurrenciesLimits();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CurrencyVM>(builder: (context, vm, _) {
      printty(vm.selectedCurrencyLimit, level: "selectedCurrencyLimit");
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          body: <PERSON><PERSON>rea(
            bottom: false,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const YBox(20),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  alignment: Alignment.centerLeft,
                  child: const ArrowBack(),
                ),
                const YBox(30),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: const AuthTextSubTitle(
                    title: "Account Limits",
                    subtitle:
                        "View your transaction limits for different currencies",
                  ),
                ),
                const YBox(16),
                SizedBox(
                  height: Sizer.height(40),
                  child: ListView.separated(
                    shrinkWrap: true,
                    scrollDirection: Axis.horizontal,
                    padding: EdgeInsets.only(
                      left: Sizer.width(24),
                    ),
                    itemBuilder: (ctx, i) {
                      var currencyLimit = vm.isCreatableCurrenciesLimits[i];
                      return CodeCard(
                        onTap: () {
                          setState(() {
                            _selectedIndex = i;
                          });
                          vm.setSelectedCurrencyLimit(currencyLimit.code);
                        },
                        isSelected: _selectedIndex == i,
                        code: currencyLimit.code ?? "",
                      );
                    },
                    separatorBuilder: (ctx, _) => const XBox(24),
                    itemCount: vm.isCreatableCurrenciesLimits.length,
                  ),
                ),
                const YBox(24),
                vm.selectedCurrencyLimit != null
                    ? Builder(builder: (context) {
                        printty(vm.selectedCurrencyLimit,
                            level: "selectedCurrencyLimit");
                        var limits = vm.selectedCurrencyLimit?.limits;
                        return Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Send Limit",
                                style: AppTypography.text14.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.primaryBlue,
                                ),
                              ),
                              const YBox(10),
                              LimitListTile(
                                percent: _convertIntToDouble(
                                    limits?.dailySpend?.percentageUsed),
                                title:
                                    '${AppUtils.formatAmountDoubleString(limits?.dailySpend?.totalAmount.toString() ?? "0")} ${vm.selectedCurrencyLimit?.code}',
                                subtitle:
                                    '${AppUtils.formatAmountDoubleString(limits?.dailySpend?.amountLeft.toString() ?? "0")} ${vm.selectedCurrencyLimit?.code} left',
                                trailingText: "Daily limit",
                              ),
                              const YBox(20),
                              LimitListTile(
                                percent: _convertIntToDouble(
                                    limits?.weeklySpend?.percentageUsed),
                                title:
                                    '${AppUtils.formatAmountDoubleString(limits?.weeklySpend?.totalAmount.toString() ?? "0")} ${vm.selectedCurrencyLimit?.code}',
                                subtitle:
                                    '${AppUtils.formatAmountDoubleString(limits?.weeklySpend?.amountLeft.toString() ?? "0")} ${vm.selectedCurrencyLimit?.code} left',
                                trailingText: "Weekly limit",
                              ),
                              const YBox(20),
                              LimitListTile(
                                percent: _convertIntToDouble(
                                    limits?.monthlySpend?.percentageUsed),
                                title:
                                    '${AppUtils.formatAmountDoubleString(limits?.monthlySpend?.totalAmount.toString() ?? "0")} ${vm.selectedCurrencyLimit?.code}',
                                subtitle:
                                    '${AppUtils.formatAmountDoubleString(limits?.monthlySpend?.amountLeft.toString() ?? "0")} ${vm.selectedCurrencyLimit?.code} left',
                                trailingText: "Monthly limit",
                              ),
                              const YBox(20),
                              Text(
                                "Receive Limit",
                                style: AppTypography.text14.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.primaryBlue,
                                ),
                              ),
                              const YBox(10),
                              ContainerWithBluewishBg(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(16),
                                  vertical: Sizer.height(20),
                                ),
                                child: Text(
                                  limits?.deposit ?? "",
                                  style: AppTypography.text14.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                              const YBox(20),
                              Text(
                                "Korrency to Korrency",
                                style: AppTypography.text14.copyWith(
                                  fontWeight: FontWeight.w500,
                                  color: AppColors.primaryBlue,
                                ),
                              ),
                              const YBox(10),
                              ContainerWithBluewishBg(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(16),
                                  vertical: Sizer.height(20),
                                ),
                                child: Text(
                                  limits?.deposit ?? "",
                                  style: AppTypography.text14.copyWith(
                                    fontWeight: FontWeight.w600,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        );
                      })
                    : const SizedBox(),
              ],
            ),
          ),
        ),
      );
    });
  }

  _convertIntToDouble(String? value) {
    return double.tryParse(value ?? "0") ?? 0;
  }
}

class CodeCard extends StatelessWidget {
  const CodeCard({
    super.key,
    this.isSelected = false,
    required this.code,
    this.onTap,
  });

  final bool isSelected;
  final String code;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(25),
          vertical: Sizer.height(13),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.primaryBlue : AppColors.gray100,
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          code,
          style: AppTypography.text12.copyWith(
            color: isSelected ? AppColors.white : AppColors.darkBlue,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}

class LimitListTile extends StatelessWidget {
  const LimitListTile({
    super.key,
    required this.title,
    required this.subtitle,
    required this.trailingText,
    required this.percent,
  });

  final String title;
  final String subtitle;
  final String trailingText;

  /// % btw 0 - 100
  final double percent;

  @override
  Widget build(BuildContext context) {
    return ContainerWithBluewishBg(
      padding: EdgeInsets.zero,
      height: Sizer.height(60),
      child: Stack(
        children: [
          Container(
            height: Sizer.height(60),
            width: Sizer.width(convertPercToWidth(percent, context)),
            decoration: const BoxDecoration(
              color: AppColors.limitColor,
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(15),
              vertical: Sizer.height(7),
            ),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: AppTypography.text14.copyWith(
                        fontWeight: FontWeight.w700,
                        color: AppColors.primaryBlue,
                      ),
                    ),
                    const YBox(4),
                    Text(
                      subtitle,
                      style: AppTypography.text12.copyWith(
                        color: AppColors.darkGreen,
                      ),
                    )
                  ],
                ),
                const Spacer(),
                Text(
                  trailingText,
                  style: AppTypography.text14.copyWith(
                    color: AppColors.primaryBlue,
                    fontWeight: FontWeight.w500,
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  double convertPercToWidth(double percent, BuildContext context) {
    Size size = MediaQuery.of(context).size;
    return (size.width - 80) * (percent / 100);
  }
}
