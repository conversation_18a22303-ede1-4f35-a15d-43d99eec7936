import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/home/<USER>/statement_confirmation_screen.dart';
import 'package:pinput/pinput.dart';

class VerifyInteracEmailScreen extends StatefulWidget {
  const VerifyInteracEmailScreen({
    Key? key,
    required this.email,
  }) : super(key: key);

  final String email;

  @override
  State<VerifyInteracEmailScreen> createState() =>
      _VerifyInteracEmailScreenState();
}

class _VerifyInteracEmailScreenState extends State<VerifyInteracEmailScreen> {
  final pinC = TextEditingController();
  final focusNode = FocusNode();

  @override
  void dispose() {
    pinC.dispose();
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<InteracVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: SafeArea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const CustomHeader(),
                  const YBox(50),
                  Container(
                    alignment: Alignment.centerLeft,
                    child: const AuthTextSubTitle(
                      title: "Verify Interac Email",
                      subtitle: "Please enter the 6-digit code sent to",
                    ),
                  ),
                  Text(
                    widget.email,
                    style: AppTypography.text15.copyWith(
                      color: AppColors.gray800,
                      fontWeight: FontWeight.w600,
                      height: 1.2,
                    ),
                  ),
                  const YBox(30),
                  Pinput(
                    defaultPinTheme: PinInputTheme.defaultPinTheme(),
                    followingPinTheme: PinInputTheme.followPinTheme(),
                    length: 6,
                    controller: pinC,
                    focusNode: focusNode,
                    showCursor: true,
                    onChanged: (value) => vm.reBuildUI(),
                    onCompleted: (pin) {
                      _verifyInteracEmail();
                    },
                  ),
                  const YBox(24),
                  ResendCode(
                    onResendCode: () {
                      vm.requestInteracEmail(widget.email);
                    },
                  ),
                  const Spacer(),
                  CustomBtn.solid(
                    online: pinC.text.trim().length == 6,
                    onTap: () {
                      _verifyInteracEmail();
                    },
                    text: "Verify",
                  ),
                  const YBox(40),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  _verifyInteracEmail() {
    context
        .read<InteracVM>()
        .verifyInteracEmail(widget.email, pinC.text.trim())
        .then((value) {
      if (value.success) {
        Navigator.pushNamed(context, RoutePath.interacEmailConfirmationScreen,
            arguments: StatementConfirmScreenArgs(
              title: "Interac email added",
              message: value.message ?? "Interac email verified successfully",
            ));
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }
}

//  "Interac email added",