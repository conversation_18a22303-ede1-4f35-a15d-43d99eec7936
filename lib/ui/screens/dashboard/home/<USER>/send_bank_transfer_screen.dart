import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SendBankTransferScreen extends StatefulWidget {
  const SendBankTransferScreen({
    super.key,
    required this.paymentMethod,
  });

  /// NOTE: if namecheck is true
  /// Add validation for account name
  /// if  false, do no validation
  final PaymentMethod paymentMethod;

  @override
  State<SendBankTransferScreen> createState() => _SendBankTransferScreenState();
}

class _SendBankTransferScreenState extends State<SendBankTransferScreen> {
  final FocusNode _accountNumFocus = FocusNode();
  final recipientFirstNameC = TextEditingController();
  final recipientLastNameC = TextEditingController();
  final recipientFirstNameF = FocusNode();
  final recipientLastNameF = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final sendMoneyVM = context.read<SendMoneyVM>();
      final bankVm = context.read<BankVM>();
      final beneVm = context.read<BeneficiaryVM>();
      bankVm.getBanksByCurrencyId(sendMoneyVM.recipientCurrency?.id ?? 0);
      beneVm.getBeneficiaries(
        currencyId: sendMoneyVM.recipientCurrency?.id,
        transferMethod: widget.paymentMethod.id,
      );
    });
  }

  @override
  void dispose() {
    _accountNumFocus.dispose();
    recipientFirstNameC.dispose();
    recipientLastNameC.dispose();
    recipientFirstNameF.dispose();
    recipientLastNameF.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<SendMoneyVM>(builder: (context, vm, _) {
      printty(" nameCheck ${widget.paymentMethod.nameCheck}");
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            child: Column(
              children: [
                const YBox(20),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(24),
                  ),
                  child: CustomHeader(
                    showBackBtn: true,
                    showHeader: true,
                    onBackBtnTap: () {
                      vm.clearBankDetails();
                      Navigator.pop(context);
                    },
                    headerText:
                        'Send to ${vm.recipientCurrency?.code} Bank Account',
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Container(
                          padding: EdgeInsets.symmetric(
                            horizontal: Sizer.width(24),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const YBox(24),
                              CustomTextField(
                                labelText: "Select Bank",
                                showLabelHeader: true,
                                isReadOnly: true,
                                hintText: 'Access Bank',
                                borderRadius: Sizer.height(4),
                                showSuffixIcon: true,
                                suffixIcon: Icon(
                                  Icons.expand_more,
                                  size: Sizer.radius(25),
                                ),
                                onTap: () {
                                  BsWrapper.bottomSheet(
                                    context: context,
                                    widget: BankSheet(
                                      selectBankType: SelectBankType.sendMoney,
                                      recipientCurrencyId:
                                          vm.recipientCurrency?.id,
                                    ),
                                  );
                                },
                                controller: vm.bankNameC,
                              ),
                              const YBox(24),
                              CustomTextField(
                                focusNode: _accountNumFocus,
                                labelText: "Account Number",
                                showLabelHeader: true,
                                isReadOnly: vm.bankNameC.text.isEmpty,
                                keyboardType: KeyboardType.number,
                                hintText: '**********',
                                borderRadius: Sizer.height(4),
                                suffixIcon: vm.busy(verifyingBankState)
                                    ? const CupertinoActivityIndicator()
                                    : null,
                                controller: vm.accountNumC,
                                onChanged: (val) {
                                  vm.clearAcctCredentials();
                                  if (val.trim().length > 9 &&
                                      widget.paymentMethod.nameCheck == true) {
                                    // _accountNumFocus.unfocus();
                                    vm
                                        .verifyBankAcctByCurrencyId(
                                      currencyId: vm.recipientCurrency?.id ?? 0,
                                      bankId: vm.bankUUID ?? '',
                                      accountNum: vm.accountNumC.text,
                                      amount:
                                          vm.recipientC.text.replaceAllCommas(),
                                    )
                                        .then((value) {
                                      if (!value.success) {
                                        FlushBarToast.fLSnackBar(
                                          message: value.message ??
                                              'Unable to verify account, kindly try again',
                                        );
                                      }
                                    });
                                  }
                                },
                              ),
                              const YBox(4),
                              if (vm.accountName != null &&
                                  widget.paymentMethod.nameCheck == true)
                                Row(
                                  children: [
                                    Text(
                                      vm.accountName ?? "",
                                      style: AppTypography.text12.copyWith(
                                        color: AppColors.baseGreen,
                                      ),
                                    ),
                                    const XBox(4),
                                    Icon(
                                      Icons.check_circle,
                                      size: Sizer.radius(13),
                                      color: AppColors.baseGreen,
                                    ),
                                  ],
                                ),
                              const YBox(24),
                              if (widget.paymentMethod.nameCheck == false)
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child: CustomTextField(
                                        focusNode: recipientFirstNameF,
                                        controller: recipientFirstNameC,
                                        labelText: "Recipient",
                                        showLabelHeader: true,
                                        hintText: 'First Name',
                                        borderRadius: Sizer.height(4),
                                        onChanged: (p0) => setState(() {}),
                                      ),
                                    ),
                                    const XBox(10),
                                    Expanded(
                                      child: CustomTextField(
                                        focusNode: recipientLastNameF,
                                        controller: recipientLastNameC,
                                        hintText: 'Last Name',
                                        borderRadius: Sizer.height(4),
                                        onChanged: (p0) => setState(() {}),
                                      ),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ),
                        const YBox(70),
                        Consumer<BeneficiaryVM>(builder: (context, beneVM, _) {
                          if (beneVM.beneficiariesByCurrencyId.isEmpty) {
                            return const YBox(160);
                          }
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(24),
                                ),
                                child: Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      "Saved Beneficiaries",
                                      style: AppTypography.text14.copyWith(
                                        fontWeight: FontWeight.w500,
                                      ),
                                    ),
                                    InkWell(
                                      onTap: () async {
                                        final res = await Navigator.pushNamed(
                                          context,
                                          RoutePath.beneficiaryScreen,
                                          arguments: BeneficiaryArg(
                                            currencyId:
                                                vm.recipientCurrency?.id ?? 0,
                                          ),
                                        );

                                        if (res is Beneficiary) {
                                          // This ensures a saved beneficiafy is selected
                                          vm.setSelectedIndex(0);
                                        }
                                      },
                                      child: Text(
                                        "View all",
                                        style: AppTypography.text14.copyWith(
                                          color: AppColors.gray500,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const YBox(24),
                              Builder(builder: (context) {
                                if (context.read<BeneficiaryVM>().isBusy) {
                                  return SizedBox(
                                    height: Sizer.height(80),
                                    child: const Center(
                                      child: CupertinoActivityIndicator(),
                                    ),
                                  );
                                }
                                return SizedBox(
                                  height: Sizer.height(120),
                                  child: ListView.separated(
                                    scrollDirection: Axis.horizontal,
                                    padding: EdgeInsets.symmetric(
                                        horizontal: Sizer.width(20)),
                                    itemBuilder: (ctx, i) {
                                      var beneficiary =
                                          beneVM.beneficiariesByCurrencyId[i];
                                      return BeneficiaryCard(
                                        title: beneficiary.accountName ?? "",
                                        imgPath: beneficiary.iconUrl ?? "",
                                        isSelected: vm.selectedIndex == i,
                                        onTap: () {
                                          vm.autoFillAcctNumberName(
                                              beneficiary);
                                          vm.setSelectedIndex(i);
                                          if (widget.paymentMethod.nameCheck ==
                                              false) {
                                            _setFirstAndLastNameFromBeneficiary(
                                                beneficiary);
                                          }
                                        },
                                      );
                                    },
                                    separatorBuilder: (ctx, _) =>
                                        const XBox(24),
                                    itemCount: beneVM.beneficiariesByCurrencyId
                                        .take(10)
                                        .length,
                                  ),
                                );
                              }),
                            ],
                          );
                        }),
                        const YBox(100),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          bottomSheet: Container(
            color: AppColors.white,
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              bottom: Sizer.height(30),
            ),
            child: CustomBtn.solid(
              onTap: () async {
                FocusScope.of(context).unfocus();

                if (vm.selectedIndex == -1) {
                  final result = await BsWrapper.bottomSheet(
                    context: context,
                    widget: BeneficiaryScamSheet(),
                  );

                  if (result is bool && result) {
                    if (widget.paymentMethod.nameCheck == false) {
                      printty("Name was checked");
                      vm.setAccountName(
                          '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}');
                    }

                    Navigator.pushNamed(
                      context,
                      RoutePath.reviewScreen,
                    );
                  }

                  return;
                }

                if (widget.paymentMethod.nameCheck == false) {
                  printty("Name was checked");
                  vm.setAccountName(
                      '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}');
                }
                Navigator.pushNamed(
                  context,
                  RoutePath.reviewScreen,
                );
              },
              online: vm.ngnButtonIsActive &&
                  (widget.paymentMethod.nameCheck == true
                      ? vm.accountName != null
                      : (recipientFirstNameC.text.isNotEmpty &&
                          recipientLastNameC.text.isNotEmpty)),
              text: "Continue",
            ),
          ),
        ),
      );
    });
  }

  _setFirstAndLastNameFromBeneficiary(Beneficiary beneficiary) {
    recipientFirstNameC.text = beneficiary.firstName ?? "";
    recipientLastNameC.text = beneficiary.lastName ?? "";

    setState(() {});
  }
}
