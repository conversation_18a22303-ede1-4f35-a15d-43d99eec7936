import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:share_plus/share_plus.dart';

class ShareByUsernameScreen extends StatelessWidget {
  const ShareByUsernameScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return Scaffold(
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: const ArrowBack(),
                ),
                const YBox(30),
                const AuthTextSubTitle(
                  title: "Your Username",
                  subtitle:
                      "Share your username with other Korrency users to receive funds easily and securely",
                ),
                const YBox(38),
                Text(
                  'Your username',
                  style: AppTypography.text14.copyWith(
                    color: AppColors.textGray900,
                  ),
                ),
                const YBox(8),
                Container(
                  padding: EdgeInsets.symmetric(
                    horizontal: Sizer.width(12),
                    vertical: Sizer.height(10),
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.userBg,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    children: [
                      Text(
                        vm.user?.userName ?? '',
                        style: AppTypography.text14,
                      ),
                      const Spacer(),
                      CopyShareBtn(
                        onTap: () {
                          Clipboard.setData(ClipboardData(
                            text: vm.user?.userName ?? '',
                          ));

                          FlushBarToast.fLSnackBar(
                            message:
                                "${vm.user?.userName ?? 'Username'} Copied",
                            snackBarType: SnackBarType.success,
                          );
                        },
                      ),
                    ],
                  ),
                ),
                const Spacer(),
                CustomBtn.solid(
                  onTap: () async {
                    await Share.share(
                      korrencyUserShare(
                        userName: vm.user?.userName,
                        refCode: vm.user?.referralCode,
                      ),
                      // vm.user?.userName ?? '',
                      subject: "Korrency",
                    );
                  },
                  online: true,
                  text: "Share",
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      );
    });
  }
}
