import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/kyc/setup_pin_screen.dart';

class ExchangeScreen extends StatelessWidget {
  const ExchangeScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.bgWhite,
      body: SafeArea(
        bottom: false,
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Container(
              //   alignment: Alignment.centerLeft,
              //   child: ArrowBack(
              //     onTap: () {},
              //   ),
              // ),
              const YBox(50),
              const AuthTextSubTitle(
                title: "Exchange",
                subtitle:
                    "List your offer on the marketplace or \nexchange instantly",
              ),
              const YBox(24),

              Consumer<WalletVM>(builder: (context, vm, _) {
                final authVM = context.read<AuthUserVM>();
                return OfferCard(
                  withBg: true,
                  svgIcon: AppSvgs.currencyExchange,
                  title: "Convert Money",
                  subTitleFirst: "Instantly exchange one currency",
                  subTitleFifth: "\nto another (wallet-to-wallet)",
                  onTap: () {
                    if (authVM.statusProcessing) {
                      FlushBarToast.fLSnackBar(
                        message: 'Your ID verification is in progress',
                        snackBarType: SnackBarType.success,
                      );
                      return;
                    }
                    if (authVM.secQuestCheck) {
                      Navigator.pushNamed(
                        context,
                        RoutePath.securityQuestionScreen,
                      );
                      return;
                    }
                    if (authVM.transactionPinCheck) {
                      BsWrapper.bottomSheet(
                        context: context,
                        canDismiss: false,
                        widget: const SetupPinScreen(),
                      );

                      return;
                    }
                    if (vm.walletList.length < 2) {
                      BsWrapper.bottomSheet(
                        context: context,
                        widget: ConfirmationSheet(
                          // title: 'Warning',
                          message:
                              "You need at least two wallets to convert currency",
                          firstBtnText: "Create Wallet",
                          firstBtnTap: () {
                            Navigator.pop(context);
                            BsWrapper.bottomSheet(
                              context: context,
                              widget: const AllWalletsSheet(),
                            );
                          },
                        ),
                      );

                      return;
                    }
                    Navigator.pushNamed(
                        context, RoutePath.convertCurrencyScreen);
                  },
                );
              }),
              const YBox(24),
              Consumer<DashboardVM>(builder: (context, vm, _) {
                return OfferCard(
                  withBg: true,
                  svgIcon: AppSvgs.people,
                  title: "Peer-to-Peer Exchange",
                  subTitleFirst: "Create an offer to buy or ",
                  subTitleFifth: " sell currency on the marketplace",
                  onTap: () {
                    FlushBarToast.fLSnackBar(
                      message: 'Marketplace is coming soon',
                      snackBarType: SnackBarType.success,
                    );
                    // Navigator.pushNamed(
                    //     context, RoutePath.navigateExchangeOffers);
                  },
                );
              }),
              const YBox(24),
            ],
          ),
        ),
      ),
    );
  }
}
