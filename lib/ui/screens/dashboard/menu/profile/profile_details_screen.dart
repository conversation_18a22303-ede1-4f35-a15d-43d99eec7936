import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ProfileDetailsScreen extends StatelessWidget {
  const ProfileDetailsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: Safe<PERSON>rea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: const ArrowBack(),
              ),
              const YBox(30),
              const AuthTextSubTitle(
                title: "Profile Details",
                subtitle: "All things that covers your profile on the app",
              ),
              const YBox(24),
              Expanded(
                child: Container(
                  width: Sizer.screenWidth,
                  color: AppColors.white,
                  child: <PERSON>View(
                    padding: EdgeInsets.only(
                      top: Sizer.height(24),
                      bottom: Sizer.height(24),
                    ),
                    children: [
                      MenuListTile(
                        title: 'View Profile',
                        iconData: Iconsax.user_edit,
                        onPressed: () {
                          Navigator.pushNamed(context, RoutePath.profileScreen);
                        },
                      ),
                      const YBox(20),
                      MenuListTile(
                        title: 'Change Avatar',
                        iconData: Iconsax.camera,
                        onPressed: () {
                          Navigator.pushNamed(
                              context, RoutePath.selectAvatarScreen);
                        },
                      ),
                      const YBox(20),
                      MenuListTile(
                        title: 'Deactivate Account',
                        iconData: Iconsax.trash,
                        bgColor: AppColors.opacityRed100,
                        iconColor: AppColors.iconRed,
                        showTrailing: false,
                        onPressed: () {
                          Navigator.pushNamed(
                              context, RoutePath.deactivateAccountScreen);
                        },
                      ),
                      const YBox(20),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
