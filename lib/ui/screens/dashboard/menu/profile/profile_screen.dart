import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/screens.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.white,
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: const ArrowBack(),
                ),
                const YBox(10),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const YBox(20),
                        Text(
                          "Profile",
                          style: AppTypography.text20.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const YBox(8),
                        InkWell(
                          onTap: () {
                            Navigator.pushNamed(
                                context, RoutePath.createFreshDeskTicketWebview,
                                arguments: WebViewArg(
                                  webURL: AppUtils.korrencyCreateTicket,
                                ));
                          },
                          child: ContainerWithBluewishBg(
                            child: Row(
                              children: [
                                Icon(
                                  Iconsax.info_circle5,
                                  color: AppColors.primaryBlue,
                                  size: Sizer.radius(24),
                                ),
                                const XBox(10),
                                Expanded(
                                  child: RichText(
                                    text: const TextSpan(children: [
                                      TextSpan(
                                        text:
                                            "Profile editing is locked as your account is verified. Please contact",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: AppColors.textBlack800,
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                      TextSpan(
                                        text: " customer support",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: AppColors.textBlue700,
                                          fontWeight: FontWeight.w500,
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                      TextSpan(
                                        text: " for changes",
                                        style: TextStyle(
                                          fontSize: 12,
                                          color: AppColors.textBlack800,
                                          fontFamily: 'Inter',
                                        ),
                                      ),
                                    ]),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                        const YBox(30),
                        CustomTextField(
                          labelText: "First Name",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.firstName,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Iconsax.lock_1,
                            color: AppColors.baseBlack,
                            size: Sizer.height(16),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "Last Name",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.lastName,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Iconsax.lock_1,
                            color: AppColors.baseBlack,
                            size: Sizer.height(16),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "Middle Name (optional)",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.middleName,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Iconsax.lock_1,
                            color: AppColors.baseBlack,
                            size: Sizer.height(16),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "Phone",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.phone,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Iconsax.lock_1,
                            color: AppColors.baseBlack,
                            size: Sizer.height(16),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "Email",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.email,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Iconsax.lock_1,
                            color: AppColors.baseBlack,
                            size: Sizer.height(16),
                          ),
                          onChanged: (val) {},
                        ),
                        // const YBox(20),
                        // CustomTextField(
                        //   labelText: "Interac Email",
                        //   isReadOnly: true,
                        //   fillColor: AppColors.litGrey100,
                        //   showLabelHeader: true,
                        //   borderRadius: Sizer.height(4),
                        //   hintText: vm.user?.interacEmail,
                        //   showSuffixIcon: true,
                        //   suffixIcon: Icon(
                        //     Iconsax.lock_1,
                        //     color: AppColors.baseBlack,
                        //     size: Sizer.height(16),
                        //   ),
                        //   onChanged: (val) {},
                        // ),
                        const YBox(36),
                        CustomTextField(
                          labelText: "Gender",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.gender,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Icons.expand_more,
                            color: AppColors.baseBlack,
                            size: Sizer.height(26),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "Date of Birth",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.dateOfBirth,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Iconsax.lock_1,
                            color: AppColors.baseBlack,
                            size: Sizer.height(16),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "Occupation",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.occupation?.name,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Iconsax.lock_1,
                            color: AppColors.baseBlack,
                            size: Sizer.height(16),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(36),
                        CustomTextField(
                          labelText: "Country of Residence",
                          showLabelHeader: true,
                          fillColor: AppColors.litGrey100,
                          isReadOnly: true,
                          borderRadius: Sizer.height(4),
                          prefixIcon: Container(
                            padding: EdgeInsets.all(Sizer.radius(10)),
                            child: imageHelper(
                              AppImages.cad,
                              height: Sizer.height(14),
                              width: Sizer.width(20),
                            ),
                          ),
                          hintText: 'Canada',
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Icons.expand_more,
                            color: AppColors.baseBlack,
                            size: Sizer.height(26),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "Address",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.address,
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "State/Region/Province",
                          fillColor: AppColors.litGrey100,
                          isReadOnly: true,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.state,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Icons.expand_more,
                            color: AppColors.gray500,
                            size: Sizer.height(26),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "City",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.city,
                          showSuffixIcon: true,
                          suffixIcon: Icon(
                            Icons.expand_more,
                            color: AppColors.gray500,
                            size: Sizer.height(26),
                          ),
                          onChanged: (val) {},
                        ),
                        const YBox(20),
                        CustomTextField(
                          labelText: "Postal Code",
                          isReadOnly: true,
                          fillColor: AppColors.litGrey100,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          hintText: vm.user?.postalCode,
                          onChanged: (val) {},
                        ),
                        const YBox(80),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }
}
