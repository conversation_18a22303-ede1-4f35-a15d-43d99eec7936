import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DeactivateAccountReasonScreen extends StatefulWidget {
  const DeactivateAccountReasonScreen({Key? key}) : super(key: key);

  @override
  State<DeactivateAccountReasonScreen> createState() =>
      _DeactivateAccountReasonScreenState();
}

class _DeactivateAccountReasonScreenState
    extends State<DeactivateAccountReasonScreen> {
  int selected = -2; // -2 for no selection, -1 for Other
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<AuthUserVM>().getDeactivedAccountMessage();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (ctx, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          appBar: CustomAppBarHeader(
            child: Container(
              padding: const EdgeInsets.only(left: 24),
              alignment: Alignment.centerLeft,
              child: const ArrowBack(),
            ),
          ),
          body: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ),
            child: ListView(
              children: [
                const YBox(30),
                const AuthTextSubTitle(
                  title: "We are sorry to see you go",
                  subtitle:
                      "Can you help us understand why you want to delete your account?",
                ),
                const YBox(24),
                ListView.separated(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemBuilder: (c, i) {
                      final msg = vm.deactivateAccountMessages[i];
                      return ReasonListTile(
                        isSelected: selected == i,
                        title: msg.reason,
                        subTitle: msg.description ?? '',
                        onTap: () {
                          selected = i;
                          vm.setSelectedReason(msg);
                        },
                      );
                    },
                    separatorBuilder: (ctx, i) => const YBox(16),
                    itemCount: vm.deactivateAccountMessages.length),
                const YBox(16),
                ReasonListTile(
                  isSelected: selected == -1,
                  subTitle: 'Others (Please Specify)',
                  onTap: () {
                    selected = -1;
                    vm.setSelectedReason(null);
                  },
                ),
                const YBox(150),
              ],
            ),
          ),
          bottomSheet: Container(
            padding: EdgeInsets.only(
              left: Sizer.width(24),
              right: Sizer.width(24),
              bottom: Sizer.height(30),
            ),
            color: AppColors.white,
            child: CustomBtn.solid(
              onTap: () {
                if (selected == -1) {
                  BsWrapper.bottomSheet(
                    context: context,
                    widget: const DeactivateAccountReasonSheet(),
                  );
                } else {
                  BsWrapper.bottomSheet(
                    context: context,
                    widget: const DeactivateAccountSheet(),
                  );
                }
              },
              online: selected != -2,
              text: "Continue",
            ),
          ),
        ),
      );
    });
  }
}

class ReasonListTile extends StatelessWidget {
  const ReasonListTile({
    Key? key,
    this.isSelected = false,
    this.title,
    required this.subTitle,
    this.onTap,
  }) : super(key: key);

  final bool isSelected;
  final String? title;
  final String subTitle;
  final Function()? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(14),
          vertical: Sizer.height(title != null ? 12 : 24),
        ),
        decoration: BoxDecoration(
          color: isSelected ? AppColors.blue100 : AppColors.blueFA,
          borderRadius: BorderRadius.circular(Sizer.radius(4)),
        ),
        child: Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (title != null)
                    Text(
                      title ?? '',
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                      style: AppTypography.text14.copyWith(
                        fontWeight: FontWeight.w500,
                        color: AppColors.black22,
                      ),
                    ),
                  if (title != null) const YBox(4),
                  Text(
                    subTitle,
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: AppTypography.text14.copyWith(
                      color: AppColors.black600,
                    ),
                  )
                ],
              ),
            ),
            const XBox(10),
            isSelected
                ? const Icon(
                    Icons.check_circle,
                    size: 26,
                    color: AppColors.baseGreen,
                  )
                : Container(
                    width: Sizer.width(20),
                    height: Sizer.height(20),
                    decoration: BoxDecoration(
                      // color: AppColors.white,
                      borderRadius: BorderRadius.circular(30),
                      border: Border.all(
                        color: AppColors.grayBF,
                        width: 1,
                      ),
                    ),
                  ),
          ],
        ),
      ),
    );
  }
}
