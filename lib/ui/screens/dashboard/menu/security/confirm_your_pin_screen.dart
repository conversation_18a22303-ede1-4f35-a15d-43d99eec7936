import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class ConfirmYourPinScreen extends StatefulWidget {
  const ConfirmYourPinScreen({Key? key}) : super(key: key);

  @override
  State<ConfirmYourPinScreen> createState() => _ConfirmYourPinScreenState();
}

class _ConfirmYourPinScreenState extends State<ConfirmYourPinScreen> {
  FocusNode pinFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    pinFocusNode.requestFocus();
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionPinVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: ArrowBack(
                      onTap: () {
                        Navigator.pop(context);
                        vm.pinConfirmC.clear();
                      },
                    ),
                  ),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Confirm Your PIN",
                    subtitle:
                        "As a last layer of security, set up a 4 digit PIN that you will use when making transfers",
                  ),
                  const YBox(30),
                  Center(
                    child: Pinput(
                      defaultPinTheme:
                          PinInputTheme.defaultPinTheme(borderRadius: 12),
                      errorPinTheme: PinInputTheme.errorPinTheme(),
                      followingPinTheme:
                          PinInputTheme.followPinTheme(borderRadius: 12),
                      length: 4,
                      controller: vm.pinConfirmC,
                      focusNode: pinFocusNode,
                      showCursor: true,
                      obscureText: true,
                      onChanged: (value) {
                        if (!vm.isPinMatched &&
                            vm.pinConfirmC.text.trim().length == 4) {
                          // vm.pinConfirmC.clear();
                        }
                        vm.reBuildUI();
                      },
                      onCompleted: (pin) {},
                    ),
                  ),
                  const YBox(10),
                  Visibility(
                    visible: !vm.isPinMatched &&
                        vm.pinConfirmC.text.trim().length == 4,
                    child: AnimatedOpacity(
                      duration: const Duration(milliseconds: 1000),
                      opacity: (!vm.isPinMatched &&
                              vm.pinConfirmC.text.trim().length == 4)
                          ? 1
                          : 0,
                      child: AnimatedContainer(
                        duration: const Duration(milliseconds: 1000),
                        curve: Curves.easeInOutCubic,
                        alignment: Alignment.center,
                        child: Text(
                          "Code doesn’t match",
                          style: AppTypography.text10.copyWith(
                            color: AppColors.alertRed,
                            fontWeight: FontWeight.w400,
                          ),
                        ),
                      ),
                    ),
                  ),
                  const Spacer(),
                  CustomBtn.solid(
                    onTap: () {
                      _setTransactionPin();
                    },
                    online: vm.isPinMatched && vm.pinConfirmC.text.length == 4,
                    text: "Continue",
                  ),
                  const YBox(60),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  _setTransactionPin() {
    var vm = context.read<TransactionPinVM>();
    vm.resetTransactionPin().then((value) {
      if (value.success) {
        vm.clearData();
        Navigator.pop(context);
        Navigator.pop(context);
        // _openSuccessSheet();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }
}
