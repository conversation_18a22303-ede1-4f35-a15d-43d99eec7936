// ignore_for_file: use_build_context_synchronously

import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class ChangeInfoOtpScreen extends StatefulWidget {
  const ChangeInfoOtpScreen({
    super.key,
    required this.arg,
  });

  final ChangeInfoArg arg;

  @override
  State<ChangeInfoOtpScreen> createState() => _ChangeInfoOtpScreenState();
}

class _ChangeInfoOtpScreenState extends State<ChangeInfoOtpScreen> {
  final pinC = TextEditingController();
  final pinFocusNode = FocusNode();
  bool isLoading = false;

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    pinC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<EmailPhoneChangeVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          appBar: NewCustomAppbar(
            showHeaderTitle: true,
            headerText: 'Confirm Email',
          ),
          body: Container(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                .copyWith(top: Sizer.height(10)),
            child: Column(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                                text: "Please enter the 6-digit code sent to ",
                                style: FontTypography.text16
                                    .withCustomColor(AppColors.gray93)
                                    .withCustomHeight(1.6)),
                            TextSpan(
                                text: widget.arg.isPhone
                                    ? "\n${widget.arg.dialCode} ${AppUtils.formatPhoneNumber(widget.arg.recipient)}"
                                    : "\n${widget.arg.recipient}",
                                style: FontTypography.text16
                                    .withCustomColor(AppColors.primaryBlue)
                                    .withCustomHeight(1.5)),
                          ],
                        ),
                      ),
                      const YBox(46),
                      Center(
                        child: Pinput(
                          defaultPinTheme:
                              PinInputTheme.changeDefaultPinTheme(),
                          followingPinTheme: PinInputTheme.changePinTheme(),
                          length: 6,
                          controller: pinC,
                          focusNode: pinFocusNode,
                          showCursor: true,
                          keyboardType: TextInputType.number,
                          onChanged: (value) => vm..reBuildUI(),
                          onCompleted: (pin) {
                            pinFocusNode.unfocus();
                            _verifyOtp();
                          },
                        ),
                      ),
                      // const YBox(24),
                      // ResendCode(
                      //   onResendCode: () {
                      //     // vm.requestOtp(otpType: OtpType.email);
                      //   },
                      // ),
                    ],
                  ),
                ),
                CustomBtn.withChild(
                  online: pinC.text.length == 6,
                  borderRadius: BorderRadius.circular(Sizer.radius(20)),
                  onTap: _verifyOtp,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Confirm OTP',
                        style: FontTypography.text15.medium
                            .withCustomColor(AppColors.white),
                      ),
                      const XBox(8),
                      const Icon(
                        Icons.arrow_forward,
                        color: AppColors.white,
                        size: 20,
                      ),
                    ],
                  ),
                ),
                const YBox(50),
              ],
            ),
          ),
        ),
      );
    });
  }

  void _verifyOtp() async {
    pinFocusNode.unfocus();
    final vm = context.read<EmailPhoneChangeVM>();
    final res = await vm.changeInfoVerification(ChangeInfoParams(
      code: pinC.text.trim(),
      recipient: widget.arg.isPhone
          ? "${widget.arg.dialCode}${widget.arg.recipient}"
          : widget.arg.recipient,
      accountChangeType: widget.arg.isPhone
          ? AccountChangeType.phone
          : AccountChangeType.email,
    ));

    handleApiResponse(
      response: res,
      onSuccess: () {
        _navigateOnSuccess();
      },
      onError: () {
        _navigateOnError();
      },
    );
  }

  _navigateOnError() {
    if (widget.arg.isPhone) {
      Navigator.pushNamed(
        context,
        RoutePath.changeConfirmationScreen,
        arguments: ChangeConfirmationArg(
          title: "Uh Oh!",
          subtitle: "Attempt unsuccessful!",
          desc:
              "Sorry we are unable to change your phone Number at this time, please try later",
          isSuccess: false,
        ),
      );
    } else {
      Navigator.pushNamed(
        context,
        RoutePath.changeConfirmationScreen,
        arguments: ChangeConfirmationArg(
          title: "Uh Oh!",
          subtitle: "Attempt unsuccessful!",
          desc:
              "Sorry we are unable to change your email at this time, please try later",
          isSuccess: false,
        ),
      );
    }
  }

  _navigateOnSuccess() {
    if (widget.arg.isPhone) {
      Navigator.pushNamed(
        context,
        RoutePath.changeConfirmationScreen,
        arguments: ChangeConfirmationArg(
          title: "Phone Number Changed",
          subtitle: "Successfully! ",
          desc: "Your Phone Number has been changed successfully",
          isSuccess: true,
        ),
      );
    } else {
      Navigator.pushNamed(
        context,
        RoutePath.changeConfirmationScreen,
        arguments: ChangeConfirmationArg(
          title: "Email Changed",
          subtitle: "Successfully! ",
          desc: "Your Email has been changed successfully",
          isSuccess: true,
        ),
      );
    }
  }
}
