import 'package:fl_country_code_picker/fl_country_code_picker.dart';
import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class NewPhoneNumberScreen extends StatefulWidget {
  const NewPhoneNumberScreen({super.key});

  @override
  State<NewPhoneNumberScreen> createState() => _NewPhoneNumberScreenState();
}

class _NewPhoneNumberScreenState extends State<NewPhoneNumberScreen> {
  final phoneC = TextEditingController();
  final focusNode = FocusNode();

  final countryPicker =
      FlCountryCodePicker(filteredCountries: ["CA", "NG", "GH", "KE", "UG"]);
  CountryCode selectedCountry =
      const CountryCode(code: 'CA', dialCode: '+1', name: 'Canada');

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    phoneC.dispose();
    focusNode.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: NewCustomAppbar(
        showHeaderTitle: true,
        headerText: 'New Phone Number',
        onBackBtnTap: () {
          Navigator.pop(context);
          Navigator.pop(context);
        },
      ),
      body: Stack(
        children: [
          ListView(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
              vertical: Sizer.height(10),
            ),
            children: [
              Text(
                "Your Korrency account information would be updated to this email",
                style: FontTypography.text16.withCustomColor(AppColors.gray93),
              ),
              YBox(30),
              Consumer<OnBoardVM>(builder: (context, vm, _) {
                return Row(
                  children: [
                    Expanded(
                      flex: 5,
                      child: CustomTextField(
                        labelText: "Country",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        isReadOnly: true,
                        suffixIcon: Icon(
                          Iconsax.lock,
                          color: AppColors.gray500,
                          size: Sizer.height(16),
                        ),
                        prefixIcon: InkWell(
                          onTap: () async {
                            final CountryCode? country =
                                await countryPicker.showPicker(
                              context: context,
                              pickerMaxHeight: Sizer.screenHeight * 0.6,
                            );

                            if (country != null) {
                              selectedCountry = country;
                              setState(() {});
                            }
                          },
                          child: Container(
                            padding: EdgeInsets.only(
                              left: Sizer.width(10),
                              right: Sizer.width(6),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(4),
                                  child: SizedBox(
                                    width: Sizer.width(20),
                                    height: Sizer.height(20),
                                    child: selectedCountry.flagImage,
                                  ),
                                ),
                                const XBox(4),
                                Text(
                                  selectedCountry.dialCode,
                                  style: FontTypography.text16
                                      .withCustomColor(AppColors.gray93),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                    ),
                    const XBox(10),
                    Expanded(
                      flex: 10,
                      child: CustomTextField(
                        controller: phoneC,
                        focusNode: focusNode,
                        labelText: "Phone Number",
                        keyboardType: KeyboardType.number,
                        inputFormatters: [
                          FilteringTextInputFormatter.digitsOnly,
                          LengthLimitingTextInputFormatter(10),
                        ],
                        showLabelHeader: true,
                        borderRadius: Sizer.height(12),
                        onChanged: (val) {
                          // if (val.trim().length > 10) {
                          //   phoneC.text = val.substring(0, 10);
                          // }
                          setState(() {});
                        },
                        onSubmitted: (val) {
                          focusNode.unfocus();
                        },
                      ),
                    ),
                  ],
                );
              }),
              YBox(40),
            ],
          ),
          Positioned(
            bottom: 50,
            left: 0,
            right: 0,
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              child: CustomBtn.withChild(
                borderRadius: BorderRadius.circular(Sizer.radius(20)),
                online: phoneC.text.trim().length >= 9,
                onTap: () async {
                  FocusScope.of(context).unfocus();
                  final res = await BsWrapper.bottomSheet(
                    context: context,
                    widget: ConfirmPhoneModal(
                      countryCode: selectedCountry.dialCode,
                      phoneNo: phoneC.text.trim(),
                    ),
                  );

                  if (res is bool && res) {
                    final vm = context.read<EmailPhoneChangeVM>();
                    final res = await vm.changeInfoReq(ChangeInfoParams(
                      code: "", // not needed here
                      accountChangeType: AccountChangeType.phone,
                      recipient:
                          "${selectedCountry.dialCode}${phoneC.text.trim()}",
                    ));
                    handleApiResponse(
                      response: res,
                      onSuccess: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.changeInfoOtpScreen,
                          arguments: ChangeInfoArg(
                            recipient: phoneC.text.trim(),
                            dialCode: selectedCountry.dialCode,
                            isPhone: true,
                          ),
                        );
                      },
                    );
                  }
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      'Continue',
                      style: FontTypography.text15.medium
                          .withCustomColor(AppColors.white),
                    ),
                    const XBox(8),
                    const Icon(
                      Icons.arrow_forward,
                      color: AppColors.white,
                      size: 20,
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
