import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class SetupYourPinScreen extends StatefulWidget {
  const SetupYourPinScreen({Key? key}) : super(key: key);

  @override
  State<SetupYourPinScreen> createState() => _SetupYourPinScreenState();
}

class _SetupYourPinScreenState extends State<SetupYourPinScreen> {
  FocusNode pinFocusNode = FocusNode();

  @override
  initState() {
    super.initState();
    pinFocusNode.requestFocus();
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<TransactionPinVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.white,
        body: Safe<PERSON>rea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  alignment: Alignment.centerLeft,
                  child: ArrowBack(
                    onTap: () {
                      Navigator.pop(context);
                      vm.pinC.clear();
                    },
                  ),
                ),
                const YBox(30),
                const AuthTextSubTitle(
                  title: "Setup PIN",
                  subtitle:
                      "As a last layer of security, set up a 4 digit PIN that you will use when making transfers",
                ),
                const YBox(30),
                Center(
                  child: Pinput(
                    defaultPinTheme:
                        PinInputTheme.defaultPinTheme(borderRadius: 12),
                    errorPinTheme: PinInputTheme.errorPinTheme(),
                    followingPinTheme:
                        PinInputTheme.followPinTheme(borderRadius: 12),
                    length: 4,
                    controller: vm.pinC,
                    focusNode: pinFocusNode,
                    showCursor: true,
                    obscureText: true,
                    obscuringWidget: Container(
                      padding: EdgeInsets.only(top: Sizer.height(8)),
                      child: Text('*', style: AppTypography.text36),
                    ),
                    onChanged: (value) {
                      vm.reBuildUI();
                      if (value.length == 4) {
                        pinFocusNode.unfocus();
                      }
                    },
                    onCompleted: (pin) {},
                  ),
                ),
                const YBox(10),
                const Spacer(),
                CustomBtn.solid(
                  onTap: () {
                    Navigator.pushNamed(
                        context, RoutePath.confirmYourPinScreen);
                  },
                  online: vm.pinC.text.length == 4,
                  text: "Continue",
                ),
                const YBox(60),
              ],
            ),
          ),
        ),
      );
    });
  }
}
