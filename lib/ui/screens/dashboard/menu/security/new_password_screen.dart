import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class NewPasswordScreen extends StatefulWidget {
  const NewPasswordScreen({Key? key}) : super(key: key);

  @override
  State<NewPasswordScreen> createState() => _NewPasswordScreenState();
}

class _NewPasswordScreenState extends State<NewPasswordScreen> {
  final FocusNode _passwordFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _passwordFocusNode);
  }

  @override
  void dispose() {
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: ArrowBack(
                  onTap: () {
                    context.read<PasskeyResetVM>().clearData();
                    Navigator.pop(context);
                  },
                ),
              ),
              const YBox(30),
              const AuthTextSubTitle(
                title: "Enter Your Password",
                subtitle: "Before you go on, we need your password to continue",
              ),
              Expanded(
                child: Consumer<PasskeyResetVM>(builder: (context, vm, _) {
                  return Column(
                    children: [
                      const YBox(30),
                      CustomTextField(
                        labelText: "Password",
                        focusNode: _passwordFocusNode,
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        controller: vm.oldPasswordC,
                        isPassword: true,
                        onChanged: (val) => vm.reBuildUI(),
                      ),
                      const Spacer(),
                      CustomBtn.solid(
                        onTap: () {
                          _passwordFocusNode.unfocus();
                          Navigator.pushNamed(
                              context, RoutePath.changePasswordScreen);
                        },
                        online: vm.oldPasswordC.text.trim().isNotEmpty,
                        text: "Continue",
                      ),
                      const YBox(50)
                    ],
                  );
                }),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
