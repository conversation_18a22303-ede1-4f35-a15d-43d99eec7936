import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class ChangePasswordScreen extends StatefulWidget {
  const ChangePasswordScreen({super.key});

  @override
  State<ChangePasswordScreen> createState() => _ChangePasswordScreenState();
}

class _ChangePasswordScreenState extends State<ChangePasswordScreen> {
  final FocusNode _passFocusNode = FocusNode();
  final FocusNode _confirmPassFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _passFocusNode);
  }

  _unFocus() {
    _passFocusNode.unfocus();
    _confirmPassFocusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PasskeyResetVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            bottom: false,
            child: Container(
              height: Sizer.screenHeight,
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
                // bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    child: ArrowBack(
                      onTap: () {
                        vm.clearData();
                        Navigator.pop(context);
                      },
                    ),
                  ),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Change Your Password",
                    subtitle: "You need a password to protect your account.",
                  ),
                  Expanded(
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        const YBox(30),
                        CustomTextField(
                          labelText: "Password",
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          controller: vm.passwordC,
                          isPassword: true,
                          onChanged: (val) {
                            vm.validatePassword(vm.passwordC.text);
                            vm.passwordMatch(
                                vm.passwordC.text, vm.passwordConfirmC.text);
                          },
                          onSubmitted: (p0) {
                            _passFocusNode.unfocus();
                            FocusScope.of(context)
                                .requestFocus(_confirmPassFocusNode);
                          },
                        ),
                        const YBox(8),
                        Column(children: [
                          ValidationItemWidget(
                            label: "At least 8 characters long",
                            isValid: vm.validatorStatus.hasAtleast8Character,
                          ),
                          const YBox(8),
                          ValidationItemWidget(
                            label: "At least 1 capital letter",
                            isValid: vm.validatorStatus.containsUpperCase,
                          ),
                          const YBox(8),
                          ValidationItemWidget(
                            label: "At least 1 number",
                            isValid: vm.validatorStatus.containsANumber,
                          ),
                          const YBox(8),
                          ValidationItemWidget(
                            label: "At least 1 special character @ # \$ %r",
                            isValid:
                                vm.validatorStatus.containsSpecialCharacter,
                          ),
                        ]),
                        const YBox(24),
                        CustomTextField(
                          labelText: "Confirm Password",
                          focusNode: _confirmPassFocusNode,
                          showLabelHeader: true,
                          borderRadius: Sizer.height(4),
                          controller: vm.passwordConfirmC,
                          errorText: vm.passWordDontMatch
                              ? 'Passwords do not match'
                              : null,
                          isPassword: true,
                          onChanged: (val) => vm.passwordMatch(
                              vm.passwordC.text, vm.passwordConfirmC.text),
                          onSubmitted: (_) {
                            _unFocus();
                          },
                        ),
                        const YBox(200),
                        CustomBtn.solid(
                          onTap: () {
                            _unFocus();
                            _changePassword();
                          },
                          online: vm.enableSavePasswordBtn(),
                          text: "Continue",
                        ),
                        const YBox(200),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  _changePassword() {
    context.read<PasskeyResetVM>().changePassword().then((value) {
      if (value.success) {
        _gotoSuccessScreen();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _gotoSuccessScreen() {
    Navigator.pushReplacementNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: "Password Reset Successful",
        btnText: "Continue",
        btnTap: () {
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
          Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
        },
      ),
    );
  }
}
