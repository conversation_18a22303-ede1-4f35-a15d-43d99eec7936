import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/helpsupport/create_freshdesk_ticket_webview.dart';
import 'package:store_redirect/store_redirect.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutUsScreen extends StatelessWidget {
  const AboutUsScreen({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: Sizer.width(24),
          ).copyWith(
            top: Sizer.height(20),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                alignment: Alignment.centerLeft,
                child: const ArrowBack(),
              ),
              const YBox(30),
              const AuthTextSubTitle(
                title: "About Korrency",
                subtitle: "Need assistance? We're here for you!",
              ),
              const YBox(32),
              MenuListTile(
                title: 'Our Story',
                iconData: Iconsax.sms,
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.createFreshDeskTicketWebview,
                    arguments: WebViewArg(
                      // appBarText: "Our Story",
                      webURL: AppUtils.korrencyAbout,
                    ),
                  );
                },
              ),
              const YBox(26),
              MenuListTile(
                title: 'Terms and Conditions',
                iconData: Iconsax.document_1,
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.createFreshDeskTicketWebview,
                    arguments: WebViewArg(
                      // appBarText: "Terms and Conditions",
                      webURL: AppUtils.korrencyTermsAndCondition,
                    ),
                  );
                },
              ),
              const YBox(26),
              MenuListTile(
                title: 'Privacy Policy',
                iconData: Iconsax.messages_2,
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.createFreshDeskTicketWebview,
                    arguments: WebViewArg(
                      // appBarText: "Privacy Policy",
                      webURL: AppUtils.korrencyPolicy,
                    ),
                  );
                },
              ),
              const YBox(26),
              MenuListTile(
                title: 'Our Blog',
                iconData: Iconsax.camera,
                onPressed: () {
                  Navigator.pushNamed(
                    context,
                    RoutePath.createFreshDeskTicketWebview,
                    arguments: WebViewArg(
                      // appBarText: "Our Blog",
                      webURL: AppUtils.korrencyBlog,
                    ),
                  );
                },
              ),
              const YBox(26),
              MenuListTile(
                title: 'Rate Our App',
                iconData: Iconsax.star,
                onPressed: () {
                  printty("Rate Our App");

                  StoreRedirect.redirect(
                    androidAppId: "korrency.mobile.com",
                    iOSAppId: "6495368627",
                  );
                },
              ),
              const YBox(26),
              MenuListTile(
                title: 'Drop a Feedback',
                iconData: Iconsax.sms_edit,
                onPressed: () {},
              ),
              const Spacer(),
              Consumer<ConfigVM>(builder: (context, vm, _) {
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    CurrencyCard(
                      svgPath: AppSvgs.fb,
                      title: "Facebook",
                      bgColor: AppColors.litGrey,
                      onTap: () {
                        AppUtils.launchURL(
                          url: vm.faceboookURL ?? "",
                          launchMode: LaunchMode.externalApplication,
                        );
                      },
                    ),
                    CurrencyCard(
                      svgPath: AppSvgs.ig,
                      title: "Instagram",
                      bgColor: AppColors.opacityRed100,
                      onTap: () {
                        AppUtils.launchURL(
                          url: vm.instagramURL ?? "",
                          launchMode: LaunchMode.externalApplication,
                        );
                      },
                    ),
                    CurrencyCard(
                      svgPath: AppSvgs.x,
                      title: "X (Twitter)",
                      onTap: () {
                        AppUtils.launchURL(
                          url: vm.twitterURL ?? "",
                          launchMode: LaunchMode.externalApplication,
                        );
                      },
                    ),
                    CurrencyCard(
                      svgPath: AppSvgs.lk,
                      title: "LinkedIn",
                      onTap: () {
                        AppUtils.launchURL(
                          url: vm.linkedInURL ?? "",
                          launchMode: LaunchMode.externalApplication,
                        );
                      },
                    ),
                  ],
                );
              }),
              const YBox(50),
            ],
          ),
        ),
      ),
    );
  }
}
