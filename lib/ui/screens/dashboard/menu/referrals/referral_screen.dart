import 'package:dotted_border/dotted_border.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ReferralScreen extends StatefulWidget {
  const ReferralScreen({super.key});

  @override
  State<ReferralScreen> createState() => _ReferralScreenState();
}

class _ReferralScreenState extends State<ReferralScreen> {
  ReferralType _referralType = ReferralType.claimed;

  @override
  void initState() {
    MixpanelService().trackScreen("Referral Page Viewed");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      printty("getReferralEarnings heelo called");
      _callRefs();
    });

    super.initState();
  }

  _callRefs() {
    context.read<ReferralVM>()
      ..getReferralEarnings()
      ..getReferrals();
  }

  _showRatingPrompt() {
    BsWrapper.bottomSheet(
      context: context,
      widget: const RateExperienceModal(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<ReferralVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          body: SafeArea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ),
              child: RefreshIndicator(
                onRefresh: () async {
                  _callRefs();
                },
                child: ListView(
                  shrinkWrap: true,
                  children: [
                    const YBox(20),
                    const CustomHeader(
                      // showBackBtn: true,
                      showHeader: true,
                      headerText: 'Referrals',
                    ),
                    const YBox(26),
                    DottedBorder(
                      dashPattern: const [8, 5],
                      strokeWidth: 2,
                      borderType: BorderType.RRect,
                      radius: const Radius.circular(4),
                      color: AppColors.dottedColor,
                      // padding: EdgeInsets.symmetric(
                      //   horizontal: Sizer.width(16),
                      //   vertical: Sizer.height(18),
                      // ),
                      child: SizedBox(
                        width: Sizer.screenWidth,
                        child: Column(
                          children: [
                            Container(
                              padding: EdgeInsets.symmetric(
                                horizontal: Sizer.width(16),
                                vertical: Sizer.height(18),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  _columnTextTile(
                                    title: 'Bonuses Earned',
                                    subtitle:
                                        '\$${vm.refEarnings?.bonusesEarned ?? 0} CAD',
                                  ),
                                  _columnTextTile(
                                      title: 'Claimed Bonuses',
                                      subtitle:
                                          '\$${vm.refEarnings?.claimedBonuses ?? 0} CAD',
                                      crossAxisAlignment:
                                          CrossAxisAlignment.end),
                                ],
                              ),
                            ),
                            InkWell(
                              onTap: () async {
                                if (vm.refEarnings
                                        ?.showEnabledClaimBonusesButton ??
                                    false) {
                                  printty("claimRefBonus called");
                                  final res = await vm.claimRefBonus();

                                  handleApiResponse(
                                    response: res,
                                    onSuccess: () async {
                                      _callRefs();
                                      final res = await context
                                          .read<RatingVm>()
                                          .getEligibility();
                                      if (res.success) {
                                        if (context
                                                .read<RatingVm>()
                                                .eligibilityModel
                                                ?.eligibility ??
                                            false) {
                                          _showRatingPrompt();
                                        }
                                      }
                                    },
                                  );
                                }
                                return;
                              },
                              child: Container(
                                width: Sizer.screenWidth,
                                padding: EdgeInsets.symmetric(
                                  horizontal: Sizer.width(16),
                                  vertical: Sizer.width(10),
                                ),
                                decoration: BoxDecoration(
                                    color: AppColors.dividerColor,
                                    borderRadius: BorderRadius.circular(4)),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Text(
                                      (vm.refEarnings?.showClaimedButton ??
                                              false)
                                          ? "Claimed"
                                          : "Claim Bonuses",
                                      style: AppTypography.text16.copyWith(
                                          fontWeight: FontWeight.w500,
                                          color: (vm.refEarnings
                                                      ?.showClaimedButton ??
                                                  false)
                                              ? AppColors.iconGreen
                                              : AppColors.baseBlack),
                                    ),
                                    if (vm.refEarnings?.showClaimedButton ??
                                        false)
                                      const XBox(6),
                                    if (vm.refEarnings?.showClaimedButton ??
                                        false)
                                      const Icon(
                                        Icons.check_circle,
                                        size: 20,
                                        color: AppColors.iconGreen,
                                      ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // CustomBtn.solid(
                    //   onTap: () {
                    //     printty("claimRefBonus called");
                    //     vm.claimRefBonus().then((value) {
                    //       if (value.success) {
                    //         FlushBarToast.fLSnackBar(
                    //           message: value.message.toString(),
                    //           backgroundColor: AppColors.baseGreen,
                    //         );
                    //       } else {
                    //         FlushBarToast.fLSnackBar(
                    //           message: value.message.toString(),
                    //           backgroundColor: AppColors.red,
                    //         );
                    //       }
                    //     });
                    //   },
                    //   online: false,
                    //   height: 44,
                    //   text: "Claim Bonuses",
                    // ),
                    const YBox(26),
                    Row(
                      children: [
                        Expanded(
                          child: TextRow(
                            text: "Complete Invites",
                            value: vm.completedRefList.length.toString(),
                            hasValue: true,
                            isSelected: _referralType == ReferralType.claimed,
                            onTap: () => setState(() {
                              _referralType = ReferralType.claimed;
                            }),
                          ),
                        ),
                        const XBox(16),
                        Expanded(
                          child: TextRow(
                            text: "Pending Invites",
                            value: vm.pendingRefList.length.toString(),
                            hasValue: true,
                            isSelected: _referralType == ReferralType.pending,
                            onTap: () => setState(() {
                              _referralType = ReferralType.pending;
                            }),
                          ),
                        )
                      ],
                    ),
                    const YBox(26),
                    if (_referralType == ReferralType.claimed)
                      Builder(builder: (context) {
                        if (vm.completedRefList.isEmpty) {
                          return const EmptyReferrals();
                        }
                        return ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, i) {
                            Referral claimedRef = vm.completedRefList[i];
                            return RefListTile(
                              name: claimedRef.referredUserFullName ?? '',
                              status: claimedRef.status ?? '',
                              isClaimed: true,
                              onTap: () {},
                            );
                          },
                          separatorBuilder: (context, index) => const YBox(30),
                          itemCount: vm.completedRefList.length,
                        );
                      }),
                    if (_referralType == ReferralType.pending)
                      Builder(builder: (context) {
                        if (vm.pendingRefList.isEmpty) {
                          return const EmptyReferrals();
                        }
                        return ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (context, i) {
                            Referral pendingRef = vm.pendingRefList[i];
                            return RefListTile(
                              name: pendingRef.referredUserFullName ?? '',
                              isClaimed: false,
                              status: pendingRef.status ?? '',
                              onTap: () {},
                            );
                          },
                          separatorBuilder: (context, index) => const YBox(30),
                          itemCount: vm.pendingRefList.length,
                        );
                      }),
                  ],
                ),
              ),
            ),
          ),
        ),
      );
    });
  }

  Column _columnTextTile({
    required String title,
    required String subtitle,
    CrossAxisAlignment? crossAxisAlignment,
  }) {
    return Column(
      crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: AppTypography.text14.copyWith(
            color: AppColors.gray500,
          ),
        ),
        const YBox(4),
        Text(
          subtitle,
          style: AppTypography.text20.copyWith(
            fontWeight: FontWeight.w700,
            color: AppColors.textBlue700,
          ),
        ),
      ],
    );
  }
}

class EmptyReferrals extends StatelessWidget {
  const EmptyReferrals({
    super.key,
    this.title,
  });

  final String? title;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.height(300),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            imageHelper(
              AppImages.phoneUser,
              height: Sizer.height(236),
            ),
            Text(
              title ?? "Invite others to join the platform",
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w400,
                color: AppColors.textBlack600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class RefListTile extends StatelessWidget {
  const RefListTile({
    super.key,
    required this.name,
    this.avater,
    this.isClaimed = false,
    required this.status,
    this.onTap,
  });

  final String name;
  final String? avater;
  final bool isClaimed;
  final String status;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        children: [
          Container(
            height: Sizer.height(36),
            width: Sizer.width(36),
            decoration: BoxDecoration(
              color: AppColors.primaryLightBlue,
              borderRadius: BorderRadius.circular(30),
              image: const DecorationImage(
                image: AssetImage(
                  AppImages.user,
                ),
              ),
            ),
          ),
          const XBox(12),
          Text(
            name,
            style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w500, color: AppColors.textBlack100),
          ),
          const Spacer(),
          Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(6),
              vertical: Sizer.height(2),
            ),
            decoration: BoxDecoration(
              color: isClaimed ? AppColors.opacityGreen : AppColors.litGrey,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              status,
              style: AppTypography.text12.copyWith(
                color: isClaimed ? AppColors.iconGreen : AppColors.primaryBlue,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
