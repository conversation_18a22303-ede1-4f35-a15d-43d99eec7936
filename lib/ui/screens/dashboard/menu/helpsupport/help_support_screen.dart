import 'package:freshchat_sdk/freshchat_sdk.dart';
import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/helpsupport/create_freshdesk_ticket_webview.dart';

class HelpSupportScreen extends StatelessWidget {
  const HelpSupportScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.white,
      appBar: CustomAppbar(),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const YBox(30),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
            child: const AuthTextSubTitle(
              title: "Help and Support",
              subtitle: "Need assistance? We're here for you!",
            ),
          ),
          // const YBox(30),
          // Padding(
          //   padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
          //   child: Row(
          //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
          //     children: [
          //       Text("Watch Videos",
          //           style: AppTypography.text14.copyWith(
          //             fontWeight: FontWeight.w700,
          //           )),
          //       InkWell(
          //         onTap: () {
          //           Navigator.pushNamed(
          //             context,
          //             RoutePath.helpVideoScreen,
          //           );
          //         },
          //         child: Text(
          //           "View All",
          //           style: AppTypography.text14.copyWith(
          //             fontWeight: FontWeight.w500,
          //             color: AppColors.textBlack800,
          //           ),
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // const YBox(16),
          // SingleChildScrollView(
          //   scrollDirection: Axis.horizontal,
          //   child: Row(
          //     children: List.generate(
          //       4,
          //       (i) => Row(
          //         mainAxisSize: MainAxisSize.min,
          //         children: [
          //           XBox(i == 0 ? 24 : 16),
          //           HelpVideoCard(
          //             width: Sizer.width(161),
          //             onTap: () {
          //               Navigator.pushNamed(
          //                 context,
          //                 RoutePath.videoPlayerScreen,
          //                 arguments: VideoPlayerArg(
          //                   url:
          //                       "https://res.cloudinary.com/sbsc/video/upload/v1743064879/Post/tmp/26254_2025-03-27_1743064878.mp4",
          //                   title: "How to use Korrency",
          //                 ),
          //               );
          //             },
          //           ),
          //           XBox(i == 3 ? 24 : 0),
          //         ],
          //       ),
          //     ),
          //   ),
          // ),
          const YBox(20),
          Expanded(
            child: Container(
              width: Sizer.screenWidth,
              color: AppColors.white,
              child: ListView(
                padding: EdgeInsets.only(
                  top: Sizer.height(24),
                  bottom: Sizer.height(24),
                  left: Sizer.width(24),
                  right: Sizer.width(24),
                ),
                children: [
                  MenuListTile(
                    title: 'FAQs',
                    iconData: Iconsax.sms,
                    onPressed: () {
                      Navigator.pushNamed(
                        context,
                        RoutePath.createFreshDeskTicketWebview,
                        arguments: WebViewArg(
                          webURL: AppUtils.korrencyFAQ,
                        ),
                      );
                    },
                  ),
                  const YBox(20),
                  MenuListTile(
                    title: 'Chat with us',
                    iconData: Iconsax.messages_2,
                    onPressed: () {
                      context.read<FreshChatVM>()
                        ..initializeFreshchat()
                        ..configureFreshchatUser(
                            context.read<AuthUserVM>().user);
                      Future.delayed(const Duration(seconds: 1), () {
                        Freshchat.showConversations();
                      });
                      // Freshchat.showConversations();
                    },
                  ),
                  const YBox(20),
                  MenuListTile(
                    title: 'Get Help',
                    iconData: Iconsax.camera,
                    onPressed: () {
                      Navigator.pushNamed(
                          context, RoutePath.reactOutToUsScreen);
                    },
                  ),
                  const YBox(20),
                  MenuListTile(
                    title: 'Report a Transaction',
                    iconData: Iconsax.camera,
                    onPressed: () {
                      Navigator.pushNamed(
                        context,
                        RoutePath.createFreshDeskTicketWebview,
                        arguments: WebViewArg(
                          webURL: AppUtils.korrencyCreateTicket,
                        ),
                      );
                    },
                  ),
                  const YBox(20),
                ],
              ),
            ),
          ),
          const YBox(30),
        ],
      ),
    );
  }
}
