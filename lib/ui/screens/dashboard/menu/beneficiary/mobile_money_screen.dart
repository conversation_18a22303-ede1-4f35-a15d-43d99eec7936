import 'package:flutter/cupertino.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';
import 'package:korrency/ui/screens/dashboard/menu/beneficiary/bank_transfer_screen.dart';

class MobileMoneyScreen extends StatefulWidget {
  const MobileMoneyScreen({
    super.key,
    required this.arg,
  });

  final BeneficiaryPaymentArg arg;

  @override
  State<MobileMoneyScreen> createState() => _MobileMoneyScreenState();
}

class _MobileMoneyScreenState extends State<MobileMoneyScreen> {
  final operatorC = TextEditingController();
  final mobileNumberC = TextEditingController();
  final recipientFirstNameC = TextEditingController();
  final recipientLastNameC = TextEditingController();

  final operatorF = FocusNode();
  final mobileNumberF = FocusNode();
  final recipientFirstNameF = FocusNode();
  final recipientLastNameF = FocusNode();

  bool hasStartedVerification = false;
  bool isMobileNumberValid = false;
  bool mobileNumberHasBeenTapped = false;
  MobileMoney? selectedMobileMoney;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final bankVm = context.read<BankVM>();
      bankVm.getMNO(widget.arg.currency.id ?? 0);
    });
  }

  @override
  void dispose() {
    operatorC.dispose();
    mobileNumberC.dispose();
    recipientFirstNameC.dispose();
    recipientLastNameC.dispose();

    operatorF.dispose();
    mobileNumberF.dispose();
    recipientFirstNameF.dispose();
    recipientLastNameF.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BeneficiaryVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            body: SafeArea(
              bottom: false,
              child: Column(
                children: [
                  const YBox(20),
                  Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(24),
                    ),
                    child: CustomHeader(
                      showBackBtn: true,
                      showHeader: true,
                      onBackBtnTap: () {
                        // vm.setMobileMoney(null);
                        Navigator.pop(context);
                      },
                      headerText: 'Mobile Money',
                    ),
                  ),
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(24),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const YBox(24),
                                CustomTextField(
                                  controller: operatorC,
                                  focusNode: operatorF,
                                  labelText: "Select Operator",
                                  showLabelHeader: true,
                                  isReadOnly: true,
                                  hintText: 'Select Operator',
                                  borderRadius: Sizer.height(4),
                                  showSuffixIcon: true,
                                  suffixIcon: Icon(
                                    Icons.expand_more,
                                    size: Sizer.radius(25),
                                  ),
                                  onTap: () async {
                                    final res = await BsWrapper.bottomSheet(
                                      context: context,
                                      widget: MobileMoneySheet(
                                        title: operatorC.text.trim(),
                                      ),
                                    );
                                    if (res is MobileMoney) {
                                      operatorC.text = res.name ?? '';
                                      selectedMobileMoney = res;
                                      if (mobileNumberC.text.length > 9 &&
                                          widget.arg.paymentMethod.nameCheck ==
                                              true) {
                                        verifyMobileWalletByCurrecyId();
                                      }
                                      setState(() {});
                                    }
                                  },
                                ),
                                const YBox(24),
                                CustomTextField(
                                  focusNode: mobileNumberF,
                                  controller: mobileNumberC,
                                  labelText: "Mobile Number",
                                  showLabelHeader: true,
                                  isReadOnly: operatorC.text.trim().isEmpty,
                                  keyboardType: KeyboardType.number,
                                  hintText: '**********',
                                  borderRadius: Sizer.height(4),
                                  errorText: mobileNumberHasBeenTapped &&
                                          operatorC.text.trim().isEmpty
                                      ? 'Please select operator'
                                      : null,
                                  suffixIcon: vm.busy(verifyingBankState)
                                      ? const CupertinoActivityIndicator()
                                      : null,
                                  onChanged: (val) {
                                    final nameCheck =
                                        widget.arg.paymentMethod.nameCheck;
                                    hasStartedVerification = false;

                                    if (val.trim().length > 9 &&
                                        nameCheck == true) {
                                      verifyMobileWalletByCurrecyId();
                                    }
                                    setState(() {});
                                  },
                                  onTap: () {
                                    if (operatorC.text.trim().isEmpty) {
                                      mobileNumberHasBeenTapped = true;
                                      setState(() {});
                                    }
                                  },
                                ),
                                const YBox(4),
                                if (hasStartedVerification &&
                                    widget.arg.paymentMethod.nameCheck == true)
                                  Row(
                                    children: [
                                      Text(
                                        isMobileNumberValid
                                            ? 'Active'
                                            : 'Not active',
                                        style: AppTypography.text12.copyWith(
                                          color: isMobileNumberValid
                                              ? AppColors.baseGreen
                                              : AppColors.red,
                                        ),
                                      ),
                                      const XBox(4),
                                      Icon(
                                        isMobileNumberValid
                                            ? Icons.check_circle
                                            : Icons.error,
                                        size: Sizer.radius(13),
                                        color: isMobileNumberValid
                                            ? AppColors.baseGreen
                                            : AppColors.red,
                                      ),
                                    ],
                                  ),
                                const YBox(20),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Expanded(
                                      child: CustomTextField(
                                        focusNode: recipientFirstNameF,
                                        controller: recipientFirstNameC,
                                        labelText: "Recipient",
                                        showLabelHeader: true,
                                        hintText: 'First Name',
                                        borderRadius: Sizer.height(4),
                                        onChanged: (p0) => setState(() {}),
                                      ),
                                    ),
                                    const XBox(10),
                                    Expanded(
                                      child: CustomTextField(
                                        focusNode: recipientLastNameF,
                                        controller: recipientLastNameC,
                                        hintText: 'Last Name',
                                        borderRadius: Sizer.height(4),
                                        onChanged: (p0) => setState(() {}),
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          const YBox(70),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
            bottomSheet: Container(
              color: AppColors.white,
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                bottom: Sizer.height(30),
              ),
              child: CustomBtn.solid(
                onTap: () async {
                  // vm.setMobileMoney(MobileMoney(
                  //   uuid: selectedMobileMoney?.uuid ?? '',
                  //   name: selectedMobileMoney?.name ?? '',
                  //   mobileNumber: mobileNumberC.text.trim(),
                  //   recipientName:
                  //       '${recipientFirstNameC.text.trim()} ${recipientLastNameC.text.trim()}',
                  // ));
                  // Navigator.pushNamed(
                  //   context,
                  //   RoutePath.reviewScreen,
                  // );

                  final result = await BsWrapper.bottomSheet(
                    context: context,
                    widget: BeneficiaryScamSheet(),
                  );
                  if (result is bool && result) {
                    _saveBeneficiary();
                  }
                },
                online: recipientFirstNameC.text.trim().isNotEmpty &&
                    recipientLastNameC.text.trim().isNotEmpty &&
                    mobileNumberC.text.trim().isNotEmpty &&
                    operatorC.text.trim().isNotEmpty &&
                    (widget.arg.paymentMethod.nameCheck == true
                        ? isMobileNumberValid
                        : true),
                text: "Continue",
              ),
            ),
          ),
        );
      },
    );
  }

  void verifyMobileWalletByCurrecyId() {
    final vm = context.read<BeneficiaryVM>();
    vm
        .verifyMobileWalletByCurrecyId(
      currencyId: widget.arg.currency.id ?? 0,
      destinationNumber: mobileNumberC.text,
    )
        .then((v) {
      hasStartedVerification = true;
      isMobileNumberValid = (v.data ?? '').toString().toLowerCase() == 'active';
    });
  }

  _saveBeneficiary() {
    var beneficiaryVM = context.read<BeneficiaryVM>();
    PostBeneficiaryArgs arg = PostBeneficiaryArgs(
      currencyId: widget.arg.currency.id ?? 0,
      institutionName: selectedMobileMoney?.name ?? '',
      // accountName: selectedMobileMoney?.name ?? '',
      accountIdentifier: mobileNumberC.text.trim(),
      institutionCode: selectedMobileMoney?.uuid ?? '',
      firstName: recipientFirstNameC.text.trim(),
      lastName: recipientLastNameC.text.trim(),
      transferMethod: TransferMethod.mobileMoney,
    );

    beneficiaryVM
        .saveBeneficiary(
      arg: arg,
      transferMethodType: TransferMethodType.mobileMoney,
    )
        .then((value) {
      if (value.success) {
        recipientFirstNameC.clear();
        recipientLastNameC.clear();
        context.read<BankVM>().resetData();
        beneficiaryVM
          ..clearData()
          ..getBeneficiaries();
        _showComfirmationScreen(msg: value.message.toString());
      } else {
        _showComfirmationScreen(
          isFailed: true,
          msg: value.message.toString(),
        );
      }
    });
  }

  _showComfirmationScreen({String? msg, bool isFailed = false}) {
    printty("showing confirmation screen $msg");
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ??
            (isFailed
                ? "Beneficiary Saved\n Failed"
                : "Beneficiary Saved\n Successfully"),
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          _pop();
          if (!isFailed) {
            _pop();
            _pop();
            _pop();
            Navigator.pushNamed(
              NavigatorKeys.appNavigatorKey.currentContext!,
              RoutePath.beneficiaryScreen,
            );
          }
        },
      ),
    );
  }

  _pop() {
    Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
  }
}
