import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/beneficiary/bank_transfer_screen.dart';

class BeneficiaryPreferredMethodScreen extends StatelessWidget {
  const BeneficiaryPreferredMethodScreen({
    super.key,
    required this.currency,
  });

  final Currency currency;

  @override
  Widget build(BuildContext context) {
    return Consumer<WalletVM>(builder: (context, vm, _) {
      return Scaffold(
        backgroundColor: AppColors.bgWhite,
        body: SafeArea(
          bottom: false,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
            ).copyWith(
              top: Sizer.height(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomHeader(
                  showHeader: true,
                  headerText: 'New Beneficiary',
                  onBackBtnTap: () {
                    Navigator.pop(context);
                  },
                ),
                const YBox(30),
                CustomListViews.view(
                  icon: Iconsax.bank,
                  title: "Bank Account",
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      RoutePath.bankTransferScreen,
                      arguments: BeneficiaryPaymentArg(
                        currency: currency,
                        paymentMethod:
                            (currency.paymentMethods ?? []).firstWhere(
                          (method) => method.id == TransferMethod.bankTransfer,
                        ),
                      ),
                    );
                  },
                ),
                const YBox(24),
                CustomListViews.view(
                  icon: Iconsax.wallet,
                  title: "Mobile Wallet",
                  onTap: () {
                    Navigator.pushNamed(
                      context,
                      RoutePath.mobileMoneyScreen,
                      arguments: BeneficiaryPaymentArg(
                        currency: currency,
                        paymentMethod:
                            (currency.paymentMethods ?? []).firstWhere(
                          (method) => method.id == TransferMethod.mobileMoney,
                        ),
                      ),
                    );
                  },
                )
              ],
            ),
          ),
        ),
      );
    });
  }
}
