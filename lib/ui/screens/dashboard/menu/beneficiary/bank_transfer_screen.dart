import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';

class BeneficiaryPaymentArg {
  final Currency currency;
  final PaymentMethod paymentMethod;

  BeneficiaryPaymentArg({
    required this.currency,
    required this.paymentMethod,
  });
}

class BankTransferScreen extends StatefulWidget {
  const BankTransferScreen({
    super.key,
    required this.arg,
  });

  final BeneficiaryPaymentArg arg;

  @override
  State<BankTransferScreen> createState() => _BankTransferScreenState();
}

class _BankTransferScreenState extends State<BankTransferScreen> {
  final recipientFirstNameC = TextEditingController();
  final recipientLastNameC = TextEditingController();

  final FocusNode recipientFirstNameF = FocusNode();
  final FocusNode recipientLastNameF = FocusNode();
  final FocusNode accountNumFocus = FocusNode();
  final FocusNode fNameFocusNode = FocusNode();
  final FocusNode lNameFocusNode = FocusNode();
  final FocusNode emailFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _init();
    });
  }

  @override
  void dispose() {
    recipientFirstNameC.dispose();
    recipientLastNameC.dispose();

    recipientFirstNameF.dispose();
    recipientLastNameF.dispose();
    accountNumFocus.dispose();
    fNameFocusNode.dispose();
    lNameFocusNode.dispose();
    emailFocusNode.dispose();

    super.dispose();
  }

  _init() {
    final bankVm = context.read<BankVM>();
    bankVm.getBanksByCurrencyId(widget.arg.currency.id ?? 0);
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<BeneficiaryVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.white,
          body: SafeArea(
            child: Container(
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(24),
              ).copyWith(
                top: Sizer.height(20),
              ),
              child: Column(
                children: [
                  CustomHeader(
                    showHeader: true,
                    headerText: 'New Beneficiary',
                    onBackBtnTap: () {
                      vm.clearData();
                      context.read<BankVM>().resetData();
                      Navigator.pop(context);
                    },
                  ),
                  const YBox(40),
                  Column(
                    children: [
                      CustomTextField(
                        labelText: "Bank",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        hintText: 'Select Bank',
                        isReadOnly: true,
                        onTap: () {
                          BsWrapper.bottomSheet(
                              context: context, widget: const BankSheet());
                        },
                        suffixIcon: Icon(
                          Icons.expand_more,
                          size: Sizer.radius(25),
                          color: AppColors.gray500,
                        ),
                        controller: vm.bankNameC,
                        onChanged: (val) {},
                      ),
                      const YBox(24),
                      CustomTextField(
                        focusNode: accountNumFocus,
                        labelText: "Account Number",
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        hintText: '**********',
                        keyboardType: KeyboardType.number,
                        controller: vm.accountNumC,
                        onChanged: (val) {
                          if (val.trim().length >= 9 &&
                              widget.arg.paymentMethod.nameCheck == true) {
                            accountNumFocus.unfocus();
                            vm
                                .verifyBankAcct(
                              bankId: vm.bankUUID ?? '',
                              accountNum: vm.accountNumC.text,
                              amount: "100",
                            )
                                .then((value) {
                              if (!value.success) {
                                FlushBarToast.fLSnackBar(
                                  message: value.message ??
                                      'Unable to verify account, kindly try again',
                                );
                              }
                            });
                          }
                        },
                      ),
                      const YBox(24),
                      // CustomTextField(
                      //   labelText: "Account Name",
                      //   isReadOnly: true,
                      //   fillColor: AppColors.litGrey100,
                      //   showLabelHeader: true,
                      //   borderRadius: Sizer.height(4),
                      //   // hintText: 'RONE-ORUGBOH AJORITSEDERE',
                      //   hideBorder: true,
                      //   controller: vm.accountNameC,
                      //   onChanged: (val) => vm.reBuildUI(),
                      // ),

                      Row(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Expanded(
                            child: CustomTextField(
                              focusNode: recipientFirstNameF,
                              controller: recipientFirstNameC,
                              labelText: "Recipient",
                              showLabelHeader: true,
                              hintText: 'First Name',
                              borderRadius: Sizer.height(4),
                              onChanged: (p0) => setState(() {}),
                            ),
                          ),
                          const XBox(10),
                          Expanded(
                            child: CustomTextField(
                              focusNode: recipientLastNameF,
                              controller: recipientLastNameC,
                              hintText: 'Last Name',
                              borderRadius: Sizer.height(4),
                              onChanged: (p0) => setState(() {}),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                  const Spacer(),
                  CustomBtn.solid(
                    onTap: () async {
                      FocusScope.of(context).unfocus();
                      final result = await BsWrapper.bottomSheet(
                        context: context,
                        widget: BeneficiaryScamSheet(),
                      );
                      if (result is bool && result) {
                        _saveBeneficiary();
                      }
                    },
                    online: vm.ngnButtonIsActive &&
                        (widget.arg.paymentMethod.nameCheck == true
                            ? vm.accountNameC.text.isNotEmpty
                            : recipientFirstNameC.text.isNotEmpty &&
                                recipientLastNameC.text.isNotEmpty),
                    text: "Save Beneficiary",
                  ),
                  const YBox(50),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  _saveBeneficiary() {
    var beneficiaryVM = context.read<BeneficiaryVM>();
    PostBeneficiaryArgs arg = PostBeneficiaryArgs(
      currencyId: widget.arg.currency.id ?? 0,
      institutionName: beneficiaryVM.bankNameC.text.trim(),
      accountName: beneficiaryVM.accountNameC.text.trim(),
      accountIdentifier: beneficiaryVM.accountNumC.text.trim(),
      institutionCode: beneficiaryVM.bankUUID,
      firstName: recipientFirstNameC.text.trim(),
      lastName: recipientLastNameC.text.trim(),
      transferMethod: TransferMethod.bankTransfer,
    );

    beneficiaryVM
        .saveBeneficiary(
      arg: arg,
      transferMethodType: TransferMethodType.bankTransfer,
    )
        .then((value) {
      if (value.success) {
        recipientFirstNameC.clear();
        recipientLastNameC.clear();
        context.read<BankVM>().resetData();
        beneficiaryVM
          ..clearData()
          ..getBeneficiaries();
        _showComfirmationScreen(msg: value.message.toString());
      } else {
        _showComfirmationScreen(
          isFailed: true,
          msg: value.message.toString(),
        );
      }
    });
  }

  _showComfirmationScreen({String? msg, bool isFailed = false}) {
    printty("showing confirmation screen $msg");
    context.read<SecQuestVM>().clearData();
    Navigator.pushNamed(
      context,
      RoutePath.successConfirmScreen,
      arguments: SuccessConfirmArg(
        title: msg ??
            (isFailed
                ? "Beneficiary Saved\n Failed"
                : "Beneficiary Saved\n Successfully"),
        imgPath: isFailed ? AppGifs.failure : null,
        btnText: "Continue",
        btnTap: () {
          _pop();
          if (!isFailed) {
            _pop();
            _pop();
            _pop();
            Navigator.pushNamed(
              NavigatorKeys.appNavigatorKey.currentContext!,
              RoutePath.beneficiaryScreen,
            );
          }
        },
      ),
    );
  }

  _pop() {
    Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
  }
}
