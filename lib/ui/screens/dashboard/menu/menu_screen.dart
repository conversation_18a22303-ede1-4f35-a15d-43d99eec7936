import 'package:flutter_svg/flutter_svg.dart';
import 'package:freshchat_sdk/freshchat_sdk.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/kyc/complete_kyc_screen.dart';

class MenuScreen extends StatefulWidget {
  const MenuScreen({super.key});

  @override
  State<MenuScreen> createState() => _MenuScreenState();
}

class _MenuScreenState extends State<MenuScreen> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SizedBox(
        height: Sizer.screenHeight,
        width: Sizer.screenWidth,
        child: BusyOverlay(
          show: context.watch<LoginVM>().isBusy,
          child: Consumer<AuthUserVM>(builder: (context, vm, _) {
            // printty(vm.user?.avatarUrl, logLevel: 'avatarUrl');
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: Sizer.screenWidth,
                  color: AppColors.primaryBlue,
                  child: SafeArea(
                      child: Container(
                    padding: EdgeInsets.symmetric(
                      horizontal: Sizer.width(24),
                    ).copyWith(
                      top: Sizer.height(20),
                      bottom: Sizer.height(20),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Container(
                              height: Sizer.height(60),
                              width: Sizer.width(60),
                              decoration: BoxDecoration(
                                color: AppColors.white,
                                borderRadius: BorderRadius.circular(100),
                              ),
                              child: cacheNetWorkImage(
                                vm.user?.avatarUrl ?? "",
                                fit: BoxFit.cover,
                              ),
                            ),
                            InkWell(
                              onTap: () {
                                context.read<FreshChatVM>()
                                  ..initializeFreshchat()
                                  ..configureFreshchatUser(
                                      context.read<AuthUserVM>().user);
                                Future.delayed(const Duration(seconds: 1), () {
                                  Freshchat.showConversations();
                                });
                              },
                              child: Row(
                                children: [
                                  SvgPicture.asset(
                                    AppSvgs.headphone,
                                    height: Sizer.height(20),
                                    width: Sizer.width(20),
                                  ),
                                  const XBox(8),
                                  Text(
                                    "Support",
                                    style: AppTypography.text14
                                        .copyWith(color: AppColors.white),
                                  )
                                ],
                              ),
                            ),
                          ],
                        ),
                        const YBox(6),
                        Text(
                          vm.user?.fullName ?? "",
                          style: AppTypography.text16.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.white,
                          ),
                        ),
                        Text(
                          vm.user?.userName ?? "",
                          style: AppTypography.text16.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.white,
                          ),
                        ),
                      ],
                    ),
                  )),
                ),
                Expanded(
                  child: Container(
                    width: Sizer.screenWidth,
                    color: AppColors.white,
                    child: ListView(
                      padding: EdgeInsets.only(
                        top: Sizer.height(24),
                        bottom: Sizer.height(24),
                        right: Sizer.width(24),
                        left: Sizer.width(24),
                      ),
                      children: [
                        MenuListTile(
                          title: 'Personal Profile',
                          iconData: Iconsax.user_edit,
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              RoutePath.profileDetailsScreen,
                            );
                          },
                        ),
                        const YBox(20),
                        MenuListTile(
                          title: 'Security',
                          iconData: Iconsax.security_safe,
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              RoutePath.securityScreen,
                            );
                          },
                        ),
                        const YBox(20),
                        MenuListTile(
                          title: 'Preferences',
                          iconData: Iconsax.edit_2,
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              RoutePath.preferenceScreen,
                            );
                          },
                        ),
                        const YBox(20),
                        MenuListTile(
                          title: 'Referrals',
                          iconData: Iconsax.gift,
                          onPressed: () {
                            // FlushBarToast.fLSnackBar(
                            //   message: 'Refer a friend is coming soon',
                            //   snackBarType: SnackBarType.success,
                            // );
                            BsWrapper.bottomSheet(
                              context: context,
                              widget: const InviteOthersAndEarnSheet(),
                            );
                          },
                        ),
                        const YBox(20),
                        Consumer<BeneficiaryVM>(builder: (context, beneVM, _) {
                          return MenuListTile(
                            title: 'Beneficiaries',
                            iconData: Iconsax.user_add,
                            onPressed: () {
                              if (!vm.userIsVerified) {
                                BsWrapper.bottomSheet(
                                  canDismiss: false,
                                  context: context,
                                  widget: const CompleteKycScreen(),
                                );

                                return;
                              }
                              Navigator.pushNamed(
                                context,
                                RoutePath.beneficiaryScreen,
                              );
                            },
                          );
                        }),
                        const YBox(20),
                        MenuListTile(
                          title: 'Help and Support',
                          iconData: Iconsax.message,
                          onPressed: () {
                            MixpanelService().track("Support Accessed");
                            Navigator.pushNamed(
                              context,
                              RoutePath.helpSupportScreen,
                            );
                          },
                        ),
                        const YBox(20),
                        MenuListTile(
                          title: 'About Korrency',
                          useImageIcon: true,
                          onPressed: () {
                            Navigator.pushNamed(
                              context,
                              RoutePath.aboutUsScreen,
                            );
                          },
                        ),
                        const YBox(30),
                        InkWell(
                          onTap: () async {
                            BsWrapper.bottomSheet(
                                context: context,
                                widget: ConfirmationSheet(
                                  title:
                                      vm.user?.firstName ?? vm.user?.userName,
                                  message:
                                      'Are you sure you want to log out of Korrency?',
                                  secondBtnText: "Log out",
                                  secendBtnTap: () => _handleLogout(),
                                ));
                          },
                          child: Container(
                            alignment: Alignment.center,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'Log out',
                                  style: AppTypography.text16.copyWith(
                                    fontWeight: FontWeight.w600,
                                    color: AppColors.iconRed,
                                  ),
                                ),
                                const XBox(8),
                                Icon(
                                  Iconsax.logout_1,
                                  size: Sizer.radius(24),
                                  color: AppColors.iconRed,
                                ),
                              ],
                            ),
                          ),
                        ),
                        const YBox(30),
                        InkWell(
                          onTap: () {
                            Navigator.pushNamed(
                                context, RoutePath.updateAvailableScreen);
                          },
                          child: Align(
                            alignment: Alignment.center,
                            child: Text(
                              'Version ${context.read<ConfigVM>().myAppCurrentVersion ?? '1.0.0'}',
                              style: AppTypography.text14.copyWith(
                                color: AppColors.grayE0,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ),
                        const YBox(100),
                      ],
                    ),
                  ),
                ),
              ],
            );
          }),
        ),
      ),
    );
  }

  void _handleLogout() async {
    final count = int.tryParse(
            await StorageService.getString(StorageKey.logoutCount) ?? "0") ??
        0;
    StorageService.storeString(StorageKey.logoutCount, '${count + 1}');
    navigate();
  }

  void navigate() {
    Navigator.pop(context);
    context.read<LoginVM>().logout();
  }
}
