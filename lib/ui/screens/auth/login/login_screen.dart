import 'package:korrency/core/core.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:local_session_timeout/local_session_timeout.dart';

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _resetData();
    });
  }

  _resetData() {
    context.read<DashboardVM>().resetData();
    context.read<AuthUserVM>().resetData();
    context.read<TransactionVM>().resetData();
    context.read<WalletVM>().resetData();
    context.read<InactivityVM>()
      ..setPauseAction(false)
      ..setUserHasLoggedIn(false);
  }

  @override
  void dispose() {
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<LoginVM>(
      builder: (context, vm, _) {
        return PopScope(
          canPop: false,
          child: BusyOverlay(
            show: vm.isBusy,
            child: Scaffold(
              backgroundColor: AppColors.bgWhite,
              body: SafeArea(
                bottom: false,
                child: Container(
                  padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                      .copyWith(top: Sizer.height(20)),
                  child: ListView(
                    children: [
                      AuthHeader(
                        onTap: () {
                          Navigator.pop(context);
                          vm.clearData();
                        },
                      ),
                      const YBox(50),
                      const AuthTextSubTitle(
                        title: "Log in to Continue",
                        subtitle: "Enter your details in the fields below",
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Email",
                        focusNode: _emailFocusNode,
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        controller: vm.emailC,
                        keyboardType: KeyboardType.email,
                        errorText: vm.emailC.text.isNotEmpty && !vm.isValidEmail
                            ? "Invalid Email"
                            : null,
                        onChanged: (_) => vm.emailIsValid(),
                        onSubmitted: (_) {
                          _emailFocusNode.unfocus();
                          FocusScope.of(context)
                              .requestFocus(_passwordFocusNode);
                        },
                      ),
                      const YBox(24),
                      CustomTextField(
                        labelText: "Password",
                        focusNode: _passwordFocusNode,
                        showLabelHeader: true,
                        borderRadius: Sizer.height(4),
                        controller: vm.passwordC,
                        isPassword: true,
                        onChanged: (_) => vm.reBuildUI(),
                        onSubmitted: (_) {
                          _passwordFocusNode.unfocus();
                          // Implement login logic here
                          _login();
                        },
                      ),
                      const YBox(25),
                      const ForgotPasswordBtn(),
                      const YBox(25),
                      // ValidationItemWidget(
                      //   label: "Remeber my account",
                      //   isValid: vm.rememberMe,
                      //   onTap: () {
                      //     vm.setRememberMe(
                      //       !vm.rememberMe,
                      //     );
                      //   },
                      // ),
                      const YBox(140),
                      CustomBtn.solid(
                        height: 65,
                        onTap: () {
                          _login();
                        },
                        online: vm.btnIsValid(),
                        text: "Log in",
                      ),
                      const YBox(30),
                      InkWell(
                        onTap: () {
                          vm.clearData();
                          Navigator.pushNamed(
                              context, RoutePath.createAcctScreen);
                        },
                        child: RichText(
                            textAlign: TextAlign.center,
                            text: TextSpan(
                              text: "Don’t have an account yet? ",
                              style: AppTypography.text14.copyWith(
                                color: AppColors.gray500,
                                fontWeight: FontWeight.w400,
                                fontFamily: 'Inter',
                                height: 2,
                              ),
                              children: [
                                TextSpan(
                                  text: "Create Account",
                                  style: AppTypography.text14.copyWith(
                                    color: AppColors.lightBlue,
                                    fontWeight: FontWeight.w500,
                                    fontFamily: 'Inter',
                                    height: 1.2,
                                  ),
                                ),
                              ],
                            )),
                      ),
                      const YBox(30),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _login() async {
    final loginVm = context.read<LoginVM>();
    final r = await loginVm.login();

    handleApiResponse(
      response: r,
      showSuccessToast: false,
      onSuccess: () {
        sessionStateStream.add(SessionState.startListening);
        Navigator.pushReplacementNamed(context, RoutePath.dashboardNav);
      },
    );
  }
}
