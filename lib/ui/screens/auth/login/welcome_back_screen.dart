import 'dart:io';

import 'package:korrency/core/core.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:local_session_timeout/local_session_timeout.dart';

class WelcomeBackScreen extends StatefulWidget {
  const WelcomeBackScreen({
    super.key,
    this.fromSplash = true,
  });

  final bool fromSplash;

  @override
  State<WelcomeBackScreen> createState() => _WelcomeBackScreenState();
}

class _WelcomeBackScreenState extends State<WelcomeBackScreen> {
  final FocusNode _passwordFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _passwordFocusNode);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      sessionStateStream.add(SessionState.stopListening);
    });
  }

  @override
  void dispose() {
    _passwordFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Consumer<AuthUserVM>(builder: (context, vm, _) {
        return BusyOverlay(
          show: context.watch<LoginVM>().isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                    .copyWith(top: Sizer.height(10)),
                child: SingleChildScrollView(
                  child: Column(
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const AuthHeader(
                              // showBackBtn: true,
                              ),
                          const YBox(50),
                          AuthTextSubTitle(
                            title: "Welcome Back, ${vm.user?.firstName ?? ""}",
                            subtitle:
                                "Use your biometrics to log in or enter your password",
                          ),
                          const YBox(24),
                          CustomTextField(
                            labelText: "Password",
                            focusNode: _passwordFocusNode,
                            showLabelHeader: true,
                            borderRadius: Sizer.height(4),
                            controller: context.read<LoginVM>().passwordC,
                            isPassword: true,
                            onChanged: (_) => vm.reBuildUI(),
                            onSubmitted: (_) {
                              _passwordFocusNode.unfocus();
                            },
                          ),
                          const YBox(25),
                          const ForgotPasswordBtn(),
                          const YBox(26),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                padding: EdgeInsets.only(
                                  top: Sizer.height(4),
                                ),
                                child: Icon(
                                  Icons.info_outline,
                                  color: AppColors.black900,
                                  size: Sizer.width(18),
                                ),
                              ),
                              const XBox(6),
                              RichText(
                                text: TextSpan(
                                  text: "Not ${vm.user?.email ?? "you"}? ",
                                  style: AppTypography.text13.copyWith(
                                    color: AppColors.gray500,
                                    fontWeight: FontWeight.w400,
                                    height: 2,
                                  ),
                                  children: <InlineSpan>[
                                    WidgetSpan(
                                      child: InkWell(
                                        onTap: () {
                                          Navigator.pushNamed(
                                              context, RoutePath.loginScreen);
                                        },
                                        child: Text(
                                          "Sign in here",
                                          style: AppTypography.text13.copyWith(
                                            color: AppColors.lightBlue,
                                            fontWeight: FontWeight.w500,
                                            height: 1.2,
                                            decoration:
                                                TextDecoration.underline,
                                            decorationColor:
                                                AppColors.lightBlue,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                          const YBox(80),
                          InkWell(
                            onTap: () => _biometricAuthentication(),
                            child: Container(
                              alignment: Alignment.center,
                              padding: const EdgeInsets.all(10),
                              child: imageHelper(
                                Platform.isIOS
                                    ? AppImages.faceId
                                    : AppImages.fingerprint1,
                                height: Sizer.height(42),
                                width: Sizer.width(37.65),
                              ),
                            ),
                          ),
                          const YBox(40),
                          Align(
                            alignment: Alignment.center,
                            child: Text(
                              'Version ${context.read<ConfigVM>().myAppCurrentVersion ?? '1.0.0'}',
                              style: AppTypography.text14.copyWith(
                                color: AppColors.grayE0,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                          const YBox(20),
                        ],
                      ),
                      CustomBtn.solid(
                        onTap: () {
                          _login();
                        },
                        online:
                            context.read<LoginVM>().passwordC.text.isNotEmpty,
                        height: 65,
                        text: "Log in",
                      ),
                      const YBox(30),
                      InkWell(
                        onTap: () {
                          Navigator.pushNamed(
                              context, RoutePath.createAcctScreen);
                        },
                        child: RichText(
                          text: TextSpan(
                            text: "Don’t have an account yet? ",
                            style: AppTypography.text14.copyWith(
                              color: AppColors.gray500,
                              fontWeight: FontWeight.w400,
                              height: 2,
                            ),
                            children: [
                              TextSpan(
                                text: "Create Account",
                                style: AppTypography.text14.copyWith(
                                  color: AppColors.lightBlue,
                                  fontWeight: FontWeight.w500,
                                  height: 1.2,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const YBox(30),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      }),
    );
  }

  _login() {
    final user = context.read<AuthUserVM>().user;
    context
        .read<LoginVM>()
        .login(
          email: user?.email,
        )
        .then((value) {
      if (value.success) {
        // context.read<InactivityVM>()
        //   ..setPauseAction(false)
        //   ..setUserHasLoggedIn(true);
        printty("User id ${user?.id}");
        MixpanelService().identify(user?.id ?? "");

        // Track login with Firebase Analytics
        FirebaseAnalyticsService.instance.trackLogin(
          userId: user?.id ?? "",
          method: AnalyticsValues.registrationEmail,
        );

        sessionStateStream.add(SessionState.startListening);
        if (widget.fromSplash) {
          Navigator.pushNamed(context, RoutePath.dashboardNav);
          return;
        }
        Navigator.pop(context);
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _biometricAuthentication() async {
    bool fingerPrintIsEnabled =
        await StorageService.getBoolItem(StorageKey.fingerPrintIsEnabled) ??
            false;

    if (!fingerPrintIsEnabled) {
      _showFingerPrompt();
      return;
    }

    await BiometricService.authenticate()
        ? _gotoNextScreen(RoutePath.dashboardNav)
        : _showToast("Biometric Authentication Failed");
  }

  _showFingerPrompt() {
    BsWrapper.bottomSheet(
      context: context,
      widget: const EnableFingerprintSheet(),
    );
  }

  _gotoNextScreen(String route) {
    Navigator.pushNamed(context, route);
  }

  _showToast(String message) {
    FlushBarToast.fLSnackBar(
      message: message,
    );
  }
}
