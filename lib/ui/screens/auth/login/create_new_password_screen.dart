import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CreateNewPasswordScreen extends StatefulWidget {
  const CreateNewPasswordScreen({Key? key}) : super(key: key);

  @override
  State<CreateNewPasswordScreen> createState() =>
      _CreateNewPasswordScreenState();
}

class _CreateNewPasswordScreenState extends State<CreateNewPasswordScreen> {
  final FocusNode _passFocusNode = FocusNode();
  final FocusNode _confirmPassFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _passFocusNode);
  }

  _unFocus() {
    _passFocusNode.unfocus();
    _confirmPassFocusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PasskeyResetVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                    .copyWith(top: Sizer.height(20)),
                child: Column(
                  children: [
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AuthHeader(
                              showBackBtn: true,
                              onTap: () {
                                vm.clearData();
                                Navigator.pop(context);
                              },
                            ),
                            const YBox(50),
                            const AuthTextSubTitle(
                              title: "Create new password",
                              subtitle: "Please enter your new password",
                            ),
                            const YBox(24),
                            CustomTextField(
                              labelText: "Password",
                              showLabelHeader: true,
                              borderRadius: Sizer.height(4),
                              controller: vm.passwordC,
                              focusNode: _passFocusNode,
                              isPassword: true,
                              onChanged: (val) {
                                vm.validatePassword(vm.passwordC.text);
                                vm.passwordMatch(vm.passwordC.text,
                                    vm.passwordConfirmC.text);
                              },
                              onSubmitted: (_) {
                                _passFocusNode.unfocus();
                                FocusScope.of(context)
                                    .requestFocus(_confirmPassFocusNode);
                              },
                            ),
                            const YBox(8),
                            Column(children: [
                              ValidationItemWidget(
                                label: "At least 8 characters long",
                                isValid:
                                    vm.validatorStatus.hasAtleast8Character,
                              ),
                              const YBox(8),
                              ValidationItemWidget(
                                label: "At least 1 Upper and 1 Lower case",
                                isValid: vm.validatorStatus.containsUpperCase,
                              ),
                              const YBox(8),
                              ValidationItemWidget(
                                label: "At least 1 number",
                                isValid: vm.validatorStatus.containsANumber,
                              ),
                              const YBox(8),
                              ValidationItemWidget(
                                label: "At least 1 special character @ # \$ %r",
                                isValid:
                                    vm.validatorStatus.containsSpecialCharacter,
                              ),
                            ]),
                            const YBox(24),
                            CustomTextField(
                              labelText: "Confirm Password",
                              showLabelHeader: true,
                              borderRadius: Sizer.height(4),
                              controller: vm.passwordConfirmC,
                              isPassword: true,
                              isConfirmPassword: true,
                              errorText: vm.passWordDontMatch
                                  ? 'Passwords do not match'
                                  : null,
                              onChanged: (val) => vm.passwordMatch(
                                  vm.passwordC.text, vm.passwordConfirmC.text),
                              focusNode: _confirmPassFocusNode,
                              onSubmitted: (_) {
                                _confirmPassFocusNode.unfocus();
                              },
                            ),
                            const YBox(120),
                          ],
                        ),
                      ),
                    ),
                    CustomBtn.solid(
                      onTap: () {
                        _unFocus();
                        _saveChanges();
                      },
                      online: vm.enableSavePasswordBtn(),
                      text: "Save",
                    ),
                    const YBox(30),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _saveChanges() {
    context.read<PasskeyResetVM>().resetPassword().then((value) {
      if (value.success) {
        _moveToSuccessScreen();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _moveToSuccessScreen() {
    Navigator.pushReplacementNamed(
      context,
      RoutePath.successScreen,
      arguments: ConfirmationArg(
        title: "Welcome Back",
        buttonText: "Go back to log in",
        subtitle: const ConfirmationSubtitleText(
          startText: "Start ",
          coloredText: "sending and exchanging",
          endText: " money in minutes. It's that easy",
        ),
        onBtnTap: () {
          Navigator.pushNamedAndRemoveUntil(
            NavigatorKeys.appNavigatorKey.currentContext!,
            RoutePath.loginScreen,
            (route) => false,
          );
        },
      ),
    );
  }
}
