import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({Key? key}) : super(key: key);

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  FocusNode focusNode = FocusNode();

  @override
  void initState() {
    KeyboardOverlay.addRemoveFocusNode(context, focusNode);
    focusNode.requestFocus();
    super.initState();
  }

  @override
  void dispose() {
    focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PasskeyResetVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            body: <PERSON><PERSON><PERSON>(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                    .copyWith(top: Sizer.height(20)),
                child: Column(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AuthHeader(
                              showBackBtn: true,
                              onTap: () {
                                Navigator.pop(context);
                                vm.clearData();
                              }),
                          const YBox(50),
                          const AuthTextSubTitle(
                            title: "Forgot Password",
                            subtitle:
                                "Enter your email address to reset password",
                          ),
                          const YBox(24),
                          CustomTextField(
                            labelText: "Email",
                            focusNode: focusNode,
                            showLabelHeader: true,
                            borderRadius: Sizer.height(4),
                            controller: vm.emailC,
                            keyboardType: KeyboardType.email,
                            errorText:
                                vm.emailC.text.isNotEmpty && !vm.isValidEmail
                                    ? "Invalid Email"
                                    : null,
                            onChanged: (_) => vm.emailIsValid(),
                            onSubmitted: (p0) {
                              focusNode.unfocus();
                            },
                          ),
                        ],
                      ),
                    ),
                    CustomBtn.solid(
                      onTap: () {
                        focusNode.unfocus();
                        _requestForgotPasswordOtp();
                      },
                      online: vm.isValidEmail,
                      height: 65,
                      text: "Send OTP",
                    ),
                    const YBox(50),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _requestForgotPasswordOtp() {
    focusNode.unfocus();
    context.read<PasskeyResetVM>().forgotPasswordOtpRequest().then((value) {
      if (value.success) {
        Navigator.pushReplacementNamed(
          context,
          RoutePath.passwordResetScreen,
        );
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
          snackBarType: SnackBarType.success,
        );
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }
}
