import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/confrimation/success_confirm_screen.dart';
import 'package:pinput/pinput.dart';

class PasswordResetScreen extends StatefulWidget {
  const PasswordResetScreen({Key? key}) : super(key: key);

  @override
  State<PasswordResetScreen> createState() => _PasswordResetScreenState();
}

class _PasswordResetScreenState extends State<PasswordResetScreen> {
  FocusNode pinFocusNode = FocusNode();

  @override
  void initState() {
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
    pinFocusNode.requestFocus();
    super.initState();
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<PasskeyResetVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                    .copyWith(top: Sizer.height(20)),
                child: Column(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AuthHeader(
                            showBackBtn: true,
                            onTap: () {
                              vm.clearData();
                              Navigator.pop(context);
                            },
                          ),
                          const YBox(50),
                          const AuthTextSubTitle(
                            title: "Password Reset",
                            subtitle: "Please enter the 6-digit code sent to ",
                          ),
                          Text(
                            vm.emailC.text,
                            style: AppTypography.text15.copyWith(
                              color: AppColors.gray800,
                              fontWeight: FontWeight.w600,
                              height: 1.3,
                            ),
                          ),
                          const YBox(30),
                          Pinput(
                              defaultPinTheme: PinInputTheme.defaultPinTheme(),
                              followingPinTheme: PinInputTheme.followPinTheme(),
                              length: 6,
                              controller: vm.otpC,
                              focusNode: pinFocusNode,
                              showCursor: true,
                              onChanged: (value) => vm.reBuildUI(),
                              onCompleted: (pin) {
                                pinFocusNode.unfocus();
                                _moveToEmailAndPasswordScreen();
                              }),
                          const YBox(24),
                          ResendCode(
                            onResendCode: () {
                              _requestForgotPasswordOtp();
                            },
                          ),
                        ],
                      ),
                    ),
                    CustomBtn.solid(
                      height: 65,
                      onTap: () {
                        pinFocusNode.unfocus();
                        _moveToEmailAndPasswordScreen();
                      },
                      online: vm.optBtnsValid(),
                      text: "Verify",
                    ),
                    const YBox(50),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _requestForgotPasswordOtp() {
    pinFocusNode.unfocus();
    context.read<PasskeyResetVM>().forgotPasswordOtpRequest().then((value) {
      if (value.success) {
        Navigator.pushReplacementNamed(
          context,
          RoutePath.passwordResetScreen,
        );
      } else {
        Navigator.pushNamed(
          context,
          RoutePath.successConfirmScreen,
          arguments: SuccessConfirmArg(
            title: value.message ?? "Something went wrong, please try again",
            imgPath: AppGifs.failure,
            btnText: "Continue",
            btnTap: () {
              Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
            },
          ),
        );
      }
    });
  }

  _moveToEmailAndPasswordScreen() {
    pinFocusNode.unfocus();
    Navigator.pushReplacementNamed(context, RoutePath.createNewPasswordScreen);
  }
}
