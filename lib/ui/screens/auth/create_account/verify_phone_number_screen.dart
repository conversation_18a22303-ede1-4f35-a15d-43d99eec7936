import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:pinput/pinput.dart';

class VerifyPhoneNumberScreen extends StatefulWidget {
  const VerifyPhoneNumberScreen({Key? key}) : super(key: key);

  @override
  State<VerifyPhoneNumberScreen> createState() =>
      _VerifyPhoneNumberScreenState();
}

class _VerifyPhoneNumberScreenState extends State<VerifyPhoneNumberScreen> {
  final FocusNode pinFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    pinFocusNode.requestFocus();
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OnBoardVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                    .copyWith(top: Sizer.height(20)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    AuthHeader(
                      showBackBtn: true,
                      onTap: () {
                        vm.clearData();
                        Navigator.pop(context);
                      },
                    ),
                    const YBox(50),
                    AuthTextSubTitle(
                      title: "Verify Phone Number",
                      subtitle:
                          "Please enter the 6-digit code sent \n${vm.phoneNumC.text}",
                    ),
                    const YBox(30),
                    Pinput(
                      defaultPinTheme: PinInputTheme.defaultPinTheme(),
                      // focusedPinTheme: PinInputTheme.focusFillPinTheme(),
                      followingPinTheme: PinInputTheme.followPinTheme(),
                      length: 6,
                      controller: vm.phoneOtp,
                      focusNode: pinFocusNode,
                      showCursor: true,
                      // onChanged: (value) {
                      //   setState(() {
                      //     _backgroundColor = AppColors.gray200;
                      //   });
                      // },
                      onCompleted: (pin) {
                        printty("Completed");
                        _verifyOtp();
                      },
                    ),
                    const YBox(24),
                    ResendCode(
                      onResendCode: () {},
                    ),
                    const Spacer(),
                    CustomBtn.solid(
                      onTap: () => _verifyOtp(),
                      online: vm.phoneOtp.text.length == 6,
                      text: "Verify",
                    ),
                    const YBox(50),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _verifyOtp() {
    pinFocusNode.unfocus();
    context.read<OnBoardVM>().verifyOtp().then((value) {
      if (value.success) {
        _moveToEmailAndPasswordScreen();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
        _clearOtp();
      }
    });
  }

  _clearOtp() {
    context.read<OnBoardVM>().phoneOtp.clear();
  }

  _moveToEmailAndPasswordScreen() {
    context.read<OnBoardVM>().phoneOtp.clear();
    Navigator.pushReplacementNamed(context, RoutePath.emailAndPasswordScreen);
  }
}
