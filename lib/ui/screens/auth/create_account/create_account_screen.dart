import 'package:flutter/gestures.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/helpsupport/create_freshdesk_ticket_webview.dart';
// import 'package:telephony/telephony.dart';

class CreateAccountScreen extends StatefulWidget {
  const CreateAccountScreen({Key? key}) : super(key: key);

  @override
  State<CreateAccountScreen> createState() => _CreateAccountScreenState();
}

class _CreateAccountScreenState extends State<CreateAccountScreen> {
  FocusNode phoneNumFocus = FocusNode();
  // final Telephony telephony = Telephony.instance;

  @override
  void initState() {
    KeyboardOverlay.addRemoveFocusNode(context, phoneNumFocus);
    WidgetsBinding.instance.addPostFrameCallback((_) {});
    // if (Platform.isAndroid) {
    //   listenToIncomingSMS();
    // }
    super.initState();
  }

  // SMS Autifill
  // void listenToIncomingSMS() {
  //   printty("Listening to sms.");
  //   telephony.listenIncomingSms(
  //       onNewMessage: (SmsMessage message) {
  //         // Handle message
  //         printty("sms received : ${message.body}");
  //         // verify if we are reading the correct sms or not

  //         if (message.body!.contains("phone-auth-15bdb")) {
  //           String otpCode = message.body!.substring(0, 6);
  //           setState(() {
  //             // _otpContoller.text = otpCode;
  //             // wait for 1 sec and then press handle submit
  //             Future.delayed(const Duration(seconds: 1), () {
  //               // handleSubmit(context);
  //             });
  //           });
  //         }
  //       },
  //       listenInBackground: false);
  // }

  @override
  void dispose() {
    phoneNumFocus.dispose();
    super.dispose();
  }

  onFocus() {
    phoneNumFocus.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OnBoardVM>(
      builder: (context, vm, _) {
        return BusyOverlay(
          show: vm.isBusy,
          child: Scaffold(
            backgroundColor: AppColors.bgWhite,
            body: SafeArea(
              bottom: false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                    .copyWith(top: Sizer.height(20)),
                child: ListView(
                  children: [
                    AuthHeader(
                      onTap: () {
                        Navigator.pop(context);
                        vm.clearData();
                      },
                    ),
                    const YBox(50),
                    Container(
                      alignment: Alignment.centerLeft,
                      child: const AuthTextSubTitle(
                        title: "Create an account",
                        subtitle: "Let’s get you started with your account",
                      ),
                    ),
                    const YBox(30),
                    CustomTextField(
                      labelText: "Phone Number",
                      keyboardType: KeyboardType.phone,
                      showLabelHeader: true,
                      controller: vm.phoneNumC,
                      focusNode: phoneNumFocus,
                      borderRadius: 4,
                      prefixIcon: InkWell(
                        onTap: () {
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: const SelectCountrySheet(),
                          );
                        },
                        child: Container(
                          padding: EdgeInsets.only(
                            left: Sizer.width(16),
                            right: Sizer.width(6),
                          ),
                          child: CurrencyPrefix(
                            countryDialCode: vm.country?.dialCode ?? "+1",
                            countryFlag: vm.country?.flag ?? "",
                          ),
                        ),
                      ),
                      onChanged: (val) {
                        if (val.trim().length > 10) {
                          vm.phoneNumC.text = val.substring(0, 10);
                        }
                        printty('phone number: ${vm.phoneNumC.text}');
                        vm.reBuildUI();
                      },
                      onSubmitted: (val) {
                        onFocus();
                      },
                    ),
                    const YBox(24),
                    Row(
                      children: [
                        CustomCheckbox(
                          isSelected: vm.optedForProductUpdate,
                          onTap: () {
                            vm.setOptedForProduct(!vm.optedForProductUpdate);
                          },
                        ),
                        const XBox(8),
                        Expanded(
                          child: Text(
                            "Receive important account updates via SMS and email. Uncheck to opt out.",
                            style: AppTypography.text12.copyWith(
                              color: vm.optedForProductUpdate
                                  ? AppColors.baseBlack
                                  : AppColors.gray500,
                              fontWeight: FontWeight.w400,
                              height: 1.2,
                            ),
                          ),
                        )
                      ],
                    ),
                    const YBox(16),
                    Row(
                      children: [
                        CustomCheckbox(
                          isSelected: vm.acceptedTermsAndConditions,
                          onTap: () {
                            vm.setTemAndCondition(
                                !vm.acceptedTermsAndConditions);
                            // setState(() {
                            //   acceptedTerms = !acceptedTerms;
                            // });
                          },
                        ),
                        const XBox(8),
                        Expanded(
                          child: RichText(
                              text: TextSpan(
                            children: [
                              TextSpan(
                                text:
                                    "By checking this box, you agree to Korrency’s ",
                                style: AppTypography.text12.copyWith(
                                  color: vm.acceptedTermsAndConditions
                                      ? AppColors.baseBlack
                                      : AppColors.gray500,
                                  fontWeight: FontWeight.w400,
                                  height: 1.2,
                                ),
                              ),
                              TextSpan(
                                text: "Terms and Conditions",
                                style: AppTypography.text12.copyWith(
                                  color: AppColors.lightBlue,
                                  fontWeight: FontWeight.w400,
                                  height: 1.2,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Navigator.pushNamed(
                                      context,
                                      RoutePath.createFreshDeskTicketWebview,
                                      arguments: WebViewArg(
                                        webURL:
                                            AppUtils.korrencyTermsAndCondition,
                                      ),
                                    );
                                  },
                              ),
                              TextSpan(
                                text: " and ",
                                style: AppTypography.text12.copyWith(
                                  color: vm.acceptedTermsAndConditions
                                      ? AppColors.baseBlack
                                      : AppColors.gray500,
                                  fontWeight: FontWeight.w400,
                                  height: 1.2,
                                ),
                              ),
                              TextSpan(
                                text: "Privacy Policy",
                                style: AppTypography.text12.copyWith(
                                  color: AppColors.lightBlue,
                                  fontWeight: FontWeight.w400,
                                  height: 1.2,
                                ),
                                recognizer: TapGestureRecognizer()
                                  ..onTap = () {
                                    Navigator.pushNamed(
                                      context,
                                      RoutePath.createFreshDeskTicketWebview,
                                      arguments: WebViewArg(
                                        webURL: AppUtils.korrencyPolicy,
                                      ),
                                    );
                                  },
                              ),
                            ],
                          )),
                        )
                      ],
                    ),
                    const YBox(230),
                    CustomBtn.solid(
                      onTap: () {
                        onFocus();
                        _requestOtp();
                      },
                      height: 65,
                      online: vm.phoneNumC.text.length > 6 &&
                          vm.acceptedTermsAndConditions,
                      text: "Continue",
                    ),
                    const YBox(30),
                    InkWell(
                      onTap: () {
                        Navigator.pushNamed(context, RoutePath.loginScreen);
                        vm.clearData();
                      },
                      child: RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            text: "Do you already have an Account? ",
                            style: AppTypography.text14.copyWith(
                              color: AppColors.gray500,
                              fontWeight: FontWeight.w400,
                              fontFamily: 'Inter',
                              height: 2,
                            ),
                            children: [
                              TextSpan(
                                text: "Log In",
                                style: AppTypography.text14.copyWith(
                                  color: AppColors.lightBlue,
                                  fontWeight: FontWeight.w500,
                                  height: 1.2,
                                  fontFamily: 'Inter',
                                ),
                              ),
                            ],
                          )),
                    ),
                    const YBox(30),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  _requestOtp() async {
    phoneNumFocus.unfocus();
    context.read<OnBoardVM>().requestOtp().then((value) {
      if (value.success) {
        printty(value.message);
        return _pushToVerifyPhoneNum();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _pushToVerifyPhoneNum() {
    Navigator.pushNamed(context, RoutePath.verifyPhoneNumScreen);
  }
}
