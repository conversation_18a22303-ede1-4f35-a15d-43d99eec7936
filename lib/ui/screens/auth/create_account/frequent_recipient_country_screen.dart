// ignore_for_file: use_build_context_synchronously

import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class FrequentRecipientCountryScreen extends StatefulWidget {
  const FrequentRecipientCountryScreen({
    super.key,
    this.fromHome = false,
  });

  final bool fromHome;

  @override
  State<FrequentRecipientCountryScreen> createState() =>
      _FrequentRecipientCountryScreenState();
}

class _FrequentRecipientCountryScreenState
    extends State<FrequentRecipientCountryScreen> {
  int? selectedCountryId;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<CurrencyVM>().getAfricanCurrencies();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<CurrencyVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: SafeArea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                  .copyWith(top: Sizer.height(20)),
              child: Column(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        InkWell(
                          onTap: () {
                            Navigator.pop(context);
                          },
                          child: Icon(
                            Iconsax.arrow_left_2,
                            size: Sizer.radius(24),
                          ),
                        ),
                        const YBox(20),
                        const AuthTextSubTitle(
                          title: "Frequent Recipient Country",
                          subtitle:
                              "Select the countries you will be sending money to frequently ",
                        ),
                        const YBox(30),
                        ListView.separated(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemBuilder: (ctx, i) {
                            final a = vm.africanCurrencies[i];
                            return FreqCountryListTile(
                              countryFlag: a.flag ?? "",
                              countryName: a.name ?? "",
                              isSelected: selectedCountryId == a.id,
                              onTap: () {
                                selectedCountryId = a.id;
                                setState(() {});
                              },
                            );
                          },
                          separatorBuilder: (_, __) => const YBox(8),
                          itemCount: vm.africanCurrencies.length,
                        )
                      ],
                    ),
                  ),
                  CustomBtn.solid(
                    onTap: _updateCountry,
                    online: selectedCountryId != null,
                    text: "Continue",
                  ),
                  const YBox(36),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  _updateCountry() async {
    final vm = context.read<CurrencyVM>();
    final res = await vm.updateFreqAfricanCountry(selectedCountryId ?? 0);

    handleApiResponse(
        response: res,
        onSuccess: () {
          widget.fromHome
              ? Navigator.pushNamedAndRemoveUntil(
                  context, RoutePath.dashboardNav, (r) => false)
              : _moveToBiometricsScreen();
        });
  }

  _moveToBiometricsScreen() {
    // _clearOtp();
    Navigator.pushNamed(context, RoutePath.biometricScreen);
  }
}
