import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class EmailAndPasswordScreen extends StatefulWidget {
  const EmailAndPasswordScreen({Key? key}) : super(key: key);

  @override
  State<EmailAndPasswordScreen> createState() => _EmailAndPasswordScreenState();
}

class _EmailAndPasswordScreenState extends State<EmailAndPasswordScreen> {
  final FocusNode _emailFocusNode = FocusNode();
  final FocusNode _passwordFocusNode = FocusNode();
  final FocusNode _userNameFocusNode = FocusNode();
  final FocusNode _referralFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _emailFocusNode);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      context.read<OnBoardVM>().getDeepLinkValueFromStorage();
    });
  }

  unfocusAllNode() {
    _emailFocusNode.unfocus();
    _passwordFocusNode.unfocus();
    _userNameFocusNode.unfocus();
    _referralFocusNode.unfocus();
  }

  @override
  void dispose() {
    _emailFocusNode.dispose();
    _passwordFocusNode.dispose();
    _userNameFocusNode.dispose();
    _referralFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<OnBoardVM>(builder: (context, vm, _) {
      return BusyOverlay(
        show: vm.isBusy,
        child: Scaffold(
          backgroundColor: AppColors.bgWhite,
          body: SafeArea(
            bottom: false,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24))
                  .copyWith(top: Sizer.height(20)),
              child: Column(
                children: [
                  Expanded(
                    child: SingleChildScrollView(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          AuthHeader(
                            showBackBtn: true,
                            onTap: () {
                              vm.clearData();
                              Navigator.pop(context);
                            },
                          ),
                          const YBox(50),
                          const AuthTextSubTitle(
                            title: "Setup your email and password",
                            subtitle:
                                "Choose a unique username, enter your email and password to continue.",
                          ),
                          const YBox(24),
                          CustomTextField(
                            labelText: "Username",
                            showLabelHeader: true,
                            focusNode: _userNameFocusNode,
                            borderRadius: Sizer.height(4),
                            controller: vm.userNameC,
                            errorText: vm.userNameC.text.isNotEmpty &&
                                    !vm.userNameValid
                                ? "Username is required"
                                : null,
                            onChanged: (val) => vm.isUserNameValid(),
                            onSubmitted: (p0) {
                              _userNameFocusNode.unfocus();
                              _emailFocusNode.requestFocus();
                            },
                          ),
                          const YBox(24),
                          CustomTextField(
                            labelText: "Email",
                            showLabelHeader: true,
                            focusNode: _emailFocusNode,
                            borderRadius: Sizer.height(4),
                            controller: vm.emailC,
                            errorText:
                                vm.emailC.text.isNotEmpty && !vm.isValidEmail
                                    ? "Invalid Email"
                                    : null,
                            onChanged: (val) => vm.emailIsValid(),
                            onSubmitted: (p0) {
                              _emailFocusNode.unfocus();
                              _passwordFocusNode.requestFocus();
                            },
                          ),
                          const YBox(24),
                          CustomTextField(
                            labelText: "Password",
                            showLabelHeader: true,
                            focusNode: _passwordFocusNode,
                            borderRadius: Sizer.height(4),
                            controller: vm.passwordC,
                            isPassword: true,
                            onChanged: (val) {
                              vm.validatePassword(vm.passwordC.text);
                            },
                            onSubmitted: (p0) {
                              _passwordFocusNode.unfocus();
                              _referralFocusNode.requestFocus();
                            },
                          ),
                          const YBox(8),
                          Column(children: [
                            ValidationItemWidget(
                              label: "At least 8 characters long",
                              isValid: vm.validatorStatus.hasAtleast8Character,
                            ),
                            const YBox(8),
                            ValidationItemWidget(
                              label: "At least 1 Upper and 1 Lower case",
                              isValid: vm.validatorStatus.containsUpperCase &&
                                  vm.validatorStatus.containsLowerCase,
                            ),
                            const YBox(8),
                            ValidationItemWidget(
                              label: "At least 1 number",
                              isValid: vm.validatorStatus.containsANumber,
                            ),
                            const YBox(8),
                            ValidationItemWidget(
                              label: "At least 1 special character @ # \$ %r",
                              isValid:
                                  vm.validatorStatus.containsSpecialCharacter,
                            ),
                          ]),
                          const YBox(24),
                          CustomTextField(
                            labelText: "Referral Code (optional)",
                            showLabelHeader: true,
                            focusNode: _referralFocusNode,
                            borderRadius: Sizer.height(4),
                            controller: vm.referralC,
                            onChanged: (val) {},
                            onSubmitted: (p0) {
                              _referralFocusNode.unfocus();
                            },
                          ),
                          const YBox(60),
                        ],
                      ),
                    ),
                  ),
                  CustomBtn.solid(
                    onTap: () {
                      unfocusAllNode();
                      verifyEmailOtp();
                    },
                    online: vm.signupBtnIsValid(),
                    height: 65,
                    text: "Continue",
                  ),
                  const YBox(30),
                ],
              ),
            ),
          ),
        ),
      );
    });
  }

  verifyEmailOtp() async {
    context.read<OnBoardVM>().requestOtp(otpType: OtpType.email).then((value) {
      if (value.success) {
        _moveToVerifyEmailScreen();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _moveToVerifyEmailScreen() {
    Navigator.pushNamed(context, RoutePath.verifyEmailScreen);
  }
}
