import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/kyc/pageviews/pageviews.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';

class CompleteYourProfileScreen extends StatefulWidget {
  const CompleteYourProfileScreen({
    Key? key,
    this.pageIndex,
  }) : super(key: key);

  final int? pageIndex;

  @override
  State<CompleteYourProfileScreen> createState() =>
      _CompleteYourProfileScreenState();
}

class _CompleteYourProfileScreenState extends State<CompleteYourProfileScreen> {
  final PageController _pageController = PageController();

  int _currentPage = 0;

  @override
  void initState() {
    printty(widget.pageIndex);
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.pageIndex != null) {
        _pageController.jumpToPage(
          widget.pageIndex ?? 0,
          // duration: const Duration(milliseconds: 500),
          // curve: Curves.easeInOut,
        );
      }
    });

    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<KycVM>(
      builder: (context, vm, _) {
        return Container(
          padding:
              EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom),
          height: Sizer.screenHeight * 0.95,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const YBox(20),
              Container(
                margin: EdgeInsets.symmetric(horizontal: Sizer.width(14)),
                height: Sizer.height(12),
                width: Sizer.screenWidth,
                decoration: BoxDecoration(
                  color: AppColors.sheetLightBlue.withOpacity(0.25),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
              ),
              Expanded(
                child: Container(
                  width: Sizer.screenWidth,
                  decoration: const BoxDecoration(
                    color: AppColors.bgWhite,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                    child: BusyOverlay(
                      show: vm.isBusy,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Container(
                            alignment: Alignment.centerLeft,
                            margin: EdgeInsets.symmetric(
                                horizontal: Sizer.width(24)),
                            padding: EdgeInsets.only(top: Sizer.height(28)),
                            child: BackPop(
                              onTap: () => _backPop(),
                            ),
                          ),
                          const YBox(30),
                          LinearPercentIndicator(
                            padding: EdgeInsets.symmetric(
                                horizontal: Sizer.width(24)),
                            animation: true,
                            animationDuration: 2000,
                            lineHeight: 4.0,
                            percent: _setPercentage(_currentPage),
                            backgroundColor: AppColors.baseGray,
                            progressColor: AppColors.primaryBlue,
                            animateFromLastPercent: true,
                            restartAnimation: false,
                          ),
                          const YBox(10),
                          Expanded(
                            child: PageView(
                              controller: _pageController,
                              physics: const NeverScrollableScrollPhysics(),
                              onPageChanged: (value) => setState(() {
                                _currentPage = value;
                              }),
                              children: [
                                CompleteProfileNamesPageView(
                                  kycVm: vm,
                                  onPageChange: () {
                                    _nextPage();
                                  },
                                ),
                                CompleteProfilePageViewTwo(
                                  kycVm: vm,
                                  onPageChange: () {
                                    _nextPage();
                                  },
                                ),
                                CompleteProfileAddressPageView(
                                  kycVm: vm,
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  _backPop() {
    if (widget.pageIndex != null) {
      return Navigator.pop(context);
    }
    if (_currentPage == 0) {
      return Navigator.pop(context);
    }
    _previousPage();
  }

  _nextPage() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  _previousPage() {
    _pageController.previousPage(
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  double _setPercentage(int index) {
    switch (index) {
      case 0:
        return 0.33;
      case 1:
        return 0.66;
      case 2:
        return 1;

      default:
        return 0.33;
    }
  }
}
