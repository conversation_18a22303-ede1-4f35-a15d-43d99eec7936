import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class GenderSheet extends StatefulWidget {
  const GenderSheet({Key? key}) : super(key: key);

  @override
  State<GenderSheet> createState() => _GenderSheetState();
}

class _GenderSheetState extends State<GenderSheet> {
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      // height: Sizer.screenHeight * 0.50,
      child: Consumer<KycVM>(builder: (context, vm, _) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    size: Sizer.radius(23),
                  ),
                )
              ],
            ),
            const YBox(25),
            InkWell(
              onTap: () {
                vm.setGenderType(GenderType.male);
                Navigator.pop(context);
              },
              child: ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                child: WalletListTile(
                  title: "Male",
                  isSelected: vm.genderType == GenderType.male,
                ),
              ),
            ),
            const YBox(16),
            InkWell(
              onTap: () {
                vm.setGenderType(GenderType.female);
                Navigator.pop(context);
              },
              child: ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                child: WalletListTile(
                  title: "Female",
                  isSelected: vm.genderType == GenderType.female,
                ),
              ),
            ),
            const YBox(16),
            InkWell(
              onTap: () {
                vm.setGenderType(GenderType.ratherNotSay);
                Navigator.pop(context);
              },
              child: ContainerWithBluewishBg(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                  horizontal: Sizer.width(16),
                ),
                child: WalletListTile(
                  title: "Rather not say",
                  isSelected: vm.genderType == GenderType.ratherNotSay,
                ),
              ),
            ),
            const YBox(100),
          ],
        );
      }),
    );
  }
}
