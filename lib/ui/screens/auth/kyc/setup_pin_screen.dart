import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/auth.dart';
import 'package:pinput/pinput.dart';

class SetupPinScreen extends StatefulWidget {
  const SetupPinScreen({Key? key}) : super(key: key);

  @override
  State<SetupPinScreen> createState() => _SetupPinScreenState();
}

class _SetupPinScreenState extends State<SetupPinScreen> {
  FocusNode pinFocusNode = FocusNode();

  @override
  initState() {
    super.initState();
    pinFocusNode.requestFocus();
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.screenHeight * 0.95,
      child: Column(
        children: [
          const YBox(20),
          Container(
            margin: EdgeInsets.symmetric(horizontal: Sizer.width(14)),
            height: Sizer.height(12),
            width: Sizer.screenWidth,
            decoration: BoxDecoration(
              color: AppColors.sheetLightBlue.withOpacity(0.25),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
          ),
          Expanded(
            child: Container(
              width: Sizer.screenWidth,
              decoration: const BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Consumer<TransactionPinVM>(
                builder: (context, vm, _) {
                  return ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                    child: BusyOverlay(
                      show: vm.isBusy,
                      child: Container(
                        color: AppColors.bgWhite,
                        padding:
                            EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Container(
                            //   alignment: Alignment.centerLeft,
                            //   padding: EdgeInsets.only(top: Sizer.height(28)),
                            //   child: BackPop(
                            //     onTap: () {
                            //       Navigator.pop(context);
                            //       vm.pinC.clear();
                            //     },
                            //   ),
                            // ),
                            const YBox(40),
                            const AuthTextSubTitle(
                              title: "Set Up PIN",
                              subtitle:
                                  "As a last layer of security, set up a 4 digit PIN that you will use when making transfers.",
                            ),
                            const YBox(40),
                            Center(
                              child: Pinput(
                                defaultPinTheme: PinInputTheme.defaultPinTheme(
                                    borderRadius: 12),
                                errorPinTheme: PinInputTheme.errorPinTheme(),
                                followingPinTheme: PinInputTheme.followPinTheme(
                                    borderRadius: 12),
                                length: 4,
                                controller: vm.pinC,
                                focusNode: pinFocusNode,
                                showCursor: true,
                                obscureText: true,
                                obscuringWidget: Container(
                                  padding:
                                      EdgeInsets.only(top: Sizer.height(8)),
                                  child: Text('*', style: AppTypography.text36),
                                ),
                                onChanged: (value) {
                                  vm.reBuildUI();
                                  if (value.length == 4) {
                                    pinFocusNode.unfocus();
                                  }
                                },
                                onCompleted: (pin) {},
                              ),
                            ),
                            const YBox(10),
                            // Container(
                            //   alignment: Alignment.center,
                            //   child: Text(
                            //     "Code doesn’t match",
                            //     style: AppTypography.text10.copyWith(
                            //       color: AppColors.alertRed,
                            //       fontWeight: FontWeight.w400,
                            //     ),
                            //   ),
                            // ),
                            const Spacer(),
                            CustomBtn.solid(
                              onTap: () {
                                BsWrapper.bottomSheet(
                                  context: context,
                                  widget: const ConfirmPinScreen(),
                                );
                              },
                              online: vm.pinC.text.length == 4,
                              text: "Continue",
                            ),

                            const YBox(50),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}
