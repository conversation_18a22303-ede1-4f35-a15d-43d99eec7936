import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CompleteProfileNamesPageView extends StatefulWidget {
  const CompleteProfileNamesPageView({
    Key? key,
    this.onPageChange,
    required this.kycVm,
  }) : super(key: key);

  final VoidCallback? onPageChange;
  final KycVM kycVm;

  @override
  State<CompleteProfileNamesPageView> createState() =>
      _CompleteProfileNamesPageViewState();
}

class _CompleteProfileNamesPageViewState
    extends State<CompleteProfileNamesPageView> {
  final FocusNode _fnameFocusNode = FocusNode();
  final FocusNode _lnameFocusNode = FocusNode();
  final FocusNode _mnameFocusNode = FocusNode();

  @override
  void dispose() {
    _fnameFocusNode.dispose();
    _lnameFocusNode.dispose();
    _mnameFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
      child: ListView(
        children: [
          const AuthTextSubTitle(
            title: "Complete your profile",
            subtitle: "Let’s get to know more about you",
          ),
          const YBox(20),
          const InfoContainer(
              text:
                  "Please enter your details as it appears on your government issued identity document."),
          const YBox(20),
          CustomTextField(
            labelText: "First Name",
            showLabelHeader: true,
            borderRadius: Sizer.height(4),
            controller: widget.kycVm.firstNameC,
            onSubmitted: (p0) =>
                FocusScope.of(context).requestFocus(_lnameFocusNode),
            onChanged: (val) => widget.kycVm.reBuildUI(),
          ),
          const YBox(24),
          CustomTextField(
            labelText: "Last Name",
            showLabelHeader: true,
            borderRadius: Sizer.height(4),
            controller: widget.kycVm.lastNameC,
            onSubmitted: (p0) =>
                FocusScope.of(context).requestFocus(_mnameFocusNode),
            onChanged: (val) => widget.kycVm.reBuildUI(),
          ),
          const YBox(24),
          CustomTextField(
            labelText: "Middle Name (optional)",
            showLabelHeader: true,
            borderRadius: Sizer.height(4),
            controller: widget.kycVm.middleNameC,
            onSubmitted: (p0) {
              _submitKycStep1();
            },
            onChanged: (_) => widget.kycVm.reBuildUI(),
          ),
          const YBox(120),
          CustomBtn.solid(
            onTap: () {
              _submitKycStep1();
            },
            online: widget.kycVm.iskycStep1Active,
            text: "Continue",
          ),
          const YBox(50),
        ],
      ),
    );
  }

  _submitKycStep1() {
    widget.kycVm.kycStep1().then((value) {
      if (value.success && widget.onPageChange != null) {
        // showToast(value.message.toString(), true);
        context.read<AuthUserVM>().setUser(value.data);
        widget.onPageChange!();
      } else {
        showToast(value.message.toString(), false);
      }
    });
  }

  showToast(String m, bool isSuccess) {
    FlushBarToast.fLSnackBar(
      message: m,
      snackBarType: isSuccess ? SnackBarType.success : SnackBarType.warning,
    );
  }
}
