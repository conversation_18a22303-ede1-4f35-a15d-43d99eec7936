import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/kyc/kyc.dart';

class CompleteKycScreen extends StatefulWidget {
  const CompleteKycScreen({super.key});

  @override
  State<CompleteKycScreen> createState() => _CompleteKycScreenState();
}

class _CompleteKycScreenState extends State<CompleteKycScreen> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.screenHeight * 0.95,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(20),
          Container(
            margin: EdgeInsets.symmetric(horizontal: Sizer.width(14)),
            height: Sizer.height(12),
            width: Sizer.screenWidth,
            decoration: BoxDecoration(
              color: AppColors.sheetLightBlue.withOpacity(0.25),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              width: Sizer.screenWidth,
              decoration: const BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Column(
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const YBox(40),
                      const AuthTextSubTitle(
                        title: "Complete your KYC",
                        titleColor: AppColors.blue900,
                        subtitle:
                            "We are excited to have you on board. Let’s get started",
                        subtitleColor: AppColors.black600,
                        subtitleFontSize: 14,
                      ),
                      const YBox(24),
                      KycCardSelect(
                        onTap: () {
                          var kycStep =
                              context.read<AuthUserVM>().user?.kycStep;
                          printty(kycStep, level: "step");
                          if (kycStep != null) {
                            printty("Not null");
                            if (kycStep >= 1 && kycStep <= 3) {
                              MixpanelService()
                                  .track('KYC Initiation', properties: {
                                'step': kycStep,
                                'page': 'complete_your_profile',
                                'time': DateTime.now().toIso8601String(),
                              });

                              // Navigator.pop(context);
                              BsWrapper.bottomSheet(
                                context: context,
                                widget: CompleteYourProfileScreen(
                                  pageIndex: kycStep - 1,
                                ),
                              );

                              return;
                            }
                          }

                          return;
                        },
                        btnIsInactive: isKycStepGreaterThanThree(),
                        title: "Complete Your Profile",
                        subtitle: "Fill in your details to get started.",
                        icon: Iconsax.user_edit,
                        isSelected: isKycStepGreaterThanThree(),
                      ),
                      const YBox(16),
                      KycCardSelect(
                        onTap: () {
                          var kycStep =
                              context.read<AuthUserVM>().user?.kycStep;
                          printty(kycStep, level: "step");
                          if (kycStep != null) {
                            printty("Not null");
                            if (kycStep == 4) {
                              MixpanelService()
                                  .track('KYC Initiation', properties: {
                                'step': kycStep,
                                'page': 'identity_verification',
                                'time': DateTime.now().toIso8601String(),
                              });

                              Navigator.pop(context);
                              BsWrapper.bottomSheet(
                                context: context,
                                widget: const IdentityVerificationScreen(),
                              );

                              return;
                            }
                          }
                          return;
                        },
                        btnIsInactive: isKycStepGreaterThanFour(),
                        title: "Identity Verification",
                        subtitle: "Please upload the required documents.",
                        icon: Iconsax.document_text,
                        isSelected: isKycStepGreaterThanFour(),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  bool isKycStepGreaterThanThree() {
    var kycStep = context.watch<AuthUserVM>().user?.kycStep;
    return kycStep != null && kycStep > 3;
  }

  bool isKycStepGreaterThanFour() {
    var kycStep = context.watch<AuthUserVM>().user?.kycStep;
    return kycStep != null && kycStep > 4;
  }
}
