import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/auth.dart';

class IdentityVerificationScreen extends StatefulWidget {
  const IdentityVerificationScreen({Key? key}) : super(key: key);

  @override
  State<IdentityVerificationScreen> createState() =>
      _IdentityVerificationScreenState();
}

class _IdentityVerificationScreenState
    extends State<IdentityVerificationScreen> {
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.screenHeight * 0.95,
      child: Column(
        children: [
          const YBox(20),
          Container(
            margin: EdgeInsets.symmetric(horizontal: Sizer.width(14)),
            height: Sizer.height(12),
            width: Sizer.screenWidth,
            decoration: BoxDecoration(
              color: AppColors.sheetLightBlue.withOpacity(0.25),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
          ),
          Expanded(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
              width: Sizer.screenWidth,
              decoration: const BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.only(top: Sizer.height(28)),
                    child: const BackPop(),
                  ),
                  const YBox(30),
                  const AuthTextSubTitle(
                    title: "Identity Verification",
                    subtitle:
                        "To keep your account secure, we need to verify your identity.",
                  ),
                  const YBox(24),
                  Container(
                    alignment: Alignment.center,
                    child: imageHelper(
                      AppImages.identity,
                      height: Sizer.height(276),
                      width: Sizer.width(276),
                    ),
                  ),
                  const YBox(24),
                  SizedBox(
                    height: Sizer.height(150),
                    // color: AppColors.red,
                    // decoration: BoxDecoration(
                    //   color: AppColors.blue100,
                    //   borderRadius: BorderRadius.circular(8),
                    // ),
                    child: Stack(
                      children: [
                        Positioned(
                          top: 30,
                          right: 0,
                          left: 0,
                          child: Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: Sizer.width(10),
                              vertical: Sizer.height(18),
                            ),
                            decoration: BoxDecoration(
                              color: AppColors.blue100,
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Column(
                              children: [
                                Text(
                                  'Please have one of the following ready to help us verify your identity quickly.',
                                  textAlign: TextAlign.center,
                                  style: AppTypography.text12.copyWith(
                                    color: AppColors.gray600,
                                    fontWeight: FontWeight.w400,
                                    height: 1.2,
                                  ),
                                ),
                                const YBox(12),
                                Row(
                                  children: [
                                    _rowText('International Passport'),
                                    const XBox(40),
                                    _rowText('Resident’s Permit'),
                                  ],
                                ),
                                const YBox(12),
                                Row(
                                  children: [
                                    _rowText('National Identity Card'),
                                    const XBox(40),
                                    _rowText('Driver\'s License'),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        Positioned(
                          right: Sizer.screenWidth / 2 - Sizer.width(50),
                          top: 5,
                          child: Container(
                            padding: EdgeInsets.all(Sizer.radius(10)),
                            decoration: BoxDecoration(
                              color: AppColors.blue100,
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: const Icon(
                              Iconsax.information5,
                              color: AppColors.blue800,
                              size: 26,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Spacer(),
                  Consumer<KycVM>(builder: (context, vm, _) {
                    return CustomBtn.solid(
                      onTap: () {
                        _startTheOnfidoWorkflow();
                      },
                      online: true,
                      isLoading: vm.isBusy,
                      text: "Continue",
                    );
                  }),
                  const YBox(50),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Row _rowText(String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          height: Sizer.height(6),
          width: Sizer.width(6),
          decoration: BoxDecoration(
            color: AppColors.blue800,
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        const XBox(8),
        Text(
          text,
          style: AppTypography.text12.copyWith(
            color: AppColors.blue800,
            fontWeight: FontWeight.w500,
            height: 1.2,
          ),
        ),
      ],
    );
  }

  _startTheOnfidoWorkflow() {
    var kycVM = context.read<KycVM>();
    kycVM.generateOnfidoToken().then((value) {
      if (value.success) {
        printty("Onfido Token Generated");
        kycVM
            .startOnfidoWorkflow(
          sdkToken: kycVM.onfidoData!.sdkToken!,
          workFlowRunId: kycVM.onfidoData!.workflowRunId!,
        )
            .then((value) {
          if (value) {
            printty("Onfido Workflow Completed");
            kycVM.updateOnfidoUser();
            _gotoNext();
          } else {
            printty("Something went wrong 1");
            _somethingWentWrong();
          }
        });
      } else {
        _somethingWentWrong();
        printty("Something went wrong 2");
      }
    });
  }

  _gotoNext() {
    Navigator.pop(context);
    BsWrapper.bottomSheet(
      context: context,
      widget: SuccessSheet(
        arg: ConfirmationArg(
          title: "Verification Successful",
          buttonText: 'Home',
          subtitle: const ConfirmationSubtitleText(
            startText: "Your",
            endText: " documents have been sent and is undergoing review.",
          ),
          onBtnTap: () {
            // Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
            Navigator.pushReplacementNamed(
                NavigatorKeys.appNavigatorKey.currentContext!,
                RoutePath.dashboardNav);
          },
        ),
      ),
    );
  }

  _somethingWentWrong() {
    BsWrapper.bottomSheet(
      context: context,
      widget: SuccessSheet(
        arg: ConfirmationArg(
            title: "Something went wrong.",
            buttonText: 'Continue',
            imagePath: AppImages.circleError,
            subtitle: const ConfirmationSubtitleText(
              startText: "We don’t ",
              endText:
                  " really know what’s wrong. Please try again and continue.",
            ),
            onBtnTap: () {
              Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
              Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext!);
              // BsWrapper.bottomSheet(
              //   context: context,
              //   widget: const SetupPinScreen(),
              // );
            }),
      ),
    );
  }
}
