import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/auth.dart';
import 'package:pinput/pinput.dart';

class ConfirmPinScreen extends StatefulWidget {
  const ConfirmPinScreen({Key? key}) : super(key: key);

  @override
  State<ConfirmPinScreen> createState() => _ConfirmPinScreenState();
}

class _ConfirmPinScreenState extends State<ConfirmPinScreen> {
  FocusNode pinFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    pinFocusNode.requestFocus();
    KeyboardOverlay.addRemoveFocusNode(context, pinFocusNode);
  }

  @override
  void dispose() {
    pinFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.screenHeight * 0.95,
      child: Column(
        children: [
          const YBox(20),
          Container(
            margin: EdgeInsets.symmetric(horizontal: Sizer.width(14)),
            height: Sizer.height(12),
            width: Sizer.screenWidth,
            decoration: BoxDecoration(
              color: AppColors.sheetLightBlue.withOpacity(0.25),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(8),
                topRight: Radius.circular(8),
              ),
            ),
          ),
          Expanded(
            child: Container(
              width: Sizer.screenWidth,
              decoration: const BoxDecoration(
                color: AppColors.bgWhite,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Consumer<TransactionPinVM>(
                builder: (context, vm, _) {
                  return ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(8),
                      topRight: Radius.circular(8),
                    ),
                    child: BusyOverlay(
                      show: vm.isBusy,
                      child: Container(
                        color: AppColors.bgWhite,
                        padding:
                            EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              alignment: Alignment.centerLeft,
                              padding: EdgeInsets.only(top: Sizer.height(28)),
                              child: BackPop(
                                onTap: () {
                                  Navigator.pop(context);
                                  vm.pinConfirmC.clear();
                                },
                              ),
                            ),
                            const YBox(30),
                            const AuthTextSubTitle(
                              title: "Confirm PIN",
                              subtitle:
                                  "As a last layer of security, set up a 4 digit PIN that you will use when making transfers.",
                            ),
                            const YBox(40),
                            Center(
                              child: Pinput(
                                defaultPinTheme: PinInputTheme.defaultPinTheme(
                                    borderRadius: 12),
                                errorPinTheme: PinInputTheme.errorPinTheme(),
                                followingPinTheme: PinInputTheme.followPinTheme(
                                    borderRadius: 12),
                                length: 4,
                                controller: vm.pinConfirmC,
                                focusNode: pinFocusNode,
                                showCursor: true,
                                obscureText: true,
                                obscuringWidget: Container(
                                  padding:
                                      EdgeInsets.only(top: Sizer.height(8)),
                                  child: Text('*', style: AppTypography.text36),
                                ),
                                onChanged: (value) {
                                  if (!vm.isPinMatched &&
                                      vm.pinConfirmC.text.trim().length == 4) {
                                    // vm.pinConfirmC.clear();
                                  }
                                  vm.reBuildUI();
                                },
                                onCompleted: (pin) {},
                              ),
                            ),
                            const YBox(10),
                            Visibility(
                              visible: !vm.isPinMatched &&
                                  vm.pinConfirmC.text.trim().length == 4,
                              child: AnimatedOpacity(
                                duration: const Duration(milliseconds: 1000),
                                opacity: (!vm.isPinMatched &&
                                        vm.pinConfirmC.text.trim().length == 4)
                                    ? 1
                                    : 0,
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 1000),
                                  curve: Curves.easeInOutCubic,
                                  alignment: Alignment.center,
                                  child: Text(
                                    "Code doesn’t match",
                                    style: AppTypography.text10.copyWith(
                                      color: AppColors.alertRed,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            const Spacer(),
                            CustomBtn.solid(
                              onTap: () {
                                _setTransactionPin();
                              },
                              online: vm.isPinMatched &&
                                  vm.pinConfirmC.text.length == 4,
                              text: "Continue",
                            ),
                            const YBox(50),
                          ],
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  _setTransactionPin() {
    var vm = context.read<TransactionPinVM>();
    vm.createTransactionPin().then((value) {
      if (value.success) {
        vm.clearData();
        Navigator.pop(context);
        Navigator.pop(context);
        _openSuccessSheet();
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }

  _openSuccessSheet() {
    BsWrapper.bottomSheet(
      context: context,
      widget: SuccessSheet(
        arg: ConfirmationArg(
          title: "Success",
          buttonText: 'Home',
          subtitle: const ConfirmationSubtitleText(
            startText: "Your PIN ",
            endText:
                "has been set up. Next up, fund your account and start moving money",
          ),
          onBtnTap: () {
            Navigator.pushNamedAndRemoveUntil(
              NavigatorKeys.appNavigatorKey.currentContext!,
              RoutePath.dashboardNav,
              (route) => false,
            );
          },
        ),
      ),
    );
  }
}
