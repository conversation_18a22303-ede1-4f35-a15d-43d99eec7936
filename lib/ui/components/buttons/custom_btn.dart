import 'package:korrency/core/core.dart';

class CustomBtn {
  static Widget solid({
    required Function()? onTap,
    bool online = true,
    bool isOutline = false,
    required String text,
    bool isLoading = false,
    BorderRadiusGeometry? borderRadius,
    double? width,
    double? height,
    Color? offlineColor,
    Color? onlineColor,
    Color? outlineColor,
    Color? textColor,
    TextStyle? textStyle,
  }) {
    return IgnorePointer(
      ignoring: !online || isLoading,
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: width ?? Sizer.screenWidth,
          height: Sizer.height(height ?? 56),
          decoration: (online && !isLoading)
              ? BoxDecoration(
                  borderRadius: borderRadius,
                  color: isOutline
                      ? AppColors.transparent
                      : onlineColor ?? AppColors.primaryBlue,
                  border: isOutline
                      ? Border.all(color: outlineColor ?? AppColors.primaryBlue)
                      : null,
                )
              : BoxDecoration(
                  borderRadius: borderRadius,
                  color: offlineColor ?? AppColors.baseGray,
                ),
          child: Center(
            child: isLoading
                ? const FittedBox(
                    child: CircularProgressIndicator(
                      color: AppColors.white,
                    ),
                  )
                : Text(
                    text,
                    style: textStyle ??
                        AppTypography.text16.copyWith(
                          fontWeight: FontWeight.w500,
                          color: online
                              ? textColor ?? AppColors.white
                              : AppColors.gray500,
                        ),
                  ),
          ),
        ),
      ),
    );
  }

  static Widget withChild({
    required Function()? onTap,
    bool online = true,
    BorderRadiusGeometry? borderRadius,
    EdgeInsetsGeometry? padding,
    double? width,
    double? height,
    Color? offlineColor,
    Color? onlineColor,
    required Widget child,
  }) {
    return IgnorePointer(
      ignoring: !online,
      child: InkWell(
        onTap: onTap,
        child: Container(
          width: width ?? Sizer.screenWidth,
          height: Sizer.height(height ?? 56),
          padding: padding ??
              EdgeInsets.symmetric(
                vertical: Sizer.height(14),
                horizontal: Sizer.width(10),
              ),
          decoration: online
              ? BoxDecoration(
                  borderRadius: borderRadius,
                  color: onlineColor ?? AppColors.primaryBlue,
                )
              : BoxDecoration(
                  borderRadius: borderRadius,
                  color: offlineColor ?? AppColors.baseGray,
                ),
          child: Center(child: child),
        ),
      ),
    );
  }
}
