import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';

class CurrencyCards extends StatelessWidget {
  const CurrencyCards({
    super.key,
    this.currencyCode,
    this.showCurrencyCode = false,
    this.showArrowIcon = true,
    this.isNetworkSvg = false,
    required this.flagIconPath,
    this.bgColor,
    this.onTap,
  });

  final String? currencyCode;
  final bool showCurrencyCode;
  final bool showArrowIcon;
  final bool isNetworkSvg;
  final String flagIconPath;
  final Color? bgColor;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(
          horizontal: Sizer.width(16),
          vertical: Sizer.height(6),
        ),
        decoration: BoxDecoration(
          color: bgColor ?? AppColors.blue100,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            isNetworkSvg
                ? SvgPicture.network(
                    flagIconPath,
                    height: Sizer.height(16),
                    width: Sizer.width(20),
                  )
                : imageHelper(
                    flagIconPath,
                    height: Sizer.height(20),
                    width: Sizer.width(20),
                  ),
            const XBox(6),
            if (showCurrencyCode)
              Text(
                currencyCode!,
                style: AppTypography.text12.copyWith(
                    color: AppColors.primaryBlue, fontWeight: FontWeight.w500),
              ),
            if (showArrowIcon)
              const Icon(
                Icons.expand_more,
                color: AppColors.primaryBlue,
                size: 25,
              ),
          ],
        ),
      ),
    );
  }
}
