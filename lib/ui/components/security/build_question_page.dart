import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class BuildQuestionPage extends StatelessWidget {
  const BuildQuestionPage({
    super.key,
    required this.index,
    required this.question,
    required this.answerControllers,
  });

  final int index;
  final SecurityQuestion question;
  final List<TextEditingController> answerControllers;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
      child: Container(
        padding: EdgeInsets.symmetric(
          vertical: Sizer.height(16),
          horizontal: Sizer.width(28),
        ),
        decoration: BoxDecoration(
          color: AppColors.grayFE,
          borderRadius: BorderRadius.circular(Sizer.height(12)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Align(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Question ${index + 1}",
                    style:
                        FontTypography.text12.withCustomColor(AppColors.gray93),
                  ),
                  YBox(8),
                  Container(
                    height: Sizer.height(1),
                    width: Sizer.width(90),
                    color: AppColors.grayEC,
                  ),
                ],
              ),
            ),
            YBox(20),
            Text(
              question.question ?? "Security Question",
              style: FontTypography.text22.semiBold,
            ),
            Spacer(),
            Text(
              "Your answer",
              style: FontTypography.text12.withCustomColor(AppColors.gray93),
            ),
            YBox(8),
            Container(
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.grayAB),
                borderRadius: BorderRadius.circular(Sizer.height(12)),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                      left: Sizer.width(12),
                      top: Sizer.height(10),
                    ),
                    child: Icon(
                      Iconsax.message_edit,
                      color: AppColors.gray500,
                      size: Sizer.height(20),
                    ),
                  ),
                  Expanded(
                    child: CustomTextField(
                      controller: answerControllers[index],
                      hideBorder: true,
                      borderRadius: Sizer.height(12),
                      maxLines: 3,
                      contentPadding: EdgeInsets.only(
                        left: Sizer.width(8),
                        top: Sizer.height(20),
                      ),
                      onChanged: (val) {
                        // Validation is handled by the controller listener
                      },
                    ),
                  ),
                ],
              ),
            ),
            // YBox(40),
          ],
        ),
      ),
    );
  }
}
