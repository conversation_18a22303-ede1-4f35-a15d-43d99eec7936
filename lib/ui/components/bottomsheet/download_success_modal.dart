import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DownloadSuccessModal extends StatefulWidget {
  const DownloadSuccessModal({super.key});

  @override
  State<DownloadSuccessModal> createState() => _DownloadSuccessModalState();
}

class _DownloadSuccessModalState extends State<DownloadSuccessModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(44)),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            YB<PERSON>(60),
            svg<PERSON>elper(
              AppSvgs.tickOn,
              height: Sizer.height(140),
            ),
            YBox(30),
            Text(
              'Download Successful!',
              textAlign: TextAlign.center,
              style: FontTypography.text18.semiBold,
            ),
            YBox(4),
            Text(
              'Your receipt has been successfully \ndownloaded',
              textAlign: TextAlign.center,
              style: FontTypography.text14.withCustomColor(
                AppColors.gray93,
              ),
            ),
            const YBox(36),
            CustomBtn.solid(
              borderRadius: BorderRadius.circular(20),
              onTap: () {
                Navigator.pop(context);
                BsWrapper.bottomSheet(
                  context: context,
                  widget: const RateErrorModal(),
                );
              },
              text: "Back to Home page",
            ),
            const YBox(30),
          ],
        ),
      ),
    );
  }
}
