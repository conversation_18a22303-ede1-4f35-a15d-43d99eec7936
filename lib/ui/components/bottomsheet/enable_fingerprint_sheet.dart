import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class EnableFingerprintSheet extends StatelessWidget {
  const EnableFingerprintSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YB<PERSON>(70),
          image<PERSON><PERSON><PERSON>(
            AppImages.fingerPrint,
            height: Sizer.height(126),
            width: Sizer.width(113),
          ),
          const YBox(40),
          Text(
            "Biometrics not enabled",
            style: AppTypography.text20.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
          const YBox(8),
          Text(
            "For faster and more secure log in, enable your Fingerprint/Face ID.",
            textAlign: TextAlign.center,
            style: AppTypography.text16.copyWith(color: AppColors.gray700),
          ),
          const <PERSON><PERSON><PERSON>(80),
        ],
      ),
    );
  }
}
