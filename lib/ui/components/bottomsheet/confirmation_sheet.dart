import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ConfirmationSheet extends StatelessWidget {
  const ConfirmationSheet({
    Key? key,
    this.title,
    required this.message,
    this.firstBtnText,
    this.secondBtnText,
    this.centerWidget,
    this.loading = false,
    this.firstBtnTap,
    this.secendBtnTap,
  }) : super(key: key);

  final String? title;
  final String message;
  final String? firstBtnText;
  final String? secondBtnText;
  final Widget? centerWidget;
  final bool loading;
  final VoidCallback? firstBtnTap;
  final VoidCallback? secendBtnTap;

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      // height: Sizer.screenHeight * 0.50,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const YBox(10),
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Icons.close,
                size: Sizer.width(25),
                color: AppColors.gray500,
              ),
            ),
          ),
          imageHelper(
            AppImages.warning,
            height: Sizer.height(100),
            width: Sizer.width(100),
          ),
          const YBox(10),
          if (title != null)
            Text(
              (title ?? ''),
              style: AppTypography.text20.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          if (title != null) const YBox(4),
          Text(
            message,
            textAlign: TextAlign.center,
            style: AppTypography.text16.copyWith(
              color: AppColors.textBlack800,
            ),
          ),
          Container(child: centerWidget),
          const YBox(40),
          CustomBtn.solid(
            onTap: firstBtnTap ??
                () {
                  Navigator.pop(context);
                },
            online: true,
            isLoading: loading,
            text: firstBtnText ?? "Cancel",
          ),
          if (secendBtnTap != null) const YBox(16),
          if (secendBtnTap != null)
            CustomBtn.solid(
              height: Sizer.height(56),
              isOutline: true,
              textColor: AppColors.primaryBlue,
              onTap: secendBtnTap ??
                  () {
                    Navigator.pop(context);
                  },
              online: true,
              isLoading: loading,
              text: secondBtnText ?? "Ok",
            ),
          const YBox(50),
        ],
      ),
    );
  }
}
