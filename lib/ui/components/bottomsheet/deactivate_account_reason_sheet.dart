import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class DeactivateAccountReasonSheet extends StatefulWidget {
  const DeactivateAccountReasonSheet({
    Key? key,
  }) : super(key: key);

  @override
  State<DeactivateAccountReasonSheet> createState() =>
      _DeactivateAccountReasonSheetState();
}

class _DeactivateAccountReasonSheetState
    extends State<DeactivateAccountReasonSheet> {
  TextEditingController descriptionC = TextEditingController();
  FocusNode descriptionFocus = FocusNode();

  @override
  void initState() {
    super.initState();
    descriptionFocus.requestFocus();
  }

  @override
  void dispose() {
    descriptionC.dispose();
    descriptionFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (ctx, vm, _) {
      return Container(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        margin: EdgeInsets.only(
          top: Sizer.height(80),
        ),
        child: ContainerWithTopBorderRadius(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const YBox(20),
              Align(
                alignment: Alignment.centerRight,
                child: InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: const Icon(
                    Icons.close,
                    size: 24,
                  ),
                ),
              ),
              const YBox(20),
              Text(
                'Tell Us More About Your Reason',
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  color: AppColors.black,
                  fontSize: Sizer.text(20),
                ),
              ),
              const YBox(4),
              Text(
                'We’re always looking to improve. Please \nshare any specific reasons for leaving.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: AppColors.gray700,
                  fontSize: Sizer.text(16),
                ),
              ),
              const YBox(20),
              CustomTextField(
                focusNode: descriptionFocus,
                controller: descriptionC,
                labelText: "Enter your reasons",
                showLabelHeader: true,
                maxLines: 4,
                onChanged: (_) => vm.reBuildUI(),
              ),
              const YBox(40),
              CustomBtn.solid(
                online: descriptionC.text.trim().isNotEmpty,
                onTap: () {
                  Navigator.pop(context);
                  vm.setSelectedReason(
                    DeactivateAccountMessage(
                      description: descriptionC.text.trim(),
                      reason: 'Other',
                    ),
                  );
                  BsWrapper.bottomSheet(
                    context: context,
                    widget: const DeactivateAccountSheet(),
                  );
                },
                text: "Continue",
              ),
              const YBox(40)
            ],
          ),
        ),
      );
    });
  }
}
