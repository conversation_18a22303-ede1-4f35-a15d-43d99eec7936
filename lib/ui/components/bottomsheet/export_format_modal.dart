import 'package:korrency/core/core.dart';

class ExportFormatModal extends StatefulWidget {
  const ExportFormatModal({
    super.key,
    this.isDownload = false,
    required this.asImage,
    required this.asPdf,
  });

  final bool isDownload;
  final VoidCallback asImage;
  final VoidCallback asPdf;

  @override
  State<ExportFormatModal> createState() => _ExportFormatModalState();
}

class _ExportFormatModalState extends State<ExportFormatModal> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(Sizer.radius(12)),
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: Sizer.width(44)),
        decoration: BoxDecoration(
          color: AppColors.bgWhite,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            YB<PERSON>(24),
            Text(
              'We would love to hear from you',
              textAlign: TextAlign.center,
              style: FontTypography.text16.withCustomColor(
                AppColors.gray51,
              ),
            ),
            const YBox(30),
            Row(
              children: [
                Expanded(
                  child: ExportWidget(
                    svgPath: AppSvgs.galleryExport,
                    desc:
                        '${widget.isDownload ? 'Download' : 'Export'} as Image',
                    onTap: widget.asImage,
                  ),
                ),
                const XBox(38),
                Expanded(
                  child: ExportWidget(
                    svgPath: AppSvgs.pdfExport,
                    desc: '${widget.isDownload ? 'Download' : 'Export'} as PDF',
                    onTap: widget.asPdf,
                    // onTap: () {
                    //   BsWrapper.bottomSheet(
                    //     context: context,
                    //     widget: DownloadSuccessModal(),
                    //   );
                    // },
                  ),
                )
              ],
            ),
            const YBox(50),
          ],
        ),
      ),
    );
  }
}

class ExportWidget extends StatelessWidget {
  const ExportWidget({
    super.key,
    required this.svgPath,
    required this.desc,
    this.onTap,
  });

  final String svgPath;
  final String desc;
  final VoidCallback? onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            height: Sizer.height(120),
            decoration: BoxDecoration(
              color: AppColors.blue8FF,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: svgHelper(
                svgPath,
                height: Sizer.height(60),
                width: Sizer.width(60),
              ),
            ),
          ),
          const YBox(12),
          Text(
            desc,
            style: FontTypography.text14.withCustomColor(
              AppColors.primaryBlue,
            ),
          ),
        ],
      ),
    );
  }
}
