import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';

class ColumnText extends StatelessWidget {
  const ColumnText({
    super.key,
    required this.text,
    required this.amount,
    this.showImage = false,
    this.isSvgNetwork = false,
    this.image,
  });

  final String text;
  final String amount;
  final bool showImage;
  final String? image;
  final bool isSvgNetwork;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if (showImage)
          isSvgNetwork
              ? SvgPicture.network(
                  image!,
                  height: Sizer.height(14),
                  width: Sizer.width(20),
                )
              : imageHelper(
                  image!,
                  height: Sizer.height(14),
                  width: Sizer.width(20),
                ),
        if (showImage) const YBox(8),
        Text(
          text,
          style: AppTypography.text12.copyWith(
            color: AppColors.blue800,
            fontWeight: FontWeight.w400,
          ),
        ),
        Text(
          amount,
          style: AppTypography.text14.copyWith(
            color: AppColors.blue800,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
