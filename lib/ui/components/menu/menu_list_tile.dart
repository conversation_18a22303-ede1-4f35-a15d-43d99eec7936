import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';

class MenuListTile extends StatelessWidget {
  const MenuListTile({
    super.key,
    this.iconData,
    required this.title,
    this.trailingWidget,
    this.useImageIcon = false,
    this.isSvg = false,
    this.showTrailing = true,
    this.imageIcon = AppImages.iconLogo,
    this.bgColor,
    this.iconColor,
    this.onPressed,
  });

  final IconData? iconData;
  final String title;
  final Widget? trailingWidget;
  final bool useImageIcon;
  final bool isSvg;
  final bool showTrailing;
  final String imageIcon;
  final Color? bgColor;
  final Color? iconColor;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(8)),
            decoration: BoxDecoration(
              color: bgColor ?? AppColors.blue100,
              borderRadius: BorderRadius.circular(Sizer.radius(4)),
            ),
            child: useImageIcon
                ? isSvg
                    ? svgHelper(imageIcon, height: Sizer.height(24))
                    : imageHelper(imageIcon, height: Sizer.height(24))
                : iconData != null
                    ? Icon(
                        iconData,
                        size: Sizer.radius(24),
                        color: iconColor ?? AppColors.lightBlue,
                      )
                    : const SizedBox.shrink(),
          ),
          const XBox(8),
          Text(title,
              style: AppTypography.text16.copyWith(
                color: iconColor ?? AppColors.iconBlack800,
              )),
          if (showTrailing) const Spacer(),
          if (showTrailing)
            Container(
              child: trailingWidget ??
                  Icon(
                    Iconsax.arrow_right_3,
                    size: Sizer.radius(24),
                    color: AppColors.iconBlack800,
                  ),
            ),
        ],
      ),
    );
  }
}

class CustomMenuItem extends StatelessWidget {
  const CustomMenuItem({
    super.key,
    this.icon,
    required this.title,
    this.trailingWidget,
    this.showTrailing = false,
    this.bgColor,
    this.iconColor,
    this.onPressed,
  });

  final dynamic icon;
  final String title;
  final Widget? trailingWidget;
  final bool showTrailing;
  final Color? bgColor;
  final Color? iconColor;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onPressed,
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(Sizer.radius(8)),
            decoration: BoxDecoration(
              color: bgColor ?? AppColors.grayFE,
              borderRadius: BorderRadius.circular(Sizer.radius(20)),
            ),
            child: icon is IconData
                ? Icon(
                    icon,
                    size: Sizer.radius(24),
                    color: iconColor ?? AppColors.lightBlue,
                  )
                : SvgPicture.asset(
                    icon,
                    height: Sizer.height(24),
                    width: Sizer.width(24),
                    colorFilter: ColorFilter.mode(
                      iconColor ?? AppColors.lightBlue,
                      BlendMode.srcIn,
                    ),
                  ),
          ),
          const XBox(8),
          Text(title,
              style: FontTypography.text16.withCustomColor(AppColors.black22)),
          if (showTrailing) const Spacer(),
          if (showTrailing)
            Container(
              child: trailingWidget ??
                  Icon(
                    Iconsax.arrow_right_3,
                    size: Sizer.radius(24),
                    color: AppColors.iconBlack800,
                  ),
            ),
        ],
      ),
    );
  }
}
