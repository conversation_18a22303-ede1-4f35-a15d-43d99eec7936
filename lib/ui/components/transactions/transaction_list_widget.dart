import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/transactions/transaction_details_screen.dart';

class TransactionListWidget extends StatelessWidget {
  final List<Transaction> transactionList;
  final String title;

  const TransactionListWidget({
    super.key,
    required this.transactionList,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    if (transactionList.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        const YBox(24),
        Text(
          title,
          style: AppTypography.text14.copyWith(
            color: AppColors.gray500,
          ),
        ),
        const YBox(16),
        ListView.separated(
          shrinkWrap: true,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          itemBuilder: (ctx, i) {
            var transaction = transactionList[i];
            return TransactionCard(
              category: transaction.category ?? '',
              type: transaction.type ?? '',
              subTitleWithColor: true,
              title: transaction.description ?? '',
              subTitle: transaction.status ?? '',
              amount:
                  "${AppUtils.formatAmountDoubleString(transaction.amount ?? "0")}  ${transaction.currency?.code ?? ""}",
              onTap: () {
                Navigator.of(context).pushNamed(
                    RoutePath.transactionDetailsScreen,
                    arguments: TransactionArg(transaction: transaction));
              },
            );
          },
          separatorBuilder: (ctx, _) => const YBox(20),
          itemCount: transactionList.length,
        ),
      ],
    );
  }
}
