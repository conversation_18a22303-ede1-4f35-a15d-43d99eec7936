import 'package:korrency/core/core.dart';

class TransactionListTile extends StatelessWidget {
  const TransactionListTile({
    super.key,
    required this.leftText,
    required this.rightText,
    this.rightTextColor,
    this.reightTextStyle,
    this.showBorder = true,
    this.onCopy,
  });

  final String leftText;
  final String rightText;
  final Color? rightTextColor;
  final TextStyle? reightTextStyle;
  final bool showBorder;
  final VoidCallback? onCopy;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: Sizer.height(16),
      ),
      decoration: BoxDecoration(
        border: showBorder
            ? Border(
                bottom: BorderSide(
                  color: AppColors.grayF4,
                  width: 1,
                ),
              )
            : null,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 4,
            child: Text(
              leftText,
              style: FontTypography.text14.withCustomColor(
                AppColors.gray500,
              ),
            ),
          ),
          const XBox(10),
          Expanded(
            flex: 5,
            child: Text(
              rightText,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              textAlign: TextAlign.end,
              style: reightTextStyle ??
                  FontTypography.text16.medium.withCustomColor(
                    rightTextColor ?? AppColors.gray700,
                  ),
            ),
          ),
          if (onCopy != null)
            Padding(
              padding: EdgeInsets.only(
                left: Sizer.width(10),
              ),
              child: InkWell(
                onTap: onCopy,
                child: svgHelper(AppSvgs.copy),
              ),
            )
        ],
      ),
    );
  }
}
