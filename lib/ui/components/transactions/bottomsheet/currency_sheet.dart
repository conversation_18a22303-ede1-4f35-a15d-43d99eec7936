import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class CurrencySheet extends StatefulWidget {
  const CurrencySheet({Key? key}) : super(key: key);

  @override
  State<CurrencySheet> createState() => _CurrencySheetState();
}

class _CurrencySheetState extends State<CurrencySheet> {
  @override
  Widget build(BuildContext context) {
    var currencyVm = context.read<CurrencyVM>();
    return ContainerWithTopBorderRadius(
      height:
          Sizer.screenHeight * (currencyVm.currencies.length > 2 ? 0.76 : 0.59),
      child: Consumer<TransactionVM>(builder: (context, vm, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Currencies",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    size: Sizer.radius(23),
                  ),
                )
              ],
            ),
            const YBox(25),
            Expanded(
              child: Builder(builder: (context) {
                if (currencyVm.currencies.isEmpty) {
                  return SizedBox(
                    height: Sizer.height(200),
                    child: Center(
                      child: Text(
                        "No currency available",
                        style: AppTypography.text16.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppColors.gray500),
                      ),
                    ),
                  );
                }
                return ListView.separated(
                  padding: EdgeInsets.symmetric(
                    vertical: Sizer.width(24),
                  ),
                  shrinkWrap: true,
                  // physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (ctx, i) {
                    var currency = currencyVm.currencies[i];
                    return InkWell(
                      onTap: () {
                        vm.setSelectedCurrency(currency);
                      },
                      child: ContainerWithBluewishBg(
                        padding: EdgeInsets.symmetric(
                          vertical: Sizer.height(20),
                          horizontal: Sizer.width(16),
                        ),
                        child: WalletListTile(
                          title: currency.name ?? "",
                          isSelected: vm.selectedCurrency == currency,
                          useNetworkSvg: true,
                          currencyIcon: currency.flag,
                          icon: Icons.check_circle,
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (ctx, _) => const YBox(24),
                  itemCount: currencyVm.currencies.length,
                );
              }),
            ),
            const YBox(20),
            CustomBtn.solid(
              onTap: () {
                vm.getFilteredTransactions(
                  currencyId: vm.selectedCurrency?.id,
                );
                vm.clearDates();
                vm.setStatusFilter(clear: true);
                Navigator.pop(context);
              },
              online: vm.selectedCurrency != null,
              text: "Apply Filter",
            ),
            const YBox(40),
          ],
        );
      }),
    );
  }
}
