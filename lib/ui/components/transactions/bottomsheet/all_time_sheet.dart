import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class AllTimeSheet extends StatefulWidget {
  const AllTimeSheet({Key? key}) : super(key: key);

  @override
  State<AllTimeSheet> createState() => _AllTimeSheetState();
}

class _AllTimeSheetState extends State<AllTimeSheet> {
  DateTime _fromWhatDate = DateTime.now();
  DateTime _toWhatDate = DateTime.now();
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      height: Sizer.screenHeight * 0.59,
      child: Consumer<TransactionVM>(builder: (context, vm, _) {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Choose Range ",
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                InkWell(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Icon(
                    Icons.close,
                    size: Sizer.radius(23),
                  ),
                )
              ],
            ),
            const YBox(25),
            CustomTextField(
              isReadOnly: true,
              height: Sizer.height(56),
              contentPadding: EdgeInsets.symmetric(
                horizontal: Sizer.width(10),
                vertical: Sizer.height(20),
              ),
              labelText: "From what date",
              // hintText: '17 Feb 2024',
              showLabelHeader: true,
              borderRadius: Sizer.height(4),
              showSuffixIcon: true,
              suffixIcon: Icon(
                Icons.event,
                color: AppColors.gray500,
                size: Sizer.height(26),
              ),
              controller: vm.startDateFormattedC,
              onTap: () {
                showCupertinoDatePicker(
                  context,
                  onDateTimeChanged: (val) {
                    // Check if val is greater now
                    if (val.isAfter(DateTime.now())) {
                      _fromWhatDate = DateTime.now();
                      return;
                    }

                    _fromWhatDate = val;
                  },
                  onDone: () {
                    vm.startDateFormattedC.text =
                        AppUtils.dayWithSuffixMonthAndYear(_fromWhatDate);
                    vm.startDateC.text =
                        _fromWhatDate.toIso8601String().split("T").first;
                    vm.reBuildUI();
                    Navigator.pop(context);
                  },
                );
              },
              onChanged: (val) {},
            ),
            const YBox(30),
            CustomTextField(
              isReadOnly: true,
              height: Sizer.height(56),
              contentPadding: EdgeInsets.symmetric(
                horizontal: Sizer.width(10),
                vertical: Sizer.height(20),
              ),
              labelText: "To what date",
              // hintText: '17 Feb 2024',
              showLabelHeader: true,
              borderRadius: Sizer.height(4),
              showSuffixIcon: true,
              suffixIcon: Icon(
                Icons.event,
                color: AppColors.gray500,
                size: Sizer.height(26),
              ),
              controller: vm.endDateFormattedC,
              onTap: () {
                showCupertinoDatePicker(
                  context,
                  minimumDate: _fromWhatDate,
                  onDateTimeChanged: (val) {
                    if (val.isAfter(DateTime.now())) {
                      _toWhatDate = DateTime.now();
                      return;
                    }
                    _toWhatDate = val;
                  },
                  onDone: () {
                    vm.endDateFormattedC.text =
                        AppUtils.dayWithSuffixMonthAndYear(_toWhatDate);
                    vm.endDateC.text =
                        _toWhatDate.toIso8601String().split("T").first;
                    vm.reBuildUI();
                    Navigator.pop(context);
                  },
                );
              },
              onChanged: (val) {},
            ),
            const Spacer(),
            CustomBtn.solid(
              onTap: () {
                vm.getFilteredTransactions(
                  startDate: vm.startDateC.text,
                  endDate: vm.endDateC.text,
                );
                vm.setStatusFilter(clear: true);
                vm.setSelectedCurrency(null);
                Navigator.pop(context);
              },
              online: vm.dateBtnIsActive,
              text: "Apply Filter",
            ),
            const YBox(40),
          ],
        );
      }),
    );
  }
}
