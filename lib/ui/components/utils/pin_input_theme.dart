import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:korrency/core/helpers/sizer.dart';
import 'package:korrency/core/themes/themes.dart';
import 'package:pinput/pinput.dart';

class PinInputTheme {
  static PinTheme defaultPinTheme({double? borderRadius, Color? bgColor}) {
    return PinTheme(
      width: 47.w,
      height: 47.w,
      textStyle: TextStyle(
          fontSize: 15.sp,
          color: AppColors.baseBlack,
          fontWeight: FontWeight.w500),
      decoration: BoxDecoration(
        color: bgColor ?? AppColors.gray300,
        border: Border.all(
          color: AppColors.gray300,
          width: 0,
        ),
        borderRadius: BorderRadius.circular(borderRadius ?? 4.r),
      ),
    );
  }

  static errorPinTheme() {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(width: 1, color: AppColors.red),
    );
  }

  static followPinTheme({double? borderRadius}) {
    return defaultPinTheme().copyDecorationWith(
      border: Border.all(width: Sizer.width(0.73), color: AppColors.gray500),
      borderRadius: BorderRadius.circular(borderRadius ?? 4.r),
      color: AppColors.bgWhite,
    );
  }

  static PinTheme changeDefaultPinTheme(
      {double? borderRadius, Color? bgColor}) {
    return PinTheme(
      height: Sizer.height(56),
      width: Sizer.width(46),
      textStyle: FontTypography.text22.semiBold,
      decoration: BoxDecoration(
        color: bgColor ?? AppColors.gray300,
        border: Border.all(
          color: AppColors.gray300,
          width: 0,
        ),
        borderRadius: BorderRadius.circular(Sizer.radius(12)),
      ),
    );
  }

  static changePinTheme() {
    return changeDefaultPinTheme().copyWith(
      height: Sizer.height(56),
      width: Sizer.width(46),
      decoration: BoxDecoration(
        border: Border.all(
          color: AppColors.grayAB,
        ),
        borderRadius: BorderRadius.circular(Sizer.radius(12)),
      ),
    );
  }
}
