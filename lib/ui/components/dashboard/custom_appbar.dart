import 'package:korrency/core/core.dart';

class CustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  const CustomAppbar({
    super.key,
    this.showBackBtn = false,
    this.showHeaderTitle = false,
    this.isHeaderText = true,
    this.color,
    this.headerText,
    this.headerWidget,
    this.rightWidget,
    this.onBackBtnTap,
  });

  final bool showBackBtn;
  final bool showHeaderTitle;
  final bool isHeaderText;
  final Color? color;
  final String? headerText;
  final Widget? headerWidget;
  final Widget? rightWidget;
  final VoidCallback? onBackBtnTap;

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(Sizer.height(50)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
              vertical: Sizer.height(10),
            ),
            child: Row(
              mainAxisAlignment: showHeaderTitle
                  ? MainAxisAlignment.center
                  : MainAxisAlignment.start,
              children: [
                InkWell(
                  onTap: onBackBtnTap ??
                      () {
                        if (Navigator.canPop(context)) {
                          Navigator.pop(context);
                        }
                      },
                  child: Icon(
                    Iconsax.arrow_left_2,
                    color: color ?? AppColors.black900,
                  ),
                ),
                if (showHeaderTitle)
                  Expanded(
                    child: Container(
                      padding: EdgeInsets.only(
                          right: Sizer.width(showBackBtn ? 60 : 0)),
                      child: Center(
                        child: isHeaderText
                            ? Text(
                                headerText ?? '',
                                style: AppTypography.text16.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: color ?? AppColors.black900,
                                ),
                              )
                            : headerWidget,
                      ),
                    ),
                  ),
                if (rightWidget != null) rightWidget!,
              ],
            ),
          )
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(Sizer.height(50));
}

class NewCustomAppbar extends StatelessWidget implements PreferredSizeWidget {
  const NewCustomAppbar({
    super.key,
    this.showBackBtn = false,
    this.showHeaderTitle = false,
    this.isHeaderText = true,
    this.color,
    this.headerText,
    this.headerWidget,
    this.rightWidget,
    this.onBackBtnTap,
  });

  final bool showBackBtn;
  final bool showHeaderTitle;
  final bool isHeaderText;
  final Color? color;
  final String? headerText;
  final Widget? headerWidget;
  final Widget? rightWidget;
  final VoidCallback? onBackBtnTap;

  @override
  Widget build(BuildContext context) {
    return PreferredSize(
      preferredSize: Size.fromHeight(Sizer.height(60)),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.end,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: Sizer.width(24),
              vertical: Sizer.height(10),
            ),
            child: Row(
              children: [
                Row(
                  children: [
                    InkWell(
                      onTap: onBackBtnTap ??
                          () {
                            if (Navigator.canPop(context)) {
                              Navigator.pop(context);
                            }
                          },
                      child: Icon(
                        Iconsax.arrow_left_2,
                        color: color ?? AppColors.black900,
                        size: Sizer.height(24),
                      ),
                    ),
                    if (showHeaderTitle)
                      Container(
                        padding: EdgeInsets.only(left: Sizer.width(16)),
                        child: isHeaderText
                            ? Text(
                                headerText ?? '',
                                style: FontTypography.text22.semiBold
                                    .withCustomColor(
                                        color ?? AppColors.mainBlack),
                              )
                            : headerWidget,
                      ),
                  ],
                ),
                if (rightWidget != null) rightWidget!,
              ],
            ),
          )
        ],
      ),
    );
  }

  @override
  Size get preferredSize => Size.fromHeight(Sizer.height(60));
}
