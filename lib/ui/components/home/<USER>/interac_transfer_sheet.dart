import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class InteracTransferSheet extends StatefulWidget {
  const InteracTransferSheet({Key? key}) : super(key: key);

  @override
  State<InteracTransferSheet> createState() => _InteracTransferSheetState();
}

class _InteracTransferSheetState extends State<InteracTransferSheet> {
  final FocusNode _interacFocusNode = FocusNode();
  final TextEditingController _interacC = TextEditingController();

  @override
  void initState() {
    super.initState();
    KeyboardOverlay.addRemoveFocusNode(context, _interacFocusNode);
    _interacFocusNode.requestFocus();
    WidgetsBinding.instance.addPostFrameCallback((_) {});
  }

  @override
  void dispose() {
    _interacFocusNode.dispose();
    _interacC.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: ContainerWithTopBorderRadius(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            const YBox(40),
            Text(
              'Kindly enter your preferred Interac email in the field below',
              style: AppTypography.text16.copyWith(
                color: AppColors.textBlack800,
              ),
            ),
            const YBox(30),
            CustomTextField(
              labelText: "Enter Interac Email",
              focusNode: _interacFocusNode,
              showLabelHeader: true,
              controller: _interacC,
              borderRadius: Sizer.height(4),
              hintText: context.watch<AuthUserVM>().userInteracMail,
              errorText: _interacC.text.trim().isNotEmpty &&
                      !(_interacC.text.contains('@') &&
                          _interacC.text.contains('.'))
                  ? "Invalid email"
                  : null,
              onChanged: (val) => setState(() {}),
            ),
            const YBox(70),
            CustomBtn.solid(
              onTap: () => _updateInteracEmail(),
              isLoading: context.watch<AuthUserVM>().isBusy,
              online: _interacC.text.trim().isNotEmpty &&
                  _interacC.text.contains('@') &&
                  _interacC.text.contains('.'),
              text: "Continue",
            ),
            const YBox(54),
          ],
        ),
      ),
    );
  }

  _updateInteracEmail() {
    var authVM = context.read<AuthUserVM>();
    authVM.updateInteracMail(_interacC.text.trim()).then((value) {
      if (value.success) {
        Navigator.pop(NavigatorKeys.appNavigatorKey.currentContext ?? context);
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
          snackBarType: SnackBarType.success,
        );
      } else {
        FlushBarToast.fLSnackBar(
          message: value.message.toString(),
        );
      }
    });
  }
}
