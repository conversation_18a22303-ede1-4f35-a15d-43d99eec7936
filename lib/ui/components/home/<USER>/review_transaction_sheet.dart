import 'package:dotted_border/dotted_border.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class ReviewTransactionSheet extends StatelessWidget {
  const ReviewTransactionSheet({
    Key? key,
    required this.onConvert,
  }) : super(key: key);

  final VoidCallback onConvert;

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      height: Sizer.screenHeight * 0.78,
      child: Consumer<ConvertMoneyVM>(builder: (context, vm, _) {
        printty(vm.toC.text, level: 'toC from review');
        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            const AuthTextSubTitle(
              title: "Review Transaction",
              subtitle: "Make sure you put in the right details",
            ),
            const YBox(26),
            ContainerWithBluewishBg(
              child: Column(
                children: [
                  Text(
                    'Amount',
                    style: AppTypography.text12.copyWith(
                      color: AppColors.blue800,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  Text(
                    '${AppUtils.formatAmountDoubleString(vm.fromC.text.trim().toString().replaceAll(',', ''))} ${vm.fromConvertWallet!.currency?.code}',
                    style: AppTypography.text24.copyWith(
                      color: AppColors.blue800,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            const YBox(15),
            DottedBorder(
              dashPattern: const [8, 5],
              strokeWidth: 2,
              borderType: BorderType.RRect,
              radius: const Radius.circular(4),
              color: AppColors.dottedColor,
              padding: EdgeInsets.symmetric(
                horizontal: Sizer.width(12),
                vertical: Sizer.height(12),
              ),
              child: SizedBox(
                width: Sizer.screenWidth,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _listText(
                      leftText: 'You are converting',
                      rightText:
                          '${AppUtils.formatAmountDoubleString(vm.fromC.text.trim().toString().replaceAll(',', ''))} ${vm.fromConvertWallet!.currency?.code}',
                      isNetworkSvg: true,
                      imgPath: vm.fromConvertWallet?.currency?.flag,
                      showImage: true,
                    ),
                    const YBox(16),
                    _listText(
                      leftText: 'Exchange Rate',
                      rightText: vm.rateFormat,
                      // rightText: '1 CAD = 1,530.00  NGN',
                      fontWeight: FontWeight.w700,
                    ),
                    const YBox(16),
                    _listText(
                      leftText: 'Fees',
                      rightText: '0 CAD',
                    ),
                    const YBox(16),
                    _listText(
                      leftText: 'You Get',
                      rightText:
                          '${vm.toC.text} ${vm.toConvertWallet?.currency?.code}',
                      isNetworkSvg: true,
                      imgPath: vm.toConvertWallet?.currency?.flag,
                      showImage: true,
                    ),
                    const YBox(16),
                    _listText(
                      leftText: 'Estimated Delivery',
                      rightText: 'Instant',
                    ),
                  ],
                ),
              ),
            ),
            const YBox(45),
            Text(
              'You are initiating a currency conversion from your ${vm.fromConvertWallet!.currency?.code} wallet to your ${vm.toConvertWallet!.currency?.code} wallet. Please confirm your details above',
              style: AppTypography.text12,
            ),
            const YBox(60),
            CustomBtn.solid(
              onTap: () {
                onConvert();
                // Navigator.pop(context);
                // BsWrapper.bottomSheet(
                //   context: context,
                //   widget: const ConfirmPinSheet(),
                // );
              },
              online: true,
              text: "Continue",
            ),
          ],
        );
      }),
    );
  }

  Row _listText({
    bool showImage = false,
    required String leftText,
    required String rightText,
    FontWeight? fontWeight,
    String? imgPath,
    bool isNetworkSvg = false,
  }) {
    return Row(
      children: [
        Text(
          leftText,
          style: AppTypography.text13.copyWith(
            color: AppColors.textGray,
          ),
        ),
        const Spacer(),
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (showImage)
              isNetworkSvg == true
                  ? SvgPicture.network(
                      imgPath!,
                      height: Sizer.height(14),
                      width: Sizer.width(20),
                    )
                  : imageHelper(
                      imgPath!,
                      height: Sizer.height(14),
                      width: Sizer.width(20),
                    ),
            if (showImage) const XBox(6),
            Text(
              rightText,
              style: AppTypography.text13.copyWith(
                color: AppColors.blue600,
                fontWeight: fontWeight,
              ),
            ),
          ],
        ),
      ],
    );
  }
}
