import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class NewCustomerOfferModal extends StatelessWidget {
  const NewCustomerOfferModal({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      child: Consumer<TrustedDeviceVM>(builder: (context, vm, _) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const YBox(20),
            <PERSON><PERSON>(
              alignment: Alignment.centerRight,
              child: InkWell(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Icon(
                  Icons.close,
                  size: Sizer.radius(24),
                ),
              ),
            ),
            imageHelper(
              AppImages.newOffer,
              height: Sizer.height(172),
              width: Sizer.width(154),
            ),
            const YBox(24),
            Text(
              'New Customer Offer',
              style: AppTypography.text20.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(4),
            RichText(
              textAlign: TextAlign.center,
              text: TextSpan(
                style: AppTypography.text16.copyWith(
                  color: AppColors.textBlack800,
                ),
                children: [
                  const TextSpan(text: 'Send '),
                  TextSpan(
                    text:
                        '${context.read<ConfigVM>().newCustomersRateMinimumAmount ?? "0"} CAD',
                    style: const TextStyle(
                      color: AppColors.primaryBlue,
                      fontWeight: FontWeight.bold,
                      decoration: TextDecoration.underline,
                    ),
                  ),
                  const TextSpan(
                      text: ' or more on your first transfer and enjoy a '),
                  const TextSpan(
                    text: 'special',
                    style: TextStyle(
                      color: AppColors.green48,
                    ),
                  ),
                  const TextSpan(text: ' rate just for you!'),
                ],
              ),
            ),
            const YBox(50),
            CustomBtn.solid(
              isLoading: vm.isBusy,
              // online: true,
              onTap: () {
                Navigator.pop(context);
              },
              text: "Ok, got it!",
            ),
            const YBox(40),
          ],
        );
      }),
    );
  }
}
