import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/auth/kyc/complete_kyc_screen.dart';
import 'package:korrency/ui/screens/auth/kyc/setup_pin_screen.dart';

class OtherActionsSheet extends StatelessWidget {
  const OtherActionsSheet({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Consumer<AuthUserVM>(builder: (context, authVM, _) {
      return ContainerWithTopBorderRadius(
        height: Sizer.screenHeight * 0.60,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const YBox(20),
            Text(
              "Other Actions",
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(20),
            CustomListViews.viewWithSubTitle(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
              icon: Iconsax.information,
              title: "Account Details",
              subTitle: "See all your available accounts by currency",
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, RoutePath.accountDetailsScreen);
              },
            ),
            const YBox(20),
            Consumer<WalletVM>(builder: (context, vm, _) {
              return CustomListViews.viewWithSubTitle(
                padding:
                    const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
                icon: Iconsax.arrow_swap_horizontal,
                title: "Convert Currency",
                subTitle: "Change your money to another currency",
                onTap: () {
                  if (authVM.kycVerifiedCheck) {
                    BsWrapper.bottomSheet(
                      canDismiss: false,
                      context: context,
                      widget: const CompleteKycScreen(),
                    );

                    return;
                  }
                  if (authVM.statusProcessing) {
                    FlushBarToast.fLSnackBar(
                      message: 'Your ID verification is in progress',
                      snackBarType: SnackBarType.success,
                    );
                    return;
                  }
                  if (authVM.secQuestCheck) {
                    Navigator.pushNamed(
                      context,
                      RoutePath.securityQuestionScreen,
                    );
                    return;
                  }
                  if (authVM.transactionPinCheck) {
                    BsWrapper.bottomSheet(
                      context: context,
                      canDismiss: false,
                      widget: const SetupPinScreen(),
                    );

                    return;
                  }

                  if (vm.walletList.length < 2) {
                    BsWrapper.bottomSheet(
                      context: context,
                      widget: ConfirmationSheet(
                        // title: 'Warning',
                        message:
                            "You need at least two wallets to convert currency",
                        firstBtnText: "Create Wallet",
                        firstBtnTap: () {
                          Navigator.pop(context);
                          BsWrapper.bottomSheet(
                            context: context,
                            widget: const AllWalletsSheet(),
                          );
                        },
                      ),
                    );

                    return;
                  }
                  Navigator.pop(context);
                  Navigator.pushNamed(context, RoutePath.convertCurrencyScreen);
                },
              );
            }),
            const YBox(20),
            CustomListViews.viewWithSubTitle(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
              icon: Iconsax.unlimited,
              title: "Account Limits",
              subTitle: "See how much you can transfer from your account",
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, RoutePath.accountLimitScreen);
              },
            ),
            const YBox(20),
            CustomListViews.viewWithSubTitle(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
              icon: Iconsax.document_text,
              title: "Statement",
              subTitle: "Download your account statements",
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, RoutePath.accountStatementScreen);
              },
            ),
            const YBox(20),
            CustomListViews.viewWithSubTitle(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 12),
              icon: Iconsax.document_text,
              title: "Exchange Rate",
              subTitle: "Check the current rates",
              onTap: () {
                if (!authVM.userIsVerified) {
                  BsWrapper.bottomSheet(
                    canDismiss: false,
                    context: context,
                    widget: const CompleteKycScreen(),
                  );

                  return;
                }
                Navigator.pop(context);
                Navigator.pushNamed(context, RoutePath.exchangeRateScreen);
              },
            ),
          ],
        ),
      );
    });
  }
}
