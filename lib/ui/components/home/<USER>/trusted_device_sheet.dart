import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class TrustedDeviceSheet extends StatelessWidget {
  const TrustedDeviceSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      height: Sizer.screenHeight * 0.60,
      child: Consumer<TrustedDeviceVM>(builder: (context, vm, _) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Y<PERSON><PERSON>(30),
            image<PERSON><PERSON><PERSON>(
              AppImages.trustedDevice,
              height: Sizer.height(174),
              width: Sizer.width(174),
            ),
            const YBox(10),
            Text(
              'Set Trusted Devices',
              style: AppTypography.text20.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
            const YBox(4),
            Text(
              'We noticed a new log in from a device. Set as trusted device?',
              textAlign: TextAlign.center,
              style: AppTypography.text16.copyWith(
                color: AppColors.textBlack800,
              ),
            ),
            const Spacer(),
            CustomBtn.solid(
              isLoading: vm.isBusy,
              // online: true,
              onTap: () {
                vm.requestOtp().then((value) {
                  if (value.success) {
                    // Navigator.pop(context);
                    Navigator.pushNamed(
                        context, RoutePath.verifyTrustedDeviceScreen);
                  } else {
                    FlushBarToast.fLSnackBar(
                      message: value.message.toString(),
                    );
                  }
                });
              },
              text: "Continue",
            ),
            const YBox(26),
            InkWell(
              onTap: () {
                Navigator.pop(context);
                context.read<LoginVM>().logout().then((value) {
                  if (value.success) {
                    context.read<DashboardVM>().resetData();
                  } else {
                    FlushBarToast.fLSnackBar(
                      message: value.message.toString(),
                    );
                  }
                });
              },
              child: Container(
                alignment: Alignment.center,
                child: Text(
                  'Log out',
                  style: AppTypography.text16.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.iconRed,
                  ),
                ),
              ),
            ),
            const YBox(40),
          ],
        );
      }),
    );
  }
}
