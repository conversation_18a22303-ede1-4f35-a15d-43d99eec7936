import 'package:flutter/services.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';
import 'package:korrency/ui/screens/dashboard/menu/helpsupport/create_freshdesk_ticket_webview.dart';
import 'package:share_plus/share_plus.dart';

class InviteOthersAndEarnSheet extends StatelessWidget {
  const InviteOthersAndEarnSheet({super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: Sizer.screenHeight * 0.95,
      child: Consumer<AuthUserVM>(builder: (context, vm, _) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const YBox(20),
            Container(
              margin: EdgeInsets.symmetric(horizontal: Sizer.width(14)),
              height: Sizer.height(12),
              width: Sizer.screenWidth,
              decoration: BoxDecoration(
                color: AppColors.sheetLightBlue.withOpacity(0.25),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
            ),
            Expanded(
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: Sizer.width(24)),
                width: Sizer.screenWidth,
                decoration: const BoxDecoration(
                  color: AppColors.bgWhite,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Column(
                  children: [
                    Column(
                      children: [
                        Container(
                          alignment: Alignment.centerRight,
                          padding: EdgeInsets.only(top: Sizer.height(28)),
                          child: InkWell(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: const Icon(
                              Icons.close,
                              size: 24,
                              color: AppColors.gray500,
                            ),
                          ),
                        ),
                        const YBox(37),
                        imageHelper(
                          AppImages.coins,
                          height: Sizer.height(208),
                          width: Sizer.width(208),
                        ),
                        const YBox(30),
                        Text(
                          'Invite Others and Earn',
                          style: AppTypography.text20.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        const YBox(8),
                        Consumer<ConfigVM>(builder: (context, vm, _) {
                          return Text(
                            'Invite friends to join Korrency and you both get to earn \$${vm.referralBonusAmount} each after they make their first transaction of \$${vm.amtForReferral} or more.',
                            textAlign: TextAlign.center,
                            style: AppTypography.text16.copyWith(
                              color: AppColors.textBlack800,
                            ),
                          );
                        })
                      ],
                    ),
                    const YBox(60),
                    CustomTextField(
                      borderRadius: Sizer.height(4),
                      hintText: vm.user?.referralCode,
                      isReadOnly: true,
                      suffixIcon: CopyWithIcon(
                        margin: EdgeInsets.only(
                          top: Sizer.height(11),
                          bottom: Sizer.height(11),
                          right: Sizer.width(16),
                        ),
                        onPressed: () {
                          // var configVM = context.read<ConfigVM>();
                          Clipboard.setData(
                            ClipboardData(
                              // text: "${vm.user?.referralCode}",
                              text: vm.user?.referralCode ?? '',
                            ),
                          );
                          FlushBarToast.fLSnackBar(
                            message: "Referral code copied",
                            snackBarType: SnackBarType.success,
                          );
                        },
                      ),
                      // controller: vm.emailController,
                      // errorText:
                      //     vm.emailController.text.isNotEmpty && !vm.isValidEmail
                      //         ? "Invalid Email"
                      //         : null,
                      onChanged: (val) {},
                    ),
                    const YBox(37),
                    CustomBtn.solid(
                      onTap: () async {
                        var configVM = context.read<ConfigVM>();
                        await Share.share(
                          referalShare(
                            refCode: vm.user?.referralCode,
                            refLink:
                                'https://korrency.onelink.me/9BIc?referrer=${vm.user?.referralCode}',
                            refBonusAmt: configVM.referralBonusAmount,
                            refamtForReferral: configVM.amtForReferral,
                          ),
                          subject: "Korrency",
                        );

                        MixpanelService()
                            .track('Referral Code Shared', properties: {
                          "referral_code": vm.user?.referralCode,
                          "referral_link":
                              'https://korrency.onelink.me/9BIc?referrer=${vm.user?.referralCode}',
                          "referral_amount": configVM.referralBonusAmount,
                          "time": DateTime.now().toIso8601String()
                        });
                      },
                      online: true,
                      text: "Invite Friends",
                    ),
                    const YBox(10),
                    InkWell(
                      onTap: () {
                        Navigator.pushNamed(
                          context,
                          RoutePath.createFreshDeskTicketWebview,
                          arguments: WebViewArg(
                            webURL: AppUtils.referralTermsAndCondition,
                          ),
                        );
                      },
                      child: Text(
                        'Terms and Conditions apply',
                        textAlign: TextAlign.center,
                        style: AppTypography.text12.copyWith(
                          color: AppColors.primaryBlue,
                        ),
                      ),
                    ),
                    const YBox(30),
                    InkWell(
                      onTap: () {
                        Navigator.pushNamed(context, RoutePath.referralScreen);
                      },
                      child: Text(
                        'View Your Referral History',
                        textAlign: TextAlign.center,
                        style: AppTypography.text16.copyWith(
                          color: AppColors.textBlack800,
                          decoration: TextDecoration.underline,
                          decorationColor: AppColors.textBlack800,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        );
      }),
    );
  }
}
