import 'package:korrency/core/core.dart';

class ForgotPasswordBtn extends StatelessWidget {
  const ForgotPasswordBtn({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        Navigator.pushNamed(context, RoutePath.forgotPasswordScreen);
        context.read<LoginVM>().clearData();
      },
      child: Container(
        alignment: Alignment.centerRight,
        child: Text(
          "Forgot Password?",
          style: AppTypography.text14.copyWith(
            color: AppColors.lightBlue,
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
    );
  }
}
