import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:iconsax/iconsax.dart';
import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class StateProvinceSheet extends StatefulWidget {
  const StateProvinceSheet({
    Key? key,
  }) : super(key: key);

  @override
  State<StateProvinceSheet> createState() => _StateProvinceSheetState();
}

class _StateProvinceSheetState extends State<StateProvinceSheet> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getStateProvince();
    });
  }

  _getStateProvince() {
    context.read<AddressSuggestionVM>().getStateProvince();
  }

  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      padding: EdgeInsets.only(top: 12.h, bottom: 0.h, left: 24.w, right: 24.w),
      child: SizedBox(
        height: Sizer.screenHeight * 0.8,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: EdgeInsets.only(
                top: Sizer.height(20),
              ),
              alignment: Alignment.center,
              child: Text(
                "Select State/Province",
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            const YBox(24),
            _search(),
            Expanded(child: mainContent())
          ],
        ),
      ),
    );
  }

  Widget mainContent() {
    return Consumer<AddressSuggestionVM>(builder: (context, vm, _) {
      if (vm.stateProvince.isEmpty) {
        return const Center(child: Text("No State/Province found"));
        // return _empty();
      }
      return ListView.separated(
        shrinkWrap: true,
        padding:
            EdgeInsets.only(top: Sizer.height(24), bottom: Sizer.height(50)),
        itemCount: vm.stateProvince.length,
        separatorBuilder: ((context, index) {
          return YBox(12.h);
        }),
        itemBuilder: ((context, index) {
          StateProvince stateProvince = vm.stateProvince[index];
          return StateProvinceItemWidget(
            onTap: () {
              if (stateProvince.restricted) {
                return FlushBarToast.fLSnackBar(
                  message: "This State/Province is restricted",
                );
              }
              context.read<KycVM>().onStateProvinceSelected(stateProvince);
              Navigator.pop(context);
            },
            label: stateProvince.name ?? "",
            restricted: stateProvince.restricted,
            //imageUrl: PngImageAsset.fmBank,
          );
        }),
      );
    });
  }

  _search() {
    return Container(
      width: double.infinity,
      height: 46.h,
      alignment: Alignment.center,
      child: TextField(
        cursorColor: AppColors.gray700,
        style: TextStyle(
            color: AppColors.gray700,
            fontSize: Sizer.text(15),
            fontWeight: FontWeight.w400),
        controller: TextEditingController(),
        onChanged: (String value) {
          context.read<AddressSuggestionVM>().searchStateProvince(value);
        },
        decoration: InputDecoration(
            contentPadding: EdgeInsets.only(top: 4.h),
            errorStyle: TextStyle(
                color: AppColors.brandOrange, fontSize: 0.01.sp, height: 0.2),
            hintText: "Search State/Province",
            hintStyle: const TextStyle(color: AppColors.gray500),
            prefixIcon: const Icon(Iconsax.search_normal_1),
            // suffixIconColor: AppColors.brandOrange,

            fillColor: AppColors.gray100,
            filled: true,
            // isCollapsed: true,
            // isDense: true,
            // labelStyle: TextStyle(color: bluishGrey, fontSize: 14.sp),
            enabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(width: 1, color: AppColors.gray500),
              borderRadius: BorderRadius.circular(Sizer.radius(12)),
            ),
            disabledBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                width: 1,
              ),
              borderRadius: BorderRadius.circular(Sizer.radius(12)),
            ),
            border: OutlineInputBorder(
              borderSide:
                  const BorderSide(width: 1, color: AppColors.primaryBlue),
              borderRadius: BorderRadius.circular(Sizer.radius(12)),
            ),
            errorBorder: OutlineInputBorder(
              //borderSide: BorderSide.none,
              borderSide:
                  BorderSide(width: 1, color: AppColors.red.withOpacity(0.8)),
              borderRadius: BorderRadius.circular(Sizer.radius(12)),
            ),
            focusedErrorBorder: OutlineInputBorder(
              borderSide:
                  BorderSide(width: 1, color: AppColors.red.withOpacity(0.8)),
              borderRadius: BorderRadius.circular(Sizer.radius(12)),
            ),
            focusedBorder: OutlineInputBorder(
              borderSide: const BorderSide(
                width: 1,
                color: AppColors.primaryBlue,
              ),
              borderRadius: BorderRadius.circular(Sizer.radius(12)),
            )),
      ),
    );
  }
}

class StateProvinceItemWidget extends StatelessWidget {
  const StateProvinceItemWidget(
      {super.key,
      required this.label,
      required this.onTap,
      required this.restricted});
  final String label;
  final bool restricted;
  final Function() onTap;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: SizedBox(
        width: Sizer.screenWidth,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            Flexible(
              flex: 19,
              child: Row(
                children: [
                  Container(
                      width: Sizer.width(40),
                      height: Sizer.width(40),
                      alignment: Alignment.center,
                      decoration: const BoxDecoration(
                          color: AppColors.gray600, shape: BoxShape.circle),
                      child: Text(
                        label.substring(0, 1),
                        style: TextStyle(
                          fontSize: Sizer.text(12),
                        ),
                      )),
                  const XBox(22),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          label,
                          style: TextStyle(
                              decoration: restricted
                                  ? TextDecoration.lineThrough
                                  : null,
                              color: AppColors.baseBlack.withOpacity(0.8),
                              fontWeight: FontWeight.w400,
                              overflow: TextOverflow.ellipsis,
                              fontSize: Sizer.text(15)),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            //const Spacer(),
            Flexible(
              flex: 1,
              child: Icon(
                Iconsax.arrow_right_3,
                size: Sizer.radius(16),
                color: const Color(0xff292D32),
              ),
            )
          ],
        ),
      ),
    );
  }
}
