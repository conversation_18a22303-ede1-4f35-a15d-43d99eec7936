import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SelectCurrencySheet extends StatefulWidget {
  const SelectCurrencySheet({
    super.key,
    this.fromConvert = false,
    this.isSendMoney = false,
  });

  final bool fromConvert;
  final bool isSendMoney;

  @override
  State<SelectCurrencySheet> createState() => _SelectCurrencySheetState();
}

class _SelectCurrencySheetState extends State<SelectCurrencySheet> {
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
      height: Sizer.screenHeight * 0.6,
      child: Column(
        children: [
          const YBox(20),
          InkWell(
            onTap: () {
              Navigator.pop(context);
            },
            child: Container(
              alignment: Alignment.centerRight,
              child: Icon(
                Icons.close,
                size: Sizer.radius(23),
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(
              top: Sizer.height(20),
            ),
            alignment: Alignment.center,
            child: Text(
              "Select Currency",
              style: AppTypography.text16.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          // const YBox(25),
          Consumer<CurrencyVM>(builder: (context, vm, _) {
            // fromConvertWallet recipientCurrenciess
            final sendMoneyVM = context.read<SendMoneyVM>();
            final sendMoneyCurrencies =
                sendMoneyVM.fromConvertWallet?.currency?.recipientCurrencies;
            return Expanded(
              child: ListView.separated(
                padding: EdgeInsets.only(
                  top: Sizer.height(20),
                  bottom: Sizer.height(80),
                ),
                shrinkWrap: true,
                itemCount: widget.isSendMoney
                    ? sendMoneyCurrencies?.length ?? 0
                    : vm.currencies.length,
                itemBuilder: (context, i) {
                  var currency = vm.currencies[i];

                  return InkWell(
                    onTap: () {
                      sendMoneyCurrencySelection(
                          i,
                          widget.isSendMoney
                              ? sendMoneyCurrencies
                              : vm.currencies);
                    },
                    child: ContainerWithBluewishBg(
                      padding: EdgeInsets.symmetric(
                        vertical: Sizer.height(20),
                        horizontal: Sizer.width(16),
                      ),
                      child: WalletListTile(
                        title: widget.isSendMoney
                            ? (sendMoneyCurrencies?[i].code ?? '')
                            : currency.code ?? "",
                        currencyIcon: widget.isSendMoney
                            ? (sendMoneyCurrencies?[i].flag ?? '')
                            : currency.flag ?? "",
                        useNetworkSvg: true,
                        showTrailingWidget: false,
                        icon: Icons.check_circle,
                      ),
                    ),
                  );
                },
                separatorBuilder: (context, index) => const YBox(16),
              ),
            );
          }),
        ],
      ),
    );
  }

  void sendMoneyCurrencySelection(int index, List<Currency>? currencies) {
    var sendMoneyVM = context.read<SendMoneyVM>();
    var walletVM = context.read<WalletVM>();

    /// checking if fromConvertWallet is the same as recipientCurrency
    /// Only allow if they are different and if both are CAD
    if (widget.fromConvert) {
      sendMoneyVM.setFromConvertWallet(
        code: currencies?[index].code ?? "",
        walletList: walletVM.walletList,
      );
    } else {
      sendMoneyVM.setRecipientCurrency(currencies![index]);
    }
    // sendMoneyVM
    //   ..getFees()
    //   ..getConversionRate();
    Navigator.pop(context);
  }
}
