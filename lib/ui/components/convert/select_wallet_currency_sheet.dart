import 'package:korrency/core/core.dart';
import 'package:korrency/ui/components/components.dart';

class SelectWalletCurrencySheet extends StatefulWidget {
  const SelectWalletCurrencySheet({
    super.key,
    this.fromConvert = false,
    this.isSendMoney = false,
  });

  final bool fromConvert; // TOP Selection
  final bool isSendMoney;

  @override
  State<SelectWalletCurrencySheet> createState() =>
      _SelectWalletCurrencySheetState();
}

class _SelectWalletCurrencySheetState extends State<SelectWalletCurrencySheet> {
  @override
  Widget build(BuildContext context) {
    return ContainerWithTopBorderRadius(
        height: Sizer.screenHeight * 0.6,
        child: ListView(
          children: [
            const YBox(20),
            InkWell(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                alignment: Alignment.centerRight,
                child: Icon(
                  Icons.close,
                  size: Sizer.radius(23),
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.only(
                top: Sizer.height(20),
              ),
              alignment: Alignment.center,
              child: Text(
                "Select Wallet",
                style: AppTypography.text16.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
            // const YBox(25),
            Consumer<WalletVM>(builder: (context, vm, _) {
              return ListView.separated(
                padding: EdgeInsets.symmetric(
                  vertical: Sizer.height(20),
                ),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: vm.walletList.length,
                itemBuilder: (context, i) {
                  final wallet = vm.walletList[i];
                  return InkWell(
                    onTap: () {
                      if (widget.isSendMoney) {
                        sendMoneyCurrencySelection(i);
                      } else {
                        handleCurrencySelection(i, vm);
                      }
                    },
                    child: ContainerWithBluewishBg(
                      padding: EdgeInsets.symmetric(
                        vertical: Sizer.height(20),
                        horizontal: Sizer.width(16),
                      ),
                      child: WalletListTile(
                        title: wallet.currency?.code ?? "",
                        currencyIcon: wallet.currency?.flag ?? "",
                        useNetworkSvg: true,
                        showTrailingWidget: false,
                        icon: Icons.check_circle,
                      ),
                    ),
                  );
                },
                separatorBuilder: (context, index) => const YBox(16),
              );
            }),
            const YBox(100),
          ],
        ));
  }

  void handleCurrencySelection(int index, WalletVM walletVM) {
    var convertMoneyVM = context.read<ConvertMoneyVM>();
    var walletVM = context.read<WalletVM>();

    if (widget.fromConvert) {
      convertMoneyVM.setFromConvertWallet(
          walletVM.walletList[index].currency?.code ?? "");
      // convertMoneyVM.setToConvertWallet(
      //     walletVM.walletList[index .currency?== 0 ? 1 : 0].code ?? "");
    } else {
      convertMoneyVM
          .setToConvertWallet(walletVM.walletList[index].currency?.code ?? "");
      // convertMoneyVM.setFromConvertWallet(
      //     walletVM.walletList[index .currency?== 0 ? 1 : 0].code ?? "");
    }
    // walletVM.getConversionRate();
    convertMoneyVM
        // ..resetData()
        .getConversionRate(isFrom: widget.fromConvert);
    Navigator.pop(context);
  }

  void sendMoneyCurrencySelection(int index) {
    var sendMoneyVM = context.read<SendMoneyVM>();
    var walletVM = context.read<WalletVM>();

    if (widget.fromConvert) {
      sendMoneyVM.setFromConvertWallet(
        code: walletVM.walletList[index].currency?.code ?? "",
        walletList: walletVM.walletList,
      );

      // Set Recipient Currency (Bottom Selection) to the selected wallet currency
      // sendMoneyVM.setRecipientCurrency(walletVM.walletList[index].currency); //TODO: check it out
    } else {
      if (walletVM.walletList[index].currency != null) {
        sendMoneyVM.setRecipientCurrency(walletVM.walletList[index].currency!);
      }
    }
    sendMoneyVM
      ..getConversionRate()
      ..getFees();
    Navigator.pop(context);
  }
}
