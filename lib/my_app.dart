// ignore_for_file: use_build_context_synchronously

import 'package:flutter/services.dart';
import 'package:korrency/session_manager.dart';
import 'package:korrency/ui/screens/splash/splash.dart';
import 'package:local_session_timeout/local_session_timeout.dart';

import 'core/core.dart';

class MyApp extends StatefulWidget {
  const MyApp({super.key});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _setupAppsFlyerListeners();
    });
  }

  void _setupAppsFlyerListeners() {
    // Listen to conversion data
    AppsFlyerService.instance.conversionDataStream?.listen((data) {
      printty("conversionData  $data");
    });

    // Listen to deep link data
    AppsFlyerService.instance.deepLinkStream?.listen((data) {
      printty("deepLink  $data");

      // Handle referrals
      if (data.containsKey('referrer')) {
        final referrer = data['referrer'];
        printty("Referrer xxxxx: $referrer");

        final context = NavigatorKeys.appNavigatorKey.currentContext;
        if (context != null) {
          context.read<OnBoardVM>().setDeepLinkReferralCode(referrer);
        }
      }
    });
  }

  @override
  void dispose() {
    AppsFlyerService.instance.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.dark,
      ),
    );

    final sessionConfig = SessionConfig(
        invalidateSessionForAppLostFocus:
            Duration(minutes: EnvConfig.focusTimeout),
        invalidateSessionForUserInactivity:
            Duration(minutes: EnvConfig.inactivityTimeout));

    sessionConfig.stream.listen((SessionTimeoutState timeoutEvent) {
      sessionStateStream.add(SessionState.stopListening);
      // if (context.watch<AuthUserVM>().user != null) {}
      if (timeoutEvent == SessionTimeoutState.userInactivityTimeout) {
        Navigator.pushNamed(NavigatorKeys.appNavigatorKey.currentContext!,
            RoutePath.welcomeBackScreen,
            arguments: false);
      } else if (timeoutEvent == SessionTimeoutState.appFocusTimeout) {
        Navigator.pushNamed(NavigatorKeys.appNavigatorKey.currentContext!,
            RoutePath.welcomeBackScreen,
            arguments: false);
      }
    });
    return MultiProvider(
      providers: allProviders,
      child: ScreenUtilInit(
        designSize: const Size(390, 844),
        builder: (context, child) {
          return SessionTimeoutManager(
            sessionConfig: sessionConfig,
            sessionStateStream: sessionStateStream.stream,
            child: MaterialApp(
              title: 'Korrency',
              debugShowCheckedModeBanner: false,
              theme: ThemeData(
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
                colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
                scaffoldBackgroundColor: AppColors.bgWhite,
                fontFamily: 'Inter',
                useMaterial3: true,
              ),
              navigatorKey: NavigatorKeys.appNavigatorKey,
              home: SplashScreen(),
              // home: const TransactionReceiptScreen(),
              // home: const DashboardNav(),
              onGenerateRoute: AppRouters.getRoute,
            ),
          );
        },
      ),
    );
  }
}
