import 'dart:convert';

import 'package:korrency/core/core.dart';

AuthUser authUserFromJson(String str) => AuthUser.fromJson(json.decode(str));

String authUserToJson(AuthUser data) => json.encode(data.toJson());

class AuthUser {
  String? id;
  String? firstName;
  String? middleName;
  String? lastName;
  String? fullName;
  String? userName;
  String? email;
  DateTime? emailVerifiedAt;
  String? phone;
  DateTime? phoneVerifiedAt;
  DateTime? loggedInAt;
  dynamic loggedOutAt;
  dynamic passwordChangedAt;
  String? status; //pending, verified, declined, processing
  bool? isSuspended;
  String? reasonSuspended;
  String? avatarUrl;
  String? dateOfBirth;
  String? gender;
  OccupationData? occupation;
  String? referralCode;
  String? address;
  String? city;
  String? state;
  String? country;
  String? postalCode;
  String? interacEmail;
  int? kycStep;
  bool? hasTransactionPin;
  bool? hasSecurityQuestions;
  bool? isTrustedDevice;
  bool? awaitingReview;
  bool? hasTransactions;
  dynamic isPoliticallyExposed;
  String? twoFactorNotificationPreference;
  NotificationSetting? notificationSetting;
  String? activeOffers;
  Currency? frequentDestinationCurrency;
  DateTime? createdAt;
  DateTime? updatedAt;

  AuthUser({
    this.id,
    this.firstName,
    this.middleName,
    this.lastName,
    this.fullName,
    this.userName,
    this.email,
    this.emailVerifiedAt,
    this.phone,
    this.phoneVerifiedAt,
    this.loggedInAt,
    this.loggedOutAt,
    this.passwordChangedAt,
    this.status,
    this.isSuspended,
    this.reasonSuspended,
    this.avatarUrl,
    this.dateOfBirth,
    this.gender,
    this.occupation,
    this.referralCode,
    this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.interacEmail,
    this.kycStep,
    this.hasTransactionPin,
    this.hasSecurityQuestions,
    this.isTrustedDevice,
    this.awaitingReview,
    this.hasTransactions,
    this.isPoliticallyExposed,
    this.twoFactorNotificationPreference,
    this.notificationSetting,
    this.activeOffers,
    this.frequentDestinationCurrency,
    this.createdAt,
    this.updatedAt,
  });

  factory AuthUser.fromJson(Map<String, dynamic> json) => AuthUser(
        id: json["id"],
        firstName: json["first_name"],
        middleName: json["middle_name"],
        lastName: json["last_name"],
        fullName: json["full_name"],
        userName: json["user_name"],
        email: json["email"],
        emailVerifiedAt: json["email_verified_at"] == null
            ? null
            : DateTime.parse(json["email_verified_at"]),
        phone: json["phone"],
        phoneVerifiedAt: json["phone_verified_at"] == null
            ? null
            : DateTime.parse(json["phone_verified_at"]),
        loggedInAt: json["logged_in_at"] == null
            ? null
            : DateTime.parse(json["logged_in_at"]),
        loggedOutAt: json["logged_out_at"],
        passwordChangedAt: json["password_changed_at"],
        status: json["status"],
        isSuspended: json["is_suspended"],
        reasonSuspended: json["reason_suspended"],
        avatarUrl: json["avatar_url"],
        dateOfBirth: json["date_of_birth"],
        gender: json["gender"],
        occupation: json["occupation"] == null
            ? null
            : OccupationData.fromJson(json["occupation"]),
        referralCode: json["referral_code"],
        address: json["address"],
        city: json["city"],
        state: json["state"],
        country: json["country"],
        postalCode: json["postal_code"],
        interacEmail: json["interac_email"],
        kycStep: json["kyc_step"],
        hasTransactionPin: json["has_transaction_pin"],
        hasSecurityQuestions: json["has_security_questions"],
        isTrustedDevice: json["is_trusted_device"],
        awaitingReview: json["awaiting_review"],
        hasTransactions: json["has_transactions"],
        isPoliticallyExposed: json["is_politically_exposed"],
        twoFactorNotificationPreference:
            json["two_factor_notification_preference"],
        notificationSetting: json["notification_setting"] == null
            ? null
            : NotificationSetting.fromJson(json["notification_setting"]),
        activeOffers: json["active_offers"],
        frequentDestinationCurrency:
            json["frequent_destination_currency"] == null
                ? null
                : Currency.fromJson(json["frequent_destination_currency"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "first_name": firstName,
        "middle_name": middleName,
        "last_name": lastName,
        "full_name": fullName,
        "user_name": userName,
        "email": email,
        "email_verified_at": emailVerifiedAt?.toIso8601String(),
        "phone": phone,
        "phone_verified_at": phoneVerifiedAt?.toIso8601String(),
        "logged_in_at": loggedInAt?.toIso8601String(),
        "logged_out_at": loggedOutAt,
        "password_changed_at": passwordChangedAt,
        "status": status,
        "is_suspended": isSuspended,
        "reason_suspended": reasonSuspended,
        "avatar_url": avatarUrl,
        "date_of_birth": dateOfBirth,
        "gender": gender,
        "occupation": occupation,
        "referral_code": referralCode,
        "address": address,
        "city": city,
        "state": state,
        "country": country,
        "postal_code": postalCode,
        "interac_email": interacEmail,
        "kyc_step": kycStep,
        "has_transaction_pin": hasTransactionPin,
        "has_security_questions": hasSecurityQuestions,
        "is_trusted_device": isTrustedDevice,
        "awaiting_review": awaitingReview,
        "has_transactions": hasTransactions,
        "is_politically_exposed": isPoliticallyExposed,
        "two_factor_notification_preference": twoFactorNotificationPreference,
        "notification_setting": notificationSetting?.toJson(),
        "active_offers": activeOffers,
        "frequent_destination_currency": frequentDestinationCurrency,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class NotificationSetting {
  bool? email;
  bool? push;
  bool? rate;

  NotificationSetting({
    this.email,
    this.push,
    this.rate,
  });

  factory NotificationSetting.fromJson(Map<String, dynamic> json) =>
      NotificationSetting(
        email: json["email"],
        push: json["push"],
        rate: json["rate"],
      );

  Map<String, dynamic> toJson() => {
        "email": email,
        "push": push,
        "rate": rate,
      };
}
