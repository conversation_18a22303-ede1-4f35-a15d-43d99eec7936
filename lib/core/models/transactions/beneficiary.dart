import 'dart:convert';

import 'package:korrency/core/enums/enums.dart';
import 'package:korrency/core/models/wallets/currency.dart';

List<Beneficiary> beneficiaryFromJson(String str) => List<Beneficiary>.from(
    json.decode(str).map((x) => Beneficiary.fromJson(x)));

String beneficiaryToJson(List<Beneficiary> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Beneficiary {
  int? id;
  String? institutionName;
  String? institutionCode;
  String? accountName;
  String? firstName;
  String? lastName;
  String? iconUrl;
  String? accountIdentifier;
  Currency? currency;
  DateTime? createdAt;

  Beneficiary({
    this.id,
    this.institutionName,
    this.institutionCode,
    this.accountName,
    this.firstName,
    this.lastName,
    this.iconUrl,
    this.accountIdentifier,
    this.currency,
    this.createdAt,
  });

  factory Beneficiary.fromJson(Map<String, dynamic> json) => Beneficiary(
        id: json["id"],
        institutionName: json["institution_name"],
        iconUrl: json["icon_url"],
        institutionCode: json["institution_code"],
        accountName: json["account_name"],
        firstName: json["first_name"],
        lastName: json["last_name"],
        accountIdentifier: json["account_identifier"],
        currency: json["currency"] == null
            ? null
            : Currency.fromJson(json["currency"]),
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "institution_name": institutionName,
        "icon_url": iconUrl,
        "institution_code": institutionCode,
        "account_name": accountName,
        "first_name": firstName,
        "last_name": lastName,
        "account_identifier": accountIdentifier,
        "currency": currency?.toJson(),
        "created_at": createdAt?.toIso8601String(),
      };

  @override
  String toString() {
    return 'Beneficiary(id: $id, institutionName: $institutionName,  "icon_url": $iconUrl, institutionCode: $institutionCode, accountName: $accountName, firstName: $firstName, lastName: $lastName, accountIdentifier: $accountIdentifier, currency: $currency, createdAt: $createdAt)';
  }
}

class PostBeneficiaryArgs {
  final int currencyId;
  final String? institutionName;
  final String? institutionCode;
  final String? accountName;
  final String? firstName;
  final String? lastName;
  final String? accountIdentifier;
  final String? transferMethod;

  PostBeneficiaryArgs({
    required this.currencyId,
    this.institutionName,
    this.institutionCode,
    this.accountName,
    this.firstName,
    this.lastName,
    this.accountIdentifier,
    this.transferMethod,
  });

  @override
  String toString() {
    return 'PostBeneficiaryArgs(currencyId: $currencyId, institutionName: $institutionName, institutionCode: $institutionCode, accountName: $accountName, accountIdentifier: $accountIdentifier)';
  }
}

class BeneficiaryArg {
  final int currencyId;
  final TransferMethodType transferMethodType;

  BeneficiaryArg({
    required this.currencyId,
    this.transferMethodType = TransferMethodType.bankTransfer,
  });

  @override
  String toString() {
    return 'BeneficiaryArg(currencyId: $currencyId, transferMethodType: $transferMethodType)';
  }
}
