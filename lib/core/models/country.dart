import 'package:korrency/core/core.dart';

class Country {
  final String name;
  final String code;
  final String dialCode;
  final String flag;

  Country(
      {required this.name,
      required this.code,
      required this.dialCode,
      required this.flag});

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is Country &&
        other.name == name &&
        other.code == code &&
        other.dialCode == dialCode &&
        other.flag == flag;
  }

  @override
  int get hashCode {
    return name.hashCode ^ code.hashCode ^ dialCode.hashCode ^ flag.hashCode;
  }
}

List<Country> countries = [
  Country(
    name: 'Canada',
    code: 'CAD',
    dialCode: '+1',
    flag: AppImages.cad,
  ),
  // Country(
  //   name: 'Nigeria',
  //   code: 'NGN',
  //   dialCode: '+234',
  //   flag: AppImages.ngn,
  // ),
];
