import 'dart:convert';

List<SendMoneyReason> sendMoneyReasonFromJson(String str) =>
    List<SendMoneyReason>.from(
        json.decode(str).map((x) => SendMoneyReason.fromJson(x)));

String sendMoneyReasonToJson(List<SendMoneyReason> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class SendMoneyReason {
  final int? id;
  final String? name;
  final DateTime? createdAt;

  SendMoneyReason({
    this.id,
    this.name,
    this.createdAt,
  });

  factory SendMoneyReason.fromJson(Map<String, dynamic> json) =>
      SendMoneyReason(
        id: json["id"],
        name: json["name"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "created_at": createdAt?.toIso8601String(),
      };

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is SendMoneyReason &&
        other.id == id &&
        other.name == name &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode => id.hashCode ^ name.hashCode ^ createdAt.hashCode;
}
