import 'dart:convert';

List<Currency> currencyFromJson(String str) =>
    List<Currency>.from(json.decode(str).map((x) => Currency.fromJson(x)));

class Currency {
  Currency({
    required this.id,
    required this.name,
    required this.code,
    required this.country,
    required this.category,
    required this.symbol,
    required this.flag,
    this.isCreatable,
    this.isFundable,
    this.hasLowBalance,
    this.paymentMethods,
    this.minimumTransferAmount,
    this.recipientCurrencies,
    this.deletedAt,
    this.createdAt,
    this.updatedAt,
  });

  final int? id;
  final String? name;
  final String? code;
  final String? country;
  final String? category;
  final String? symbol;
  // final dynamic flag;
  final bool? isCreatable;
  final bool? isFundable;
  final bool? hasLowBalance;
  final List<PaymentMethod>? paymentMethods;
  final String? minimumTransferAmount;
  final String? flag;
  final List<Currency>? recipientCurrencies;
  final dynamic deletedAt;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  factory Currency.fromJson(Map<String, dynamic> json) {
    return Currency(
      id: json["id"],
      name: json["name"],
      code: json["code"],
      country: json["country"],
      category: json["category"],
      symbol: json["symbol"],
      flag: json["flag"],
      isCreatable: json["is_creatable"],
      isFundable: json["is_fundable"],
      hasLowBalance: json["has_low_balance"],
      paymentMethods: json["payment_methods"] == null
          ? []
          : List<PaymentMethod>.from(
              json["payment_methods"]!.map((x) => PaymentMethod.fromJson(x))),
      minimumTransferAmount: json["minimum_transfer_amount"],
      recipientCurrencies: json["recipient_currencies"] == null
          ? []
          : List<Currency>.from(
              json["recipient_currencies"]!.map((x) => Currency.fromJson(x))),
      deletedAt: json["deleted_at"],
      createdAt: json["created_at"] == null
          ? null // DateTime.parse(json["created_at"])
          : DateTime.parse(json["created_at"]),
    );
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "country": country,
        "category": category,
        "symbol": symbol,
        "flag": flag,
        "is_creatable": isCreatable,
        "is_fundable": isFundable,
        "has_low_balance": hasLowBalance,
        "payment_methods": paymentMethods == null
            ? []
            : List<dynamic>.from(paymentMethods!.map((x) => x.toJson())),
        "minimum_transfer_amount": minimumTransferAmount,
        "recipient_currencies": recipientCurrencies == null
            ? []
            : List<dynamic>.from(recipientCurrencies!.map((x) => x.toJson())),
        "deleted_at": deletedAt,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };

  @override
  String toString() {
    return 'Currency(id: $id, name: $name, code: $code, country: $country, category: $category, symbol: $symbol, isCreatable: $isCreatable, isFundable: $isFundable, paymentMethods: $paymentMethods, minimumTransferAmount: $minimumTransferAmount, flag: $flag, recipientCurrencies: $recipientCurrencies, deletedAt: $deletedAt, createdAt: $createdAt, updatedAt: $updatedAt)';
  }
}

class PaymentMethod {
  final String? id;
  final String? icon;
  final String? name;
  final bool? nameCheck;

  PaymentMethod({
    this.id,
    this.icon,
    this.name,
    this.nameCheck,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) => PaymentMethod(
        id: json["id"],
        icon: json["icon"],
        name: json["name"],
        nameCheck: json["name_check"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "icon": icon,
        "name": name,
        "name_check": nameCheck,
      };

  @override
  String toString() {
    return 'PaymentMethod(id: $id, icon: $icon, name: $name, nameCheck: $nameCheck)';
  }
}
