import 'dart:convert';

List<Bank> bankFromJson(String str) =>
    List<Bank>.from(json.decode(str).map((x) => Bank.fromJson(x)));

String bankToJson(List<Bank> data) =>
    json.encode(List<dynamic>.from(data.map((x) => x.toJson())));

class Bank {
  String? name;
  String? uuid;
  String? interInstitutionCode;
  String? sortCode;

  Bank({
    this.name,
    this.uuid,
    this.interInstitutionCode,
    this.sortCode,
  });

  factory Bank.fromJson(Map<String, dynamic> json) => Bank(
        name: json["name"],
        uuid: json["uuid"],
        interInstitutionCode: json["interInstitutionCode"],
        sortCode: json["sortCode"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "uuid": uuid,
        "interInstitutionCode": interInstitutionCode,
        "sortCode": sortCode,
      };

  @override
  String toString() {
    return 'Bank(name: $name, uuid: $uuid, interInstitutionCode: $interInstitutionCode, sortCode: $sortCode)';
  }
}
