/// Firebase Analytics Event Names and Parameter Constants
/// 
/// This file contains all the event names and parameter keys used for
/// Firebase Analytics tracking to ensure consistency across the app.
class AnalyticsEvents {
  // Core Conversion Events
  static const String createAccount = 'create_account';
  static const String completeKyc = 'complete_kyc';
  static const String depositMoney = 'deposit_money';
  static const String sendMoney = 'send_money';
  
  // User Journey Events
  static const String appOpen = 'app_open';
  static const String screenView = 'screen_view';
  static const String login = 'login';
  static const String logout = 'logout';
  static const String signUp = 'sign_up';
  
  // KYC Related Events
  static const String kycInitiation = 'kyc_initiation';
  static const String kycStepCompleted = 'kyc_step_completed';
  static const String identityVerificationStarted = 'identity_verification_started';
  static const String identityVerificationCompleted = 'identity_verification_completed';
  
  // Transaction Events
  static const String transactionInitiated = 'transaction_initiated';
  static const String transactionCompleted = 'transaction_completed';
  static const String transactionFailed = 'transaction_failed';
  static const String paymentMethodSelected = 'payment_method_selected';
  
  // Engagement Events
  static const String notificationViewed = 'notification_viewed';
  static const String referralCodeUsed = 'referral_code_used';
  static const String supportContactInitiated = 'support_contact_initiated';
  
  // Error Events
  static const String errorOccurred = 'error_occurred';
  static const String apiError = 'api_error';
}

/// Firebase Analytics Parameter Keys
class AnalyticsParameters {
  // User Parameters
  static const String userId = 'user_id';
  static const String userCountry = 'user_country';
  static const String userTier = 'user_tier';
  static const String registrationMethod = 'registration_method';
  
  // Transaction Parameters
  static const String amount = 'amount';
  static const String currency = 'currency';
  static const String transactionId = 'transaction_id';
  static const String paymentMethod = 'payment_method';
  static const String corridor = 'corridor';
  static const String senderCountry = 'sender_country';
  static const String recipientCountry = 'recipient_country';
  static const String transferMethod = 'transfer_method';
  
  // KYC Parameters
  static const String kycLevel = 'kyc_level';
  static const String kycStep = 'kyc_step';
  static const String verificationMethod = 'verification_method';
  static const String completionTime = 'completion_time';
  
  // General Parameters
  static const String timestamp = 'timestamp';
  static const String screenName = 'screen_name';
  static const String errorMessage = 'error_message';
  static const String errorCode = 'error_code';
  static const String source = 'source';
  static const String method = 'method';
  static const String success = 'success';
  
  // Notification Parameters
  static const String notificationId = 'notification_id';
  static const String notificationTitle = 'notification_title';
  static const String notificationMessage = 'notification_message';
  
  // Referral Parameters
  static const String referralCode = 'referral_code';
  static const String referrer = 'referrer';
}

/// Predefined Values for Analytics Parameters
class AnalyticsValues {
  // Registration Methods
  static const String registrationEmail = 'email';
  static const String registrationPhone = 'phone';
  static const String registrationSocial = 'social';
  
  // KYC Levels
  static const String kycBasic = 'basic';
  static const String kycAdvanced = 'advanced';
  static const String kycPremium = 'premium';
  
  // Payment Methods
  static const String paymentCard = 'card';
  static const String paymentBankTransfer = 'bank_transfer';
  static const String paymentWallet = 'wallet';
  static const String paymentCrypto = 'crypto';
  
  // Transfer Methods
  static const String transferInstant = 'instant';
  static const String transferStandard = 'standard';
  static const String transferExpress = 'express';
  
  // User Tiers
  static const String tierBasic = 'basic';
  static const String tierPremium = 'premium';
  static const String tierVip = 'vip';
  
  // Common Sources
  static const String sourceApp = 'app';
  static const String sourceWeb = 'web';
  static const String sourceDeepLink = 'deep_link';
  static const String sourceNotification = 'notification';
  static const String sourceReferral = 'referral';
}

/// Screen Names for Analytics
class AnalyticsScreens {
  static const String splash = 'splash';
  static const String onboarding = 'onboarding';
  static const String login = 'login';
  static const String signup = 'signup';
  static const String home = 'home';
  static const String dashboard = 'dashboard';
  static const String profile = 'profile';
  static const String kyc = 'kyc';
  static const String kycComplete = 'kyc_complete';
  static const String identityVerification = 'identity_verification';
  static const String sendMoney = 'send_money';
  static const String depositMoney = 'deposit_money';
  static const String transactionHistory = 'transaction_history';
  static const String notifications = 'notifications';
  static const String settings = 'settings';
  static const String support = 'support';
}
