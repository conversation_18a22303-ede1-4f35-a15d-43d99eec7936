import 'package:korrency/core/core.dart';

void handleApiResponse({
  required ApiResponse response,
  void Function()? onSuccess,
  void Function()? onError,
  bool showSuccessToast = true,
  bool showErrorToast = true,
}) {
  if (response.success) {
    if (onSuccess != null) onSuccess();
    if (showSuccessToast) {
      FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.success,
        message: response.message ?? 'Operation successful',
      );
    }
  } else {
    if (onError != null) onError();
    if (showErrorToast) {
      FlushBarToast.fLSnackBar(
        snackBarType: SnackBarType.warning,
        message: response.message ?? 'Something went wrong',
      );
    }
  }
}
