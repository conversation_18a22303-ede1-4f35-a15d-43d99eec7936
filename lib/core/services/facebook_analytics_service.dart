import 'dart:async';
import 'dart:io';

import 'package:facebook_app_events/facebook_app_events.dart';
import 'package:flutter/foundation.dart';
import 'package:korrency/core/core.dart';

/// Facebook Analytics Service
///
/// Comprehensive service for tracking events with Facebook Analytics for
/// optimal ad optimization and conversion tracking. Works alongside Firebase Analytics.
class FacebookAnalyticsService {
  static FacebookAnalyticsService? _instance;
  static FacebookAnalyticsService get instance =>
      _instance ??= FacebookAnalyticsService._();

  FacebookAnalyticsService._();

  late FacebookAppEvents _facebookAppEvents;
  bool _isInitialized = false;
  bool _debugMode = false;

  /// Initialize Facebook Analytics
  Future<void> initialize({bool debugMode = false}) async {
    if (_isInitialized) return;

    try {
      _facebookAppEvents = FacebookAppEvents();
      _debugMode = debugMode || kDebugMode;

      // Set advertiser tracking enabled (iOS 14.5+)
      await _facebookAppEvents.setAdvertiserTracking(enabled: true);

      if (_debugMode) {
        printty('🔧 Facebook Analytics initialized in DEBUG mode');
      } else {
        printty('📊 Facebook Analytics initialized in PRODUCTION mode');
      }

      _isInitialized = true;
      printty('✅ Facebook Analytics initialized successfully');

      // Log app open event
      await logEvent(FacebookEvents.viewContent, {
        FacebookParameters.contentType: FacebookValues.contentTypeFinancial,
        FacebookParameters.timestamp: DateTime.now().toIso8601String(),
        FacebookParameters.source: FacebookValues.sourceApp,
      });
    } catch (e) {
      printty('❌ Facebook Analytics initialization error: $e');
      rethrow;
    }
  }

  /// Log a custom event with parameters
  Future<void> logEvent(
      String eventName, Map<String, dynamic>? parameters) async {
    if (!_isInitialized) {
      printty('⚠️ Facebook Analytics not initialized');
      return;
    }

    try {
      // Clean parameters (remove null values and ensure proper types)
      final cleanParameters = _cleanParameters(parameters);

      if (_debugMode) {
        printty('📤 Facebook Analytics Event: $eventName');
        printty('📤 Parameters: $cleanParameters');
      }

      await _facebookAppEvents.logEvent(
        name: eventName,
        parameters: cleanParameters,
      );

      if (_debugMode) {
        printty('✅ Facebook event logged successfully: $eventName');
      }
    } catch (e) {
      printty('❌ Error logging Facebook event "$eventName": $e');
    }
  }

  /// Clean parameters to ensure they meet Facebook requirements
  Map<String, Object>? _cleanParameters(Map<String, dynamic>? parameters) {
    if (parameters == null) return null;

    final cleaned = <String, Object>{};

    for (final entry in parameters.entries) {
      if (entry.value != null) {
        // Convert to appropriate types
        if (entry.value is String ||
            entry.value is num ||
            entry.value is bool) {
          cleaned[entry.key] = entry.value;
        } else {
          // Convert other types to string
          cleaned[entry.key] = entry.value.toString();
        }
      }
    }

    return cleaned.isNotEmpty ? cleaned : null;
  }

  /// Set user ID for tracking
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) return;

    try {
      // Facebook App Events doesn't have a direct setUserId method
      // We'll track the user ID as a custom parameter in events
      // Store the user ID for use in subsequent events
      _currentUserId = userId;
      printty('👤 Facebook Analytics User ID set: $userId');
    } catch (e) {
      printty('❌ Error setting Facebook user ID: $e');
    }
  }

  String? _currentUserId;

  /// Set user data for advanced matching
  Future<void> setUserData({
    String? email,
    String? firstName,
    String? lastName,
    String? phone,
    String? city,
    String? state,
    String? country,
  }) async {
    if (!_isInitialized) return;

    try {
      await _facebookAppEvents.setUserData(
        email: email,
        firstName: firstName,
        lastName: lastName,
        phone: phone,
        city: city,
        state: state,
        country: country,
      );

      if (_debugMode) {
        printty('👥 Facebook user data updated for advanced matching');
      }
    } catch (e) {
      printty('❌ Error setting Facebook user data: $e');
    }
  }

  /// Clear user data
  Future<void> clearUserData() async {
    if (!_isInitialized) return;

    try {
      await _facebookAppEvents.clearUserData();
      printty('🔄 Facebook user data cleared');
    } catch (e) {
      printty('❌ Error clearing Facebook user data: $e');
    }
  }

  /// Flush events (force send pending events)
  Future<void> flush() async {
    if (!_isInitialized) return;

    try {
      await _facebookAppEvents.flush();
      if (_debugMode) {
        printty('🚀 Facebook Analytics events flushed');
      }
    } catch (e) {
      printty('❌ Error flushing Facebook events: $e');
    }
  }

  /// Set advertiser tracking enabled (iOS 14.5+)
  Future<void> setAdvertiserTracking({required bool enabled}) async {
    if (!_isInitialized) return;

    try {
      await _facebookAppEvents.setAdvertiserTracking(enabled: enabled);
      printty(
          '📊 Facebook advertiser tracking ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      printty('❌ Error setting Facebook advertiser tracking: $e');
    }
  }

  /// Log purchase event with value and currency
  Future<void> logPurchase({
    required double amount,
    required String currency,
    Map<String, dynamic>? parameters,
  }) async {
    final purchaseParameters = <String, dynamic>{
      FacebookParameters.value: amount,
      FacebookParameters.currency: currency,
    };

    if (parameters != null) {
      purchaseParameters.addAll(parameters);
    }

    await logEvent(FacebookEvents.purchase, purchaseParameters);
  }

  // ============================================================================
  // CONVERSION EVENT TRACKING METHODS
  // ============================================================================

  /// Track user account creation (Facebook Standard Event)
  Future<void> trackCompleteRegistration({
    required String userId,
    required String registrationMethod,
    String? userCountry,
    String? userTier,
  }) async {
    await logEvent(FacebookEvents.completeRegistration, {
      FacebookParameters.userId: userId,
      FacebookParameters.registrationMethod: registrationMethod,
      FacebookParameters.userCountry: userCountry,
      FacebookParameters.userTier: userTier ?? FacebookValues.tierBasic,
      FacebookParameters.contentType: FacebookValues.contentTypeRegistration,
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
      FacebookParameters.source: FacebookValues.sourceApp,
      FacebookParameters.success: '1',
    });

    // Also set user ID for tracking
    await setUserId(userId);
  }

  /// Track KYC completion (Facebook Standard Event - Achievement Unlocked)
  Future<void> trackAchievementUnlocked({
    required String userId,
    required String kycLevel,
    required String verificationMethod,
    required int completionTimeSeconds,
    String? userTier,
  }) async {
    await logEvent(FacebookEvents.achievementUnlocked, {
      FacebookParameters.userId: userId,
      FacebookParameters.kycLevel: kycLevel,
      FacebookParameters.verificationMethod: verificationMethod,
      FacebookParameters.completionTime: completionTimeSeconds,
      FacebookParameters.userTier: userTier,
      FacebookParameters.contentType: FacebookValues.contentTypeKyc,
      FacebookParameters.description: 'KYC Verification Completed',
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
    });
  }

  /// Track payment info addition (Facebook Standard Event)
  Future<void> trackAddPaymentInfo({
    required String userId,
    required String currency,
    required String paymentMethod,
    required String transactionId,
    String? corridor,
    double? amount,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      FacebookParameters.userId: userId,
      FacebookParameters.currency: currency,
      FacebookParameters.paymentMethod: paymentMethod,
      FacebookParameters.transactionId: transactionId,
      FacebookParameters.corridor: corridor,
      FacebookParameters.contentType: FacebookValues.contentTypeDeposit,
      FacebookParameters.paymentInfoAvailable: '1',
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
    };

    if (amount != null) {
      parameters[FacebookParameters.value] = amount;
    }

    if (additionalParameters != null) {
      parameters.addAll(additionalParameters);
    }

    await logEvent(FacebookEvents.addPaymentInfo, parameters);
  }

  /// Track money transfer (Facebook Standard Event - Purchase)
  Future<void> trackPurchase({
    required String userId,
    required double amount,
    required String currency,
    required String senderCountry,
    required String recipientCountry,
    required String transactionId,
    required String transferMethod,
    String? corridor,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final corridorValue = corridor ?? '$senderCountry-$recipientCountry';

    final parameters = <String, dynamic>{
      FacebookParameters.userId: userId,
      FacebookParameters.value: amount,
      FacebookParameters.currency: currency,
      FacebookParameters.senderCountry: senderCountry,
      FacebookParameters.recipientCountry: recipientCountry,
      FacebookParameters.corridor: corridorValue,
      FacebookParameters.transactionId: transactionId,
      FacebookParameters.transferMethod: transferMethod,
      FacebookParameters.contentType: FacebookValues.contentTypeTransfer,
      FacebookParameters.contentId: transactionId,
      FacebookParameters.numItems: 1,
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
    };

    if (additionalParameters != null) {
      parameters.addAll(additionalParameters);
    }

    await logEvent(FacebookEvents.purchase, parameters);
  }

  // ============================================================================
  // ADDITIONAL TRACKING METHODS
  // ============================================================================

  /// Track KYC initiation
  Future<void> trackKycInitiation({
    required String userId,
    required int kycStep,
    required String page,
  }) async {
    await logEvent(FacebookEvents.kycInitiation, {
      FacebookParameters.userId: userId,
      'kyc_step': kycStep,
      'page': page,
      FacebookParameters.contentType: FacebookValues.contentTypeKyc,
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
    });
  }

  /// Track user login
  Future<void> trackLogin({
    required String userId,
    String? method,
  }) async {
    await logEvent(FacebookEvents.loginAttempt, {
      FacebookParameters.userId: userId,
      FacebookParameters.method: method ?? 'email',
      FacebookParameters.success: '1',
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
    });

    await setUserId(userId);
  }

  /// Track user logout
  Future<void> trackLogout({
    String? userId,
  }) async {
    await logEvent(FacebookEvents.logoutEvent, {
      FacebookParameters.userId: userId,
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
    });

    // Clear user data on logout
    await clearUserData();
  }

  /// Track screen view
  Future<void> trackScreenView(String screenName) async {
    await logEvent(FacebookEvents.screenView, {
      FacebookParameters.screenName: screenName,
      FacebookParameters.contentType: FacebookValues.contentTypeFinancial,
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
    });
  }

  /// Track notification viewed
  Future<void> trackNotificationViewed({
    required String notificationId,
    required String title,
    String? message,
    String? userId,
  }) async {
    await logEvent(FacebookEvents.notificationViewed, {
      FacebookParameters.notificationId: notificationId,
      FacebookParameters.notificationTitle: title,
      FacebookParameters.notificationMessage: message,
      FacebookParameters.userId: userId,
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
    });
  }

  /// Track error occurrence
  Future<void> trackError({
    required String errorMessage,
    String? errorCode,
    String? source,
    String? userId,
  }) async {
    await logEvent(FacebookEvents.errorOccurred, {
      FacebookParameters.errorMessage: errorMessage,
      FacebookParameters.errorCode: errorCode,
      FacebookParameters.source: source,
      FacebookParameters.userId: userId,
      FacebookParameters.timestamp: DateTime.now().toIso8601String(),
    });
  }

  // ============================================================================
  // DEBUG AND TESTING METHODS
  // ============================================================================

  /// Test Facebook Analytics integration by logging a test event
  Future<void> testAnalyticsIntegration() async {
    if (!_isInitialized) {
      printty('⚠️ Facebook Analytics not initialized for testing');
      return;
    }

    try {
      await logEvent('test_facebook_integration', {
        'test_timestamp': DateTime.now().toIso8601String(),
        'test_platform': Platform.isIOS ? 'iOS' : 'Android',
        'test_debug_mode': _debugMode.toString(),
        'test_version': '1.0.0',
      });

      printty('✅ Facebook Analytics test event logged successfully');

      if (_debugMode) {
        printty(
            '🔧 Debug mode is enabled - events should appear in Facebook Analytics');
        printty('🔧 To view events: Facebook Analytics Dashboard > Events');
      }
    } catch (e) {
      printty('❌ Facebook Analytics test failed: $e');
    }
  }

  /// Test all conversion events with sample data
  Future<void> testConversionEvents() async {
    if (!_isInitialized) {
      printty('⚠️ Facebook Analytics not initialized for testing');
      return;
    }

    final testUserId = 'test_user_${DateTime.now().millisecondsSinceEpoch}';

    try {
      // Test complete_registration event
      await trackCompleteRegistration(
        userId: testUserId,
        registrationMethod: FacebookValues.registrationEmail,
        userCountry: 'Canada',
        userTier: FacebookValues.tierBasic,
      );
      printty('✅ Test fb_mobile_complete_registration event logged');

      // Test achievement_unlocked event (KYC)
      await trackAchievementUnlocked(
        userId: testUserId,
        kycLevel: FacebookValues.kycAdvanced,
        verificationMethod: 'onfido',
        completionTimeSeconds: 300,
        userTier: FacebookValues.tierPremium,
      );
      printty('✅ Test fb_mobile_achievement_unlocked event logged');

      // Test add_payment_info event
      await trackAddPaymentInfo(
        userId: testUserId,
        currency: 'CAD',
        paymentMethod: FacebookValues.paymentBankTransfer,
        transactionId: 'test_deposit_123',
        corridor: 'Canada-CAD',
        amount: 100.0,
      );
      printty('✅ Test fb_mobile_add_payment_info event logged');

      // Test purchase event (send money)
      await trackPurchase(
        userId: testUserId,
        amount: 50.0,
        currency: 'CAD',
        senderCountry: 'Canada',
        recipientCountry: 'Nigeria',
        transactionId: 'test_transfer_456',
        transferMethod: FacebookValues.transferInstant,
        corridor: 'Canada-Nigeria',
      );
      printty('✅ Test fb_mobile_purchase event logged');

      printty('🎉 All Facebook conversion events tested successfully!');

      if (_debugMode) {
        printty('🔧 Check Facebook Analytics Dashboard to see the test events');
      }
    } catch (e) {
      printty('❌ Facebook conversion events test failed: $e');
    }
  }

  /// Get current analytics configuration for debugging
  Map<String, dynamic> getAnalyticsConfig() {
    return {
      'initialized': _isInitialized,
      'debug_mode': _debugMode,
      'platform': Platform.isIOS ? 'iOS' : 'Android',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Log analytics configuration for debugging
  void logAnalyticsConfig() {
    final config = getAnalyticsConfig();
    printty('📊 Facebook Analytics Configuration:');
    config.forEach((key, value) {
      printty('  $key: $value');
    });
  }

  /// Enable debug logging for analytics events
  void enableDebugLogging() {
    _debugMode = true;
    printty('🔧 Facebook Analytics debug logging enabled');
  }

  /// Disable debug logging for analytics events
  void disableDebugLogging() {
    _debugMode = false;
    printty('🔧 Facebook Analytics debug logging disabled');
  }
}
