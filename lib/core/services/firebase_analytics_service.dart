import 'dart:async';
import 'dart:io';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';
import 'package:korrency/core/core.dart';

/// Firebase Analytics Service
///
/// Comprehensive service for tracking events, user properties, and conversions
/// with Firebase Analytics. Integrates with GA4 for conversion optimization.
class FirebaseAnalyticsService {
  static FirebaseAnalyticsService? _instance;
  static FirebaseAnalyticsService get instance =>
      _instance ??= FirebaseAnalyticsService._();

  FirebaseAnalyticsService._();

  late FirebaseAnalytics _analytics;
  late FirebaseAnalyticsObserver _observer;
  bool _isInitialized = false;
  bool _debugMode = false;

  /// Get the FirebaseAnalyticsObserver for navigation tracking
  FirebaseAnalyticsObserver get observer => _observer;

  /// Initialize Firebase Analytics
  Future<void> initialize({bool debugMode = false}) async {
    if (_isInitialized) return;

    try {
      _analytics = FirebaseAnalytics.instance;
      _observer = FirebaseAnalyticsObserver(analytics: _analytics);
      _debugMode = debugMode || kDebugMode;

      // Set debug mode for development
      if (_debugMode) {
        await _analytics.setAnalyticsCollectionEnabled(true);
        printty('🔧 Firebase Analytics initialized in DEBUG mode');
      } else {
        printty('📊 Firebase Analytics initialized in PRODUCTION mode');
      }

      // Set default user properties
      await _setDefaultUserProperties();

      _isInitialized = true;
      printty('✅ Firebase Analytics initialized successfully');

      // Log app open event
      await logEvent(AnalyticsEvents.appOpen, {
        AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
        AnalyticsParameters.source: AnalyticsValues.sourceApp,
      });
    } catch (e) {
      printty('❌ Firebase Analytics initialization error: $e');
      rethrow;
    }
  }

  /// Set default user properties
  Future<void> _setDefaultUserProperties() async {
    try {
      await _analytics.setUserProperty(
        name: 'platform',
        value: Platform.isIOS ? 'iOS' : 'Android',
      );

      await _analytics.setUserProperty(
        name: 'app_version',
        value: await _getAppVersion(),
      );

      await _analytics.setUserProperty(
        name: 'debug_mode',
        value: _debugMode.toString(),
      );
    } catch (e) {
      printty('❌ Error setting default user properties: $e');
    }
  }

  /// Get app version from storage or package info
  Future<String> _getAppVersion() async {
    try {
      return await StorageService.getStringItem(StorageKey.appVersion) ??
          '1.0.0';
    } catch (e) {
      return '1.0.0';
    }
  }

  /// Log a custom event with parameters
  Future<void> logEvent(
      String eventName, Map<String, dynamic>? parameters) async {
    if (!_isInitialized) {
      printty('⚠️ Firebase Analytics not initialized');
      return;
    }

    try {
      // Clean parameters (remove null values and ensure proper types)
      final cleanParameters = _cleanParameters(parameters);

      if (_debugMode) {
        printty('📤 Firebase Analytics Event: $eventName');
        printty('📤 Parameters: $cleanParameters');
      }

      await _analytics.logEvent(
        name: eventName,
        parameters: cleanParameters,
      );

      if (_debugMode) {
        printty('✅ Event logged successfully: $eventName');
      }
    } catch (e) {
      printty('❌ Error logging event "$eventName": $e');
    }
  }

  /// Clean parameters to ensure they meet Firebase requirements
  Map<String, Object>? _cleanParameters(Map<String, dynamic>? parameters) {
    if (parameters == null) return null;

    final cleaned = <String, Object>{};

    for (final entry in parameters.entries) {
      if (entry.value != null) {
        // Convert to appropriate types
        if (entry.value is String ||
            entry.value is num ||
            entry.value is bool) {
          cleaned[entry.key] = entry.value;
        } else {
          // Convert other types to string
          cleaned[entry.key] = entry.value.toString();
        }
      }
    }

    return cleaned.isNotEmpty ? cleaned : null;
  }

  /// Set user ID for tracking
  Future<void> setUserId(String userId) async {
    if (!_isInitialized) return;

    try {
      await _analytics.setUserId(id: userId);
      printty('👤 Firebase Analytics User ID set: $userId');
    } catch (e) {
      printty('❌ Error setting user ID: $e');
    }
  }

  /// Set user properties
  Future<void> setUserProperty(String name, String? value) async {
    if (!_isInitialized) return;

    try {
      await _analytics.setUserProperty(name: name, value: value);
      if (_debugMode) {
        printty('👥 User property set: $name = $value');
      }
    } catch (e) {
      printty('❌ Error setting user property "$name": $e');
    }
  }

  /// Set multiple user properties at once
  Future<void> setUserProperties(Map<String, String?> properties) async {
    for (final entry in properties.entries) {
      await setUserProperty(entry.key, entry.value);
    }
  }

  /// Log screen view event
  Future<void> logScreenView(String screenName, {String? screenClass}) async {
    await logEvent(AnalyticsEvents.screenView, {
      AnalyticsParameters.screenName: screenName,
      'screen_class': screenClass ?? screenName,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
    });
  }

  /// Reset analytics data (for user logout)
  Future<void> resetAnalyticsData() async {
    if (!_isInitialized) return;

    try {
      await _analytics.resetAnalyticsData();
      printty('🔄 Firebase Analytics data reset');
    } catch (e) {
      printty('❌ Error resetting analytics data: $e');
    }
  }

  /// Enable/disable analytics collection
  Future<void> setAnalyticsCollectionEnabled(bool enabled) async {
    if (!_isInitialized) return;

    try {
      await _analytics.setAnalyticsCollectionEnabled(enabled);
      printty('📊 Analytics collection ${enabled ? 'enabled' : 'disabled'}');
    } catch (e) {
      printty('❌ Error setting analytics collection: $e');
    }
  }

  // ============================================================================
  // CONVERSION EVENT TRACKING METHODS
  // ============================================================================

  /// Track user account creation (Conversion Event)
  Future<void> trackCreateAccount({
    required String userId,
    required String registrationMethod,
    String? userCountry,
    String? userTier,
  }) async {
    await logEvent(AnalyticsEvents.createAccount, {
      AnalyticsParameters.userId: userId,
      AnalyticsParameters.registrationMethod: registrationMethod,
      AnalyticsParameters.userCountry: userCountry,
      AnalyticsParameters.userTier: userTier ?? AnalyticsValues.tierBasic,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      AnalyticsParameters.source: AnalyticsValues.sourceApp,
    });

    // Also set user properties
    await setUserId(userId);
    await setUserProperties({
      AnalyticsParameters.userCountry: userCountry,
      AnalyticsParameters.userTier: userTier ?? AnalyticsValues.tierBasic,
      AnalyticsParameters.registrationMethod: registrationMethod,
    });
  }

  /// Track KYC completion (Conversion Event)
  Future<void> trackCompleteKyc({
    required String userId,
    required String kycLevel,
    required String verificationMethod,
    required int completionTimeSeconds,
    String? userTier,
  }) async {
    await logEvent(AnalyticsEvents.completeKyc, {
      AnalyticsParameters.userId: userId,
      AnalyticsParameters.kycLevel: kycLevel,
      AnalyticsParameters.verificationMethod: verificationMethod,
      AnalyticsParameters.completionTime: completionTimeSeconds,
      AnalyticsParameters.userTier: userTier,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
    });

    // Update user properties
    await setUserProperties({
      AnalyticsParameters.kycLevel: kycLevel,
      AnalyticsParameters.userTier: userTier,
    });
  }

  /// Track money deposit (Conversion Event)
  Future<void> trackDepositMoney({
    required String userId,
    required double amount,
    required String currency,
    required String paymentMethod,
    required String transactionId,
    String? corridor,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final parameters = <String, dynamic>{
      AnalyticsParameters.userId: userId,
      AnalyticsParameters.amount: amount,
      AnalyticsParameters.currency: currency,
      AnalyticsParameters.paymentMethod: paymentMethod,
      AnalyticsParameters.transactionId: transactionId,
      AnalyticsParameters.corridor: corridor,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      'value': amount, // For GA4 conversion value
    };

    if (additionalParameters != null) {
      parameters.addAll(additionalParameters);
    }

    await logEvent(AnalyticsEvents.depositMoney, parameters);
  }

  /// Track money transfer (Conversion Event)
  Future<void> trackSendMoney({
    required String userId,
    required double amount,
    required String currency,
    required String senderCountry,
    required String recipientCountry,
    required String transactionId,
    required String transferMethod,
    String? corridor,
    Map<String, dynamic>? additionalParameters,
  }) async {
    final corridorValue = corridor ?? '$senderCountry-$recipientCountry';

    final parameters = <String, dynamic>{
      AnalyticsParameters.userId: userId,
      AnalyticsParameters.amount: amount,
      AnalyticsParameters.currency: currency,
      AnalyticsParameters.senderCountry: senderCountry,
      AnalyticsParameters.recipientCountry: recipientCountry,
      AnalyticsParameters.corridor: corridorValue,
      AnalyticsParameters.transactionId: transactionId,
      AnalyticsParameters.transferMethod: transferMethod,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
      'value': amount, // For GA4 conversion value
    };

    if (additionalParameters != null) {
      parameters.addAll(additionalParameters);
    }

    await logEvent(AnalyticsEvents.sendMoney, parameters);
  }

  // ============================================================================
  // ADDITIONAL TRACKING METHODS
  // ============================================================================

  /// Track KYC initiation
  Future<void> trackKycInitiation({
    required String userId,
    required int kycStep,
    required String page,
  }) async {
    await logEvent(AnalyticsEvents.kycInitiation, {
      AnalyticsParameters.userId: userId,
      AnalyticsParameters.kycStep: kycStep,
      'page': page,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
    });
  }

  /// Track user login
  Future<void> trackLogin({
    required String userId,
    String? method,
  }) async {
    await logEvent(AnalyticsEvents.login, {
      AnalyticsParameters.userId: userId,
      AnalyticsParameters.method: method ?? 'email',
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
    });

    await setUserId(userId);
  }

  /// Track user logout
  Future<void> trackLogout({
    String? userId,
  }) async {
    await logEvent(AnalyticsEvents.logout, {
      AnalyticsParameters.userId: userId,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
    });

    // Reset analytics data on logout
    await resetAnalyticsData();
  }

  /// Track notification viewed
  Future<void> trackNotificationViewed({
    required String notificationId,
    required String title,
    String? message,
    String? userId,
  }) async {
    await logEvent(AnalyticsEvents.notificationViewed, {
      AnalyticsParameters.notificationId: notificationId,
      AnalyticsParameters.notificationTitle: title,
      AnalyticsParameters.notificationMessage: message,
      AnalyticsParameters.userId: userId,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
    });
  }

  /// Track error occurrence
  Future<void> trackError({
    required String errorMessage,
    String? errorCode,
    String? source,
    String? userId,
  }) async {
    await logEvent(AnalyticsEvents.errorOccurred, {
      AnalyticsParameters.errorMessage: errorMessage,
      AnalyticsParameters.errorCode: errorCode,
      AnalyticsParameters.source: source,
      AnalyticsParameters.userId: userId,
      AnalyticsParameters.timestamp: DateTime.now().toIso8601String(),
    });
  }

  // ============================================================================
  // DEBUG AND TESTING METHODS
  // ============================================================================

  /// Test Firebase Analytics integration by logging a test event
  Future<void> testAnalyticsIntegration() async {
    if (!_isInitialized) {
      printty('⚠️ Firebase Analytics not initialized for testing');
      return;
    }

    try {
      await logEvent('test_analytics_integration', {
        'test_timestamp': DateTime.now().toIso8601String(),
        'test_platform': Platform.isIOS ? 'iOS' : 'Android',
        'test_debug_mode': _debugMode.toString(),
        'test_version': '1.0.0',
      });

      printty('✅ Firebase Analytics test event logged successfully');

      if (_debugMode) {
        printty(
            '🔧 Debug mode is enabled - events should appear in Firebase Analytics DebugView');
        printty('🔧 To view events: Firebase Console > Analytics > DebugView');
      }
    } catch (e) {
      printty('❌ Firebase Analytics test failed: $e');
    }
  }

  /// Test all conversion events with sample data
  Future<void> testConversionEvents() async {
    if (!_isInitialized) {
      printty('⚠️ Firebase Analytics not initialized for testing');
      return;
    }

    final testUserId = 'test_user_${DateTime.now().millisecondsSinceEpoch}';

    try {
      // Test create_account event
      await trackCreateAccount(
        userId: testUserId,
        registrationMethod: AnalyticsValues.registrationEmail,
        userCountry: 'Canada',
        userTier: AnalyticsValues.tierBasic,
      );
      printty('✅ Test create_account event logged');

      // Test complete_kyc event
      await trackCompleteKyc(
        userId: testUserId,
        kycLevel: AnalyticsValues.kycAdvanced,
        verificationMethod: 'onfido',
        completionTimeSeconds: 300,
        userTier: AnalyticsValues.tierPremium,
      );
      printty('✅ Test complete_kyc event logged');

      // Test deposit_money event
      await trackDepositMoney(
        userId: testUserId,
        amount: 100.0,
        currency: 'CAD',
        paymentMethod: AnalyticsValues.paymentBankTransfer,
        transactionId: 'test_deposit_123',
        corridor: 'Canada-CAD',
      );
      printty('✅ Test deposit_money event logged');

      // Test send_money event
      await trackSendMoney(
        userId: testUserId,
        amount: 50.0,
        currency: 'CAD',
        senderCountry: 'Canada',
        recipientCountry: 'Nigeria',
        transactionId: 'test_transfer_456',
        transferMethod: AnalyticsValues.transferInstant,
        corridor: 'Canada-Nigeria',
      );
      printty('✅ Test send_money event logged');

      printty('🎉 All conversion events tested successfully!');

      if (_debugMode) {
        printty('🔧 Check Firebase Analytics DebugView to see the test events');
      }
    } catch (e) {
      printty('❌ Conversion events test failed: $e');
    }
  }

  /// Get current analytics configuration for debugging
  Map<String, dynamic> getAnalyticsConfig() {
    return {
      'initialized': _isInitialized,
      'debug_mode': _debugMode,
      'platform': Platform.isIOS ? 'iOS' : 'Android',
      'timestamp': DateTime.now().toIso8601String(),
    };
  }

  /// Log analytics configuration for debugging
  void logAnalyticsConfig() {
    final config = getAnalyticsConfig();
    printty('📊 Firebase Analytics Configuration:');
    config.forEach((key, value) {
      printty('  $key: $value');
    });
  }

  /// Enable debug logging for analytics events
  void enableDebugLogging() {
    _debugMode = true;
    printty('🔧 Firebase Analytics debug logging enabled');
  }

  /// Disable debug logging for analytics events
  void disableDebugLogging() {
    _debugMode = false;
    printty('🔧 Firebase Analytics debug logging disabled');
  }
}
