import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:korrency/core/core.dart';

const String convertMoneyLoading = "convertMoneyLoading";

class ConvertMoneyVM extends BaseVM {
  TextEditingController fromC = TextEditingController();
  TextEditingController toC = TextEditingController();
  // TextEditingController pinC = TextEditingController();

  /// if userHasTransaction is false, use the new conversion rate
  /// & new rate format
  bool? _userHasTransaction;
  String? _newCustomersRateMinimumAmount;
  late WalletVM walletProvider;

  init({
    required WalletVM walletProvider,
    ConvertArg? currencyCodesFromTransactionDetails,
    bool? userHasTransaction,
    String? newCustomersRateMinimumAmount,
    String? freqDestinationCurrencyCode,
  }) {
    this.walletProvider = walletProvider;

    /// if User is coming from transactions details screen and wants to convert again
    /// For _fromConvertWallet
    if (currencyCodesFromTransactionDetails != null) {
      _fromConvertWallet = walletProvider.walletList.firstWhereOrNull(
        (element) =>
            element.currency?.code ==
            currencyCodesFromTransactionDetails.fromCurrencyCode,
      );
    } else {
      _fromConvertWalletStore = walletProvider.activeWallet;
      _fromConvertWallet = walletProvider.activeWallet;
    }

    /// if User is coming from transactions details screen and wants to convert again
    /// For _toConvertWallet
    if (currencyCodesFromTransactionDetails != null) {
      _toConvertWallet = walletProvider.walletList.firstWhereOrNull(
        (element) =>
            element.currency?.code ==
            currencyCodesFromTransactionDetails.toCurrencyCode,
      );
    } else {
      // If freqDestinationCurrencyCode is not null, use that as the toConvertWallet
      // Otherwise, use the first wallet that is not the fromConvertWallet
      _toConvertWallet = walletProvider.walletList.firstWhereOrNull((element) {
        if (freqDestinationCurrencyCode != null) {
          return element.currency?.code == freqDestinationCurrencyCode;
        }
        return false;
      });
    }

    // If _toConvertWallet is still null, use the first wallet that is not the fromConvertWallet
    _toConvertWallet ??= walletProvider.walletList.firstWhereOrNull((element) {
      return element.currency?.code != _fromConvertWallet?.currency?.code;
    });

    _userHasTransaction = userHasTransaction;
    _newCustomersRateMinimumAmount = newCustomersRateMinimumAmount;

    reBuildUI();
  }

  // This temporarily stores the wallet selected for conversion
  // This make it easier for swaping the wallets
  Wallet? _fromConvertWalletStore;
  Wallet? get fromConvertWalletStore => _fromConvertWalletStore;
  Wallet? _fromConvertWallet;
  Wallet? get fromConvertWallet => _fromConvertWallet;
  Wallet? _toConvertWallet;
  Wallet? get toConvertWallet => _toConvertWallet;
  CoversionRate? _conversionRate;
  CoversionRate? get conversionRate => _conversionRate;
  TransactionFee? _transactionFee;
  TransactionFee? get transactionFee => _transactionFee;

  // This is whole from amount value without rounding up
  String? _fromAmount;
  String? get fromAmount => _fromAmount;
  double? _convertedAmount;
  double? get convertedAmount => _convertedAmount;
  bool _notEnoughBalanceFrom = false;
  bool get notEnoughBalanceFrom => _notEnoughBalanceFrom;
  bool _notEnoughBalanceTo = false;
  bool get notEnoughBalanceTo => _notEnoughBalanceTo;
  bool get convertBtnEnabled =>
      fromC.text.isNotEmpty &&
      toC.text.isNotEmpty &&
      !_notEnoughBalanceFrom &&
      (fromConvertWallet?.currency?.code != toConvertWallet?.currency?.code);

  String get rateFormat =>
      (setUserHasRateMinAmount && (_userHasTransaction == false))
          ? (_conversionRate?.rate?.newCustomerRateFormat ?? "0.00")
          : (_conversionRate?.rate?.rateFormat ?? "0.00");

  bool get setUserHasRateMinAmount {
    final rateMinAmt =
        double.tryParse(_newCustomersRateMinimumAmount ?? "0") ?? 0;

    if (_fromConvertWallet?.currency?.code == CurrencyConstant.cadCurrency) {
      final cadAmountDouble =
          double.tryParse(fromC.text.trim().replaceAllCommas()) ?? 0;
      return cadAmountDouble >= rateMinAmt;
    }
    return false;
  }

  // Checks if the currency is the same (from -> to)
  bool get currencyUpAndDownIsSame {
    if (_fromConvertWallet?.currency?.code ==
        _toConvertWallet?.currency?.code) {
      return true;
    }
    return false;
  }

  /// This will retrurn true if user has transaction = false
  /// and the amount is greater than the rate minimum amount
  /// and the currency is not the same
  bool get setRateBannerToGreen {
    final rateMinAmt =
        double.tryParse(_newCustomersRateMinimumAmount ?? "0") ?? 0;

    if (currencyUpAndDownIsSame) {
      return false;
    }

    if (_fromConvertWallet?.currency?.code == CurrencyConstant.cadCurrency) {
      final cadAmountDouble =
          double.tryParse(fromC.text.trim().replaceAllCommas()) ?? 0;
      return (cadAmountDouble >= rateMinAmt) && _userHasTransaction == false;
    } else if (_toConvertWallet?.currency?.code ==
        CurrencyConstant.cadCurrency) {
      final cadAmountDouble =
          double.tryParse(toC.text.trim().replaceAllCommas()) ?? 0;
      return (cadAmountDouble >= rateMinAmt) && _userHasTransaction == false;
    }

    return false;
  }

  setFromAmount(String amt) {
    _fromAmount = amt;
    reBuildUI();
  }

  setFromConvertWallet(String code) {
    printty("setFromConvertWallet: $code");
    var fromWallet = walletProvider.walletList.firstWhereOrNull(
      (element) => element.currency?.code == code,
    );
    _fromConvertWalletStore = fromWallet;
    _fromConvertWallet = fromWallet;

    reBuildUI();
  }

  setToConvertWallet(String code) {
    printty("setToConvertWallet: $code");
    _toConvertWallet = walletProvider.walletList.firstWhereOrNull(
      (element) => element.currency?.code == code,
    );
    reBuildUI();
  }

  // This is converting from 'FROM' currency to 'TO' currency
  void convertAmount(double amount) {
    // This is whole from amount value without rounding up
    _fromAmount = amount.toString();

    double convertedAmt;
    var rate = _conversionRate?.rate?.multiplyBy;
    if (rate == null) {
      return;
    }
    _notEnoughBalanceFrom = amount >
        (double.tryParse(_fromConvertWallet?.balance.toString() ?? "0") ?? 0);
    final double conversionRate = double.tryParse(rate.toString()) ?? 0.0;
    convertedAmt = amount * conversionRate;
    printty("convertedAmt amt: $amount");
    printty("convertedAmt: $convertedAmt");
    printty("convertedAmt Rate: $conversionRate");
    _convertedAmount = convertedAmt;
    toC.text = AppUtils.formatAmountDoubleString(convertedAmt.toString());
    // toC.text = convertedAmt.toString();
    printty("toC.text: ${toC.text}");
    reBuildUI();
  }

  /// Inverse of convertAmount()
  /// This is converting from 'TO' currency to 'FROM' currency
  void convertAmountTo(double amount) {
    double convertedAmt;
    var rate = _conversionRate?.rate?.multiplyBy;
    if (rate == null) {
      return;
    }
    _notEnoughBalanceTo = amount >
        (double.tryParse(_toConvertWallet?.balance.toString() ?? "0") ?? 0);
    final double conversionRate = double.tryParse(rate.toString()) ?? 0.0;
    convertedAmt = amount / conversionRate;
    _notEnoughBalanceFrom = convertedAmt >
        (double.tryParse(_fromConvertWallet?.balance.toString() ?? "0") ?? 0);

    _fromAmount = convertedAmt.toString();
    fromC.text = AppUtils.formatAmountDoubleString(convertedAmt.toString());
    printty("toC.text: ${fromC.text}");
    reBuildUI();
  }

  addAllFromWalletBalance() {
    fromC.text = _fromConvertWallet?.balance.toString() ?? "0";

    convertAmount(double.tryParse(fromC.text) ?? 0.0);
    reBuildUI();
  }

  swapWallets() {
    var temp = _fromConvertWallet;
    _fromConvertWallet = _toConvertWallet;
    _toConvertWallet = temp;

    getConversionRate()
        .then((value) => convertAmount(double.tryParse(fromC.text) ?? 0.0));
    reBuildUI();
  }

  Future<ApiResponse> getConversionRate(
      {int? fromId, int? toId, bool? isFrom}) async {
    final varFromId = fromId ?? _fromConvertWallet?.currency?.id;
    final varToId = toId ?? _toConvertWallet?.currency?.id;
    return await performApiCall(
      url: "/currencies/rates/$varFromId/$varToId",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var res = coversionRateFromJson(json.encode(data["data"]));
        _conversionRate = res;

        ///This will for reverse conversion
        if (isFrom == true) {
          convertAmount(double.tryParse(fromC.text.replaceAllCommas()) ?? 0);
        } else if (isFrom == false) {
          convertAmountTo(_convertedAmount ?? 0);
        }

        return ApiResponse(success: true, data: res);
      },
    );
  }

  Future<ApiResponse> getFees({required String type, int? currencyId}) async {
    return await performApiCall(
      url: "/transactions/fees/$type/$currencyId",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _transactionFee = TransactionFee.fromJson(data["data"]);
        printty(_transactionFee, level: "Fees");
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> convertMoney() {
    printty(fromC.text);
    return performApiCall(
      url: "/currencies/convert",
      method: apiService.postWithAuth,
      busyObjectName: convertMoneyLoading,
      body: {
        "from_currency_id": _fromConvertWallet?.currency?.id,
        "to_currency_id": _toConvertWallet?.currency?.id,
        "amount": _fromAmount ?? "0.0",
        "rate": _conversionRate?.rate?.rate,
      },
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: apiResponse.data,
          message: apiResponse.message,
        );
      },
    );
  }

  resetData() {
    fromC.clear();
    toC.clear();
    // pinC.clear();
    _convertedAmount = 0.0;
    _notEnoughBalanceFrom = false;

    reBuildUI();
  }

  @override
  void dispose() {
    printty("ConvertMoneyVM Disposed");

    fromC.dispose();
    toC.dispose();
    // pinC.dispose();
    super.dispose();
  }
}
