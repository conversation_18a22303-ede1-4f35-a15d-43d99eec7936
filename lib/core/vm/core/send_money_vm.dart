import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:korrency/core/core.dart';

const String verifyingBankState = "verifyingBankState";

class SendMoneyVM extends BaseVM {
  TextEditingController fromC = TextEditingController();
  TextEditingController recipientC = TextEditingController();
  TextEditingController bankNameC = TextEditingController();
  TextEditingController accountNumC = TextEditingController();

  // TextEditingController interacNameC = TextEditingController();
  TextEditingController interacFNameC = TextEditingController();
  TextEditingController interacLNameC = TextEditingController();
  TextEditingController interacEmailC = TextEditingController();

  List<Wallet> _walletList = [];
  List<Wallet> get walletList => _walletList;
  Wallet? _fromConvertWallet;
  Wallet? get fromConvertWallet => _fromConvertWallet;
  Currency? _recipientCurrency;
  Currency? get recipientCurrency => _recipientCurrency;
  CoversionRate? _conversionRate;
  CoversionRate? get conversionRate => _conversionRate;
  TransactionFee? _transactionFee;
  TransactionFee? get transactionFee => _transactionFee;
  VerifyBank? _verifiedBank;
  VerifyBank? get verifiedBank => _verifiedBank;

  String? _accountName;
  String? get accountName => _accountName;
  String? _bankUUID;
  String? get bankUUID => _bankUUID;

  // For Mobile Money
  MobileMoney? _mobileMoney;
  MobileMoney? get mobileMoney => _mobileMoney;

  int _selectedIndex = -1;
  int get selectedIndex => _selectedIndex;
  TransferMethodType? _transferMethod;
  TransferMethodType? get transferMethod => _transferMethod;

  // For Send moeny methods
  List<PaymentMethod> get paymentMethods {
    final selectedCurrency = _conversionRate?.recipientCurrencies?.firstWhere(
        (method) =>
            method.code?.toLowerCase() ==
            _recipientCurrency?.code?.toLowerCase());

    return selectedCurrency?.paymentMethods ?? [];
  }

  /// Check to see if _recipientCurrency is part of _fromConvertWallet
  /// recipientCurrencies
  bool get isRecipientCurrencyInFromConvertWallet {
    return _fromConvertWallet?.currency?.recipientCurrencies
            ?.any((element) => element.code == _recipientCurrency?.code) ??
        false;
  }

  /// if userHasTransaction is false, use the new conversion rate
  /// & new rate format
  bool? _userHasTransaction;
  String? _newCustomersRateMinimumAmount;

  String?
      _fromDecimalValue; // This is whole from amount value without rounding up

  // Checks if the currency is the same (from -> to)
  bool get currencyUpAndDownIsSame {
    if (_fromConvertWallet?.currency?.code == _recipientCurrency?.code) {
      return true;
    }
    return false;
  }

  String get rateMultiplyBy =>
      (setUserHasRateMinAmount && (_userHasTransaction == false))
          ? (_conversionRate?.rate?.newCustomerMultiplyBy ?? "0.00")
          : (_conversionRate?.rate?.multiplyBy ?? "0.00");

  String get rateFormat {
    if (currencyUpAndDownIsSame) {
      printty("currencyUpAndDownIsSame");
      return _conversionRate?.rate?.rateFormat ?? "0.00";
    }
    return (setUserHasRateMinAmount && (_userHasTransaction == false))
        ? (_conversionRate?.rate?.newCustomerRateFormat ?? "0.00")
        : (_conversionRate?.rate?.rateFormat ?? "0.00");
  }

  String get recipientMinAmount {
    final amount = AppUtils.formatAmountDoubleString(
        _recipientCurrency?.minimumTransferAmount ?? "0");

    return '$amount ${_recipientCurrency?.code}';
  }

  //On Screen init, by Default its CAD to NGN
  init(
      {Wallet? cadWallet,
      Currency? recipientCurrency,
      required List<Wallet> walletList,
      bool? userHasTransaction,
      String? newCustomersRateMinimumAmount,
      fromConvertWallet}) {
    printty("activeWallet $cadWallet");
    _fromConvertWallet = cadWallet;
    _recipientCurrency = recipientCurrency;
    _walletList = walletList;
    _userHasTransaction = userHasTransaction;
    _newCustomersRateMinimumAmount = newCustomersRateMinimumAmount;

    reBuildUI();
  }

  setAccountName(String value) {
    _accountName = value;
    reBuildUI();
  }

  setFromDecimalValue(String value) {
    _fromDecimalValue = value;
    printty("fromDecimalValue $_fromDecimalValue");
    reBuildUI();
  }

  setBank(Bank bank) {
    printty(bank.toString(), level: 'set bank');
    accountNumC.clear();
    clearAcctCredentials();

    bankNameC.text = bank.name!;
    _bankUUID = bank.uuid;

    reBuildUI();
  }

  setTransferMethodType(TransferMethodType method) {
    _transferMethod = method;
    reBuildUI();
  }

  setMobileMoney(MobileMoney? mobileMoney) {
    _mobileMoney = mobileMoney;
    reBuildUI();
  }

  bool get setUserHasRateMinAmount {
    final rateMinAmt =
        double.tryParse(_newCustomersRateMinimumAmount ?? "0") ?? 0;

    if (_fromConvertWallet?.currency?.code == CurrencyConstant.cadCurrency) {
      final cadAmountDouble =
          double.tryParse(fromC.text.trim().replaceAllCommas()) ?? 0;
      return cadAmountDouble >= rateMinAmt;
    } else {
      final cadAmountDouble =
          double.tryParse(recipientC.text.trim().replaceAllCommas()) ?? 0;
      return cadAmountDouble >= rateMinAmt;
    }
  }

  /// This will retrurn true if user has transaction = false
  /// and the amount is greater than the rate minimum amount
  /// and the currency is not the same
  bool get setRateBannerToGreen {
    final rateMinAmt =
        double.tryParse(_newCustomersRateMinimumAmount ?? "0") ?? 0;

    if (currencyUpAndDownIsSame) {
      return false;
    }

    if (_fromConvertWallet?.currency?.code == CurrencyConstant.cadCurrency) {
      final cadAmountDouble =
          double.tryParse(fromC.text.trim().replaceAllCommas()) ?? 0;
      return (cadAmountDouble >= rateMinAmt) && _userHasTransaction == false;
    } else if (_recipientCurrency?.code == CurrencyConstant.cadCurrency) {
      final cadAmountDouble =
          double.tryParse(recipientC.text.trim().replaceAllCommas()) ?? 0;
      return (cadAmountDouble >= rateMinAmt) && _userHasTransaction == false;
    }

    return false;
  }

  bool get isInteracEmaiLValid =>
      interacEmailC.text.contains("@") &&
      interacEmailC.text.contains(".") &&
      interacEmailC.text.split('.').last.isNotEmpty;

  String get fromConvertWalletBalance {
    var balance = AppUtils.formatAmountString(
        _fromConvertWallet?.balance.toString() ?? "0");
    var currencySymbol = _fromConvertWallet?.currency?.symbol ?? "";

    return "$currencySymbol$balance";
  }

  senderGetAmount() {
    printty("rateMultiplyBy $rateMultiplyBy");
    var amountDouble =
        double.tryParse(recipientC.text.trim().replaceAllCommas()) ?? 0;
    if (_recipientCurrency?.code == _fromConvertWallet?.currency?.code) {
      // This is whole from amount value without rounding up
      _fromDecimalValue = amountDouble.toString();
      fromC.text = AppUtils.formatAmountDoubleString(amountDouble.toString());
    } else {
      var rateMultiplyByDouble =
          double.tryParse(rateMultiplyBy.replaceAllCommas()) ?? 0;
      // This is whole from amount value without rounding up
      _fromDecimalValue = (amountDouble / rateMultiplyByDouble).toString();
      printty("_fromDecimalValue $_fromDecimalValue");
      fromC.text =
          AppUtils.formatAmountDoubleString(_fromDecimalValue ?? "0.00");
    }
  }

  String get recipientGetAmount {
    // _fromDecimalValue = fromC.text
    //     .replaceAllCommas();

    var amountDouble = double.tryParse(_fromDecimalValue ?? "0") ?? 0;

    printty('_fromConvertWallet code ${_fromConvertWallet?.currency?.code}');
    printty('_recipientCurrency code ${_recipientCurrency?.code}');
    printty('_recipientCurrency _fromDecimalValue $_fromDecimalValue');

    if (_recipientCurrency?.code == _fromConvertWallet?.currency?.code) {
      return AppUtils.formatAmountDoubleString(amountDouble.toString());
    } else {
      var rateMultiplyByDouble =
          double.tryParse(rateMultiplyBy.replaceAllCommas()) ?? 0;

      printty('rateMultiplyBy $rateMultiplyBy');
      printty('rateMultiplyByDouble $rateMultiplyByDouble');

      return AppUtils.formatAmountDoubleString(
          (amountDouble * rateMultiplyByDouble).toString());
    }
  }

  setRecipientGetAmountToTextField() {
    printty(
        "setRecipientGetAmountToTextField recipientGetAmount $recipientGetAmount");
    recipientC.text = recipientGetAmount;

    reBuildUI();
  }

  String get transferFee {
    var recipientGetAmountDouble =
        double.tryParse(recipientGetAmount.replaceAllCommas()) ?? 0;
    var doubleFee = double.tryParse(_transactionFee?.fee ?? "0") ?? 0;

    if (_transactionFee?.method == OfferConst.multiply) {
      var amount = AppUtils.formatAmountDoubleString(
          (recipientGetAmountDouble * doubleFee).toString());
      var currencyCode = _recipientCurrency?.code ?? "";

      return "$amount $currencyCode";
    } else {
      var amount = AppUtils.formatAmountDoubleString(doubleFee.toString());
      var currencyCode = _recipientCurrency?.code ?? "";

      return "$amount $currencyCode";
    }
  }

// Total is supposed to be recipientGetAmount + transferFee
// Thats if there's transferFee
  String get total {
    var recipientGetAmountDouble =
        double.tryParse(recipientGetAmount.replaceAllCommas()) ?? 0;
    var doubleFee = double.tryParse(_transactionFee?.fee ?? "0") ?? 0;

    // printty("recipientGetAmountDouble $recipientGetAmount");
    // printty("recipientGetAmountDouble doubleFee $doubleFee");

    if (_transactionFee?.method == OfferConst.multiply) {
      var amount = AppUtils.formatAmountDoubleString(
          (recipientGetAmountDouble + (recipientGetAmountDouble * doubleFee))
              .toString());
      var currencyCode = _recipientCurrency?.code ?? "";

      return "$amount $currencyCode";
    } else {
      var amount = AppUtils.formatAmountDoubleString(
          (recipientGetAmountDouble + doubleFee).toString());
      var currencyCode = _recipientCurrency?.code ?? "";

      return "$amount $currencyCode";
    }
  }

  /// Check if amount is greater than balance
  /// Returns true if amount is greater than balance
  bool get amtSendIsGreaterThanBalance {
    String fromCText =
        fromC.text.isNotEmpty ? fromC.text.replaceAllCommas() : "0";
    double? fromCDouble = double.tryParse(fromCText);
    double? balanceDouble =
        double.tryParse(_fromConvertWallet?.balance.toString() ?? "0");

    // Check if both values are successfully parsed before comparing
    if (fromCDouble != null && balanceDouble != null) {
      return fromCDouble > balanceDouble;
    } else {
      // Handle the case where parsing failed for either value
      // For example, you might want to log an error or return false
      printty("Error parsing amount or balance");
      return false; // Assuming failure means the amounts cannot be compared
    }
  }

  // Check for minimum amount
  bool get recipientMinimumAmountCheck {
    final recipientAmount =
        double.tryParse(recipientC.text.trim().replaceAllCommas()) ?? 0;

    printty(
        "printty recipientAmount ${_recipientCurrency?.minimumTransferAmount}");

    return recipientC.text.trim().isNotEmpty &&
        (recipientAmount <
            (double.tryParse(
                    _recipientCurrency?.minimumTransferAmount ?? "0") ??
                0));
  }

  bool get btnEnabled {
    final amount = double.tryParse(fromC.text.trim().replaceAllCommas());
    final recipientAmount =
        double.tryParse(recipientC.text.trim().replaceAllCommas()) ?? 0;

    return amount != null &&
        amount >= 1 &&
        !amtSendIsGreaterThanBalance &&
        _fromConvertWallet != null &&
        _recipientCurrency != null &&
        // isRecipientCurrencyInFromConvertWallet &&
        recipientAmount >=
            (double.tryParse(
                    _recipientCurrency?.minimumTransferAmount ?? "0") ??
                0);
  }

  bool get ngnButtonIsActive =>
      bankNameC.text.trim().isNotEmpty && accountNumC.text.trim().isNotEmpty;

  bool get cadButtonIsActive =>
      isInteracEmaiLValid &&
      interacEmailC.text.trim().isNotEmpty &&
      interacFNameC.text.trim().isNotEmpty &&
      interacLNameC.text.trim().isNotEmpty;

  // For review screen
  String get reviewAmtYouSend {
    String amount = fromC.text.isEmpty ? "0" : fromC.text;
    amount = AppUtils.formatAmountDoubleString(amount.replaceAllCommas());
    String code = _fromConvertWallet?.currency?.code ?? "";
    return "$amount $code";
  }

  // For review screen
  String get reviewRecipientGets {
    String amount = recipientGetAmount;
    String code = _recipientCurrency?.code ?? "";
    return "$amount $code";
  }

// This set the korrency user
  setKorrencyUsername(String value) {
    _accountName = value;

    reBuildUI();
  }

  setSelectedIndex(int index) {
    _selectedIndex = index;
    reBuildUI();
  }

  // Auto fill for bank transfer method
  autoFillAcctNumberName(Beneficiary beneficiary) {
    printty(beneficiary, level: 'autoFillAcctNumberName Beneficiary');
    _bankUUID = beneficiary.institutionCode;
    bankNameC.text = beneficiary.institutionName!;
    accountNumC.text = beneficiary.accountIdentifier!;
    _accountName = beneficiary.accountName!;

    reBuildUI();
  }

  autoFillInteracEmailName(Beneficiary beneficiary) {
    printty(beneficiary, level: 'autoFillInteracEmailName Beneficiary');
    interacEmailC.text = beneficiary.accountIdentifier!;
    interacFNameC.text = beneficiary.firstName!;
    interacLNameC.text = beneficiary.lastName!;

    reBuildUI();
  }

// here
  setRecipientCurrency(Currency? currency) async {
    _recipientCurrency = currency;

    // We have to ge the conversion rate first
    // So we can have the correct multiplyBy value
    await getConversionRate();
    await getFees();
    setRecipientGetAmountToTextField();
    reBuildUI();
  }

  setFromConvertWallet({
    required List<Wallet> walletList,
    required String code,
  }) async {
    printty("setFromConvertWallet called");
    _fromConvertWallet = walletList.firstWhereOrNull(
      (element) => element.currency?.code == code,
    );

    // await getConversionRate();
    // await getFees();

    setRecipientGetAmountToTextField();
    reBuildUI();
  }

  swapCurrency() async {
    var temp = _fromConvertWallet;
    _fromConvertWallet = _walletList.firstWhereOrNull(
      (element) => element.currency?.code == _recipientCurrency?.code,
    );
    _recipientCurrency = temp?.currency;

    // call the set recipient get amount to text field

    await getConversionRate();
    await getFees();
    setRecipientGetAmountToTextField();
    reBuildUI();
  }

  Future<ApiResponse> getConversionRate() async {
    // clearTextFields(); TODO: // check this out latar
    var fromId = _fromConvertWallet?.currency?.id;
    var toId = _recipientCurrency?.id;
    return await performApiCall(
      url: "/currencies/rates/$fromId/$toId",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var res = coversionRateFromJson(json.encode(data["data"]));
        _conversionRate = res;

        return ApiResponse(success: true, data: res);
      },
    );
  }

  Future<ApiResponse> getFees() async {
    var type = FeesTypes.transfer;
    var currencyId = _recipientCurrency?.id;
    return await performApiCall(
      url: "/transactions/fees/$type/$currencyId",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _transactionFee = TransactionFee.fromJson(data["data"]);
        printty(_transactionFee, level: "Fees");
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> verifyBankAcct({
    required String bankId,
    required String accountNum,
    String? amount,
  }) async {
    return await performApiCall(
      url: "/wallets/ngn/verify-bank-account",
      method: apiService.postWithAuth,
      body: {
        "destination_bank_uuid": bankId,
        "destination_bank_account_number": accountNum,
        "amount": amount,
      },
      onSuccess: (data) {
        printty(data["data"], level: 'verify bank account');
        _verifiedBank = verifyBankFromJson(json.encode(data["data"]));
        if (_verifiedBank != null) {
          _accountName = _verifiedBank!.accountName!;
        }
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> verifyBankAcctByCurrencyId({
    required int currencyId,
    required String bankId,
    required String accountNum,
    String? amount,
  }) async {
    return await performApiCall(
      url: "/wallets/verify-bank-account/$currencyId",
      method: apiService.postWithAuth,
      body: {
        "destination_bank_uuid": bankId,
        "destination_bank_account_number": accountNum,
        "amount": amount,
      },
      busyObjectName: verifyingBankState,
      onSuccess: (data) {
        printty(data["data"], level: 'verify bank account');
        _verifiedBank = verifyBankFromJson(json.encode(data["data"]));
        if (_verifiedBank != null) {
          _accountName = _verifiedBank!.accountName ?? "No account name";
        }
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> verifyMobileWalletByCurrecyId({
    required int currencyId,
    required String destinationNumber,
  }) async {
    return await performApiCall(
      url: "/wallets/verify-mobile-wallet/$currencyId",
      method: apiService.postWithAuth,
      body: {
        "destination_number": destinationNumber,
      },
      busyObjectName: verifyingBankState,
      onSuccess: (data) {
        return ApiResponse(
            success: true, data: apiResponse.data["data"]?["status"]);
      },
    );
  }

  Future<ApiResponse> transfer(String pin, [int? transactionPurposeId]) async {
    printty(
        getTransferData(pin: pin, transactionPurposeId: transactionPurposeId),
        level: "transfer body");
    return await performApiCall(
      url: "/wallets/transfer",
      method: apiService.postWithAuth,
      body: getTransferData(
        pin: pin,
        transactionPurposeId: transactionPurposeId,
      ),
      onSuccess: (data) {
        // Track money transfer completion with Firebase Analytics
        StorageService.getUser().then((user) {
          if (user != null &&
              _fromConvertWallet != null &&
              _recipientCurrency != null) {
            final amount = double.tryParse(
                    (_fromDecimalValue ?? '0').replaceAllCommas()) ??
                0.0;
            final transactionData = data["data"];

            FirebaseAnalyticsService.instance.trackSendMoney(
              userId: user.id ?? "",
              amount: amount,
              currency: _fromConvertWallet!.currency?.code ?? "Unknown",
              senderCountry: user.country ?? "Unknown",
              recipientCountry: _recipientCurrency!.country ?? "Unknown",
              transactionId: transactionData?["id"]?.toString() ??
                  "unknown_${DateTime.now().millisecondsSinceEpoch}",
              transferMethod:
                  _transferMethod?.name ?? AnalyticsValues.transferStandard,
              corridor: "${user.country}-${_recipientCurrency!.country}",
              additionalParameters: {
                'source_currency': _fromConvertWallet!.currency?.code,
                'target_currency': _recipientCurrency!.code,
                'exchange_rate': _conversionRate?.rate?.rate,
                'transaction_purpose_id': transactionPurposeId,
              },
            );
          }
        });

        return apiResponse;
      },
    );
  }

  Map<String, dynamic> getTransferData({
    required String pin,
    int? transactionPurposeId,
  }) {
    final commonFields = {
      "source_currency_id": _fromConvertWallet?.currency?.id,
      "target_currency_id": _recipientCurrency?.id,
      "amount": (_fromDecimalValue ?? '0').replaceAllCommas(),
      "pin": pin,
      "rate": _conversionRate?.rate?.rate,
      "transaction_purpose_id": transactionPurposeId,
    };

    final transferMethods = {
      TransferMethodType.bankTransfer: {
        ...commonFields,
        "destination_bank_uuid": bankUUID,
        "destination_bank_account_number": accountNumC.text.trim(),
        "account_name": _accountName,
        "transfer_method": TransferMethod.bankTransfer,
        "bank_name": bankNameC.text.trim(),
      },
      TransferMethodType.korrency: {
        ...commonFields,
        "transfer_method": TransferMethod.korrency,
        "korrency_username": _accountName,
      },
      TransferMethodType.interac: {
        ...commonFields,
        "transfer_method": TransferMethod.interac,
        "interac_email": interacEmailC.text.trim(),
        // "interac_name": interacNameC.text.trim(),
        "interac_first_name": interacFNameC.text.trim(),
        "interac_last_name": interacLNameC.text.trim(),
      },
      TransferMethodType.mobileMoney: {
        ...commonFields,
        "destination_bank_uuid": _mobileMoney?.uuid ?? '',
        "destination_bank_account_number": _mobileMoney?.mobileNumber ?? '',
        "account_name": _mobileMoney?.recipientName ?? '',
        "transfer_method": TransferMethod.mobileMoney,
        "bank_name": _mobileMoney?.name ?? '',
      },
    };

    printty(transferMethods[_transferMethod], level: 'Transfer Data');

    return transferMethods[_transferMethod] ?? {};
  }

  clearAcctCredentials() {
    _selectedIndex = -1;
    _accountName = null;
    _verifiedBank = null;
    reBuildUI();
  }

  clearTextFields() {
    fromC.clear();
    recipientC.clear();
    bankNameC.clear();
    accountNumC.clear();
    interacEmailC.clear();
    // interacNameC.clear();
    interacFNameC.clear();
    interacLNameC.clear();
    _accountName = null;
  }

  clearBankDetails() {
    _selectedIndex = -1;
    _accountName = null;
    _verifiedBank = null;

    bankNameC.clear();
    accountNumC.clear();
    reBuildUI();
  }

  resetData() {
    clearTextFields();

    _userHasTransaction = null;
    _newCustomersRateMinimumAmount = null;

    _fromDecimalValue = null;
    _verifiedBank = null;
    _selectedIndex = -1;
    _fromConvertWallet = null;
    _recipientCurrency = null;
    _conversionRate = null;
    _transactionFee = null;

    reBuildUI();
  }

  @override
  void dispose() {
    printty("SendMoneyVM Disposed");

    fromC.dispose();
    recipientC.dispose();
    bankNameC.dispose();
    accountNumC.dispose();
    interacEmailC.dispose();
    // interacNameC.dispose();
    interacFNameC.dispose();
    interacLNameC.dispose();

    super.dispose();
  }
}
