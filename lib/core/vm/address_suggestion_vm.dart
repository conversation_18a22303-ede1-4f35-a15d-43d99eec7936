// import 'package:google_maps_webservice/places.dart';
import 'dart:convert';

import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:korrency/core/core.dart';

class AddressSuggestionVM extends BaseVM {
  final List<StateProvince> _stateProvince = [];
  List<StateProvince> get stateProvince => _stateProvince;

  List<AddressPredictions> _predictions = [];
  List<AddressPredictions> get predictions => _predictions;
  // List<AddressComponent> _addressComponent = [];
  // List<AddressComponent> get addressComponent => _addressComponent;

  final String _postalCode = "";
  String get postalCode => _postalCode;

  ViewState _predictionViewState = ViewState.idle;
  ViewState get predictionViewState => _predictionViewState;

  void setPredictionViewState(ViewState viewState) {
    _predictionViewState = viewState;
    notifyListeners();
  }

  updatePredictions(List<AddressPredictions> pre) {
    _predictions = pre;
    reBuildUI();
  }

  Future<ApiResponse> getPlacePredictions(String input,
      {String countryValue = "country:ca"}) async {
    final String googleApiKey = dotenv.env['GOOGLE_PLACES_API_KEY'] ?? "";

    final String url =
        "https://maps.googleapis.com/maps/api/place/autocomplete/json?components=$countryValue&input=$input&key=$googleApiKey";

    return await performApiCall(
      url: url,
      method: apiService.get,
      busyObjectName: LoadState.all,
      onSuccess: (data) {
        printty("data: $data", level: "getPlacePredictions");
        _predictions =
            addressPredictionsFromJson(json.encode(data["predictions"]));
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getPlaceDetails(String placeId) async {
    final String googleApiKey = dotenv.env['GOOGLE_PLACES_API_KEY'] ?? "";

    final String url =
        "https://maps.googleapis.com/maps/api/place/details/json?place_id=$placeId&key=$googleApiKey";

    return await performApiCall(
      url: url,
      method: apiService.get,
      busyObjectName: LoadState.all,
      onSuccess: (data) {
        // printty("data: $data", level: "getPlaceDetails");
        return ApiResponse(
          success: true,
          data: addressComponentFromJson(
            json.encode(
              data["result"]["address_components"],
            ),
          ),
        );
      },
    );
  }

  getStateProvince() {
    List<StateProvince> allStateProvince = [];
    allStateProvince = listOfStateProvince;
    allStateProvince.sort(
      (a, b) => a.name!.toLowerCase().compareTo(b.name!.toLowerCase()),
    );

    _stateProvince.clear();
    _stateProvince.addAll(allStateProvince);
    reBuildUI();
  }

  void searchStateProvince(String query) {
    if (query.isEmpty) {
      getStateProvince();
      return;
    }

    final List<StateProvince> searchResult = _stateProvince
        .where((element) =>
            element.name!.toLowerCase().contains(query.toLowerCase()))
        .toList();

    _stateProvince.clear();
    _stateProvince.addAll(searchResult);
    reBuildUI();
  }
}
