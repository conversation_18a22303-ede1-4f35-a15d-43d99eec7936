import 'dart:convert';

import 'package:korrency/core/core.dart';

class ReferralVM extends BaseVM {
  List<Referral> _completedRefList = [];
  List<Referral> get completedRefList => _completedRefList;
  List<Referral> _pendingRefList = [];
  List<Referral> get pendingRefList => _pendingRefList;
  Earnings? _refEarnings;
  Earnings? get refEarnings => _refEarnings;

  ReferralWallet? _refWallet;
  ReferralWallet? get refWallet => _refWallet;

  Future<ApiResponse> getReferrals() async {
    return await performApiCall(
      url: "/referrals",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var allRefList =
            referralFromJson(json.encode(data["datatable"]["data"]));
        if (allRefList.isNotEmpty) {
          _completedRefList =
              allRefList.where((ref) => ref.status == "claimed").toList();
          _pendingRefList =
              allRefList.where((ref) => ref.status != "claimed").toList();
        } else {
          _completedRefList = [];
          _pendingRefList = [];
        }

        return ApiResponse(success: true, data: allRefList);
      },
    );
  }

  Future<ApiResponse> getReferralWallet() async {
    return await performApiCall(
      url: "/referrals/wallet",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _refWallet = ReferralWallet.fromJson(data["data"]);
        return ApiResponse(success: true, data: _refWallet);
      },
    );
  }

  Future<ApiResponse> getReferralEarnings() async {
    return await performApiCall(
      url: "/referrals/earnings",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        _refEarnings = Earnings.fromJson(data["data"]);
        return ApiResponse(success: true, data: data);
      },
    );
  }

  Future<ApiResponse> claimRefBonus() async {
    return await performApiCall(
      url: "/referrals/claim",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: apiResponse.data,
          message: apiResponse.message,
        );
      },
    );
  }
}
