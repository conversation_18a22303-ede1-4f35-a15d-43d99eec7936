import 'dart:convert';

import 'package:korrency/core/core.dart';
import 'package:onfido_sdk/onfido_sdk.dart';

const String occupationUpdate = "occupationUpdate";

class KycVM extends BaseVM {
  TextEditingController firstNameC = TextEditingController();
  TextEditingController lastNameC = TextEditingController();
  TextEditingController middleNameC = TextEditingController();

  TextEditingController formattedDobC = TextEditingController();
  TextEditingController dobC = TextEditingController();
  TextEditingController genderC = TextEditingController();
  TextEditingController occupationC = TextEditingController();

  // TextEditingController countryC = TextEditingController();
  TextEditingController addressC = TextEditingController();
  TextEditingController stateC = TextEditingController();
  TextEditingController cityC = TextEditingController();
  TextEditingController postalCodeC = TextEditingController();

  List<OccupationData> _occupationDataSTORE = [];
  List<OccupationData> _occupationData = [];
  List<OccupationData> get occupationData => _occupationData;
  OccupationData? _selectedOccupation;
  OccupationData? get selectedOccupation => _selectedOccupation;

  GenderType _genderType = GenderType.none;
  GenderType get genderType => _genderType;
  OnfidoData? _onfidoData;
  OnfidoData? get onfidoData => _onfidoData;

  bool get iskycStep1Active {
    return firstNameC.text.isNotEmpty && lastNameC.text.isNotEmpty;
  }

  bool get iskycStep2Active {
    return dobC.text.isNotEmpty &&
        genderC.text.isNotEmpty &&
        occupationC.text.isNotEmpty;
  }

  bool get iskycStep3Active {
    return addressC.text.isNotEmpty &&
        stateC.text.isNotEmpty &&
        cityC.text.isNotEmpty &&
        postalCodeC.text.isNotEmpty;
  }

  setSelectedOccupation(OccupationData occupation) {
    _selectedOccupation = occupation;
    occupationC.text = occupation.name ?? "";

    reBuildUI();
  }

  setGenderType(GenderType genderType) {
    _genderType = genderType;
    genderC.text = genderType.value;
    reBuildUI();
  }

  onStateProvinceSelected(StateProvince state) {
    stateC.text = state.name ?? "";
    reBuildUI();
  }

  clearKyc3() {
    addressC.clear();
    stateC.clear();
    cityC.clear();
    postalCodeC.clear();
  }

  clearKyc2() {
    formattedDobC.clear();
    dobC.clear();
    genderC.clear();
    occupationC.clear();

    _selectedOccupation = null;
    _genderType = GenderType.none;
  }

  clearKyc1() {
    firstNameC.clear();
    lastNameC.clear();
    middleNameC.clear();
  }

  Future<ApiResponse> kycStep1() async {
    printty('Kyc Step 1');
    return await performApiCall(
      url: "/auth/profile/update",
      method: apiService.postWithAuth,
      body: {
        "first_name": firstNameC.text.trim(),
        "last_name": lastNameC.text.trim(),
        "middle_name": middleNameC.text.trim(),
        "kyc_step": 2,
      },
      onSuccess: (data) {
        StorageService.storeUser(data["data"]);
        var user = authUserFromJson(json.encode(data["data"]));
        return ApiResponse(
          success: true,
          data: user,
          message: apiResponse.message,
        );
      },
    );
  }

  Future<ApiResponse> kycStep2() async {
    printty('Kyc Step 2');
    return await performApiCall(
      url: "/auth/profile/update",
      method: apiService.postWithAuth,
      body: {
        "date_of_birth": dobC.text.trim(),
        // "occupation": occupationC.text.trim(),
        "occupation_id": _selectedOccupation?.id ?? 0,
        "gender": genderC.text.trim(),
        "kyc_step": 3,
      },
      onSuccess: (data) {
        StorageService.storeUser(data["data"]);
        var user = authUserFromJson(json.encode(data["data"]));
        return ApiResponse(
          success: true,
          data: user,
          message: apiResponse.message,
        );
      },
    );
  }

  Future<ApiResponse> kycStep3() async {
    printty('Kyc Step 3');
    return await performApiCall(
      url: "/auth/profile/update",
      method: apiService.postWithAuth,
      body: {
        "country": "canada",
        "address": addressC.text.trim(),
        "state": stateC.text.trim(),
        "city": cityC.text.trim(),
        "postal_code": postalCodeC.text.trim(),
        "kyc_step": 4,
      },
      onSuccess: (data) {
        StorageService.storeUser(data["data"]);
        var user = authUserFromJson(json.encode(data["data"]));
        return ApiResponse(
          success: true,
          data: user,
          message: apiResponse.message,
        );
      },
    );
  }

  Future<ApiResponse> updateOnfidoUser() async {
    return await performApiCall(
      url: "/verifications/onfido/update-user",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        printty(data, level: "onfido/update-user");
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> generateOnfidoToken() async {
    return await performApiCall(
      url: "/verifications/onfido/generate-sdk-token",
      method: apiService.postWithAuth,
      onSuccess: (data) {
        _onfidoData = onfidoDataFromJson(json.encode(data["data"]));
        return apiResponse;
      },
    );
  }

  Future<bool> startOnfidoWorkflow(
      {required String sdkToken, required String workFlowRunId}) async {
    try {
      final Onfido onfido = Onfido(
        sdkToken: sdkToken,
      );
      await onfido.startWorkflow(workFlowRunId);
      printty('Onfido Success from VM');
      return Future.value(true);
    } catch (error) {
      printty('Onfido Error: $error');
      return Future.value(false);
    }
  }

  Future<ApiResponse> updateUserOccupation(int id) async {
    return await performApiCall(
      url: "/auth/profile/update",
      method: apiService.postWithAuth,
      busyObjectName: occupationUpdate,
      body: {
        "occupation_id": id,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> getOccupationData() async {
    return await performApiCall(
      url: "/auth/profile/occupations",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        // printty(data, logLevel: "Raw Data");
        final res = occupationDataFromJson(json.encode(data["data"]));
        _occupationData = res;
        _occupationDataSTORE = res;
        return ApiResponse(success: true, message: data["message"]);
      },
    );
  }

  searchoccupation(String query) {
    if (query.isEmpty) {
      _occupationData = _occupationDataSTORE;
    } else {
      _occupationData = _occupationData
          .where((element) =>
              element.name!.toLowerCase().contains(query.toLowerCase()))
          .toList();
    }
    reBuildUI();
  }

  @override
  void dispose() {
    printty('KycVM Disposed');
    firstNameC.dispose();
    lastNameC.dispose();
    middleNameC.dispose();

    dobC.dispose();
    formattedDobC.dispose();
    genderC.dispose();
    occupationC.dispose();

    // countryC.dispose();
    addressC.dispose();
    stateC.dispose();
    cityC.dispose();
    postalCodeC.dispose();

    super.dispose();
  }
}
