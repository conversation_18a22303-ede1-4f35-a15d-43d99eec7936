import 'package:korrency/core/core.dart';

class TransactionPinVM extends BaseVM {
  TextEditingController pinC = TextEditingController();
  TextEditingController pinConfirmC = TextEditingController();

  bool get isPinMatched => pinC.text.trim() == pinConfirmC.text.trim();

  clearData() {
    pinC.clear();
    pinConfirmC.clear();
  }

  Future<ApiResponse> createTransactionPin() async {
    return await performApiCall(
      url: "/auth/transaction-pin/create",
      method: apiService.postWithAuth,
      body: {
        "pin": pinC.text.trim(),
        "pin_confirmation": pinConfirmC.text.trim(),
      },
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: apiResponse.data,
          message: apiResponse.message,
        );
      },
    );
  }

  Future<ApiResponse> resetTransactionPin() async {
    return await performApiCall(
      url: "/auth/transaction-pin/reset",
      method: apiService.postWithAuth,
      body: {
        "pin": pinC.text.trim(),
        "pin_confirmation": pinConfirmC.text.trim(),
      },
      onSuccess: (data) {
        return ApiResponse(
          success: true,
          data: apiResponse.data,
          message: apiResponse.message,
        );
      },
    );
  }

  @override
  void dispose() {
    printty('TransactionPinVM Dispose called');

    pinC.dispose();
    pinConfirmC.dispose();
    super.dispose();
  }
}
