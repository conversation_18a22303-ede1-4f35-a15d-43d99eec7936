import 'package:korrency/core/core.dart';

class LoginVM extends BaseVM {
  TextEditingController emailC = TextEditingController();
  TextEditingController passwordC = TextEditingController();

  bool _isValidEmail = false;
  bool get isValidEmail => _isValidEmail;

  bool _isTrustedDevice = false;
  bool get isTrustedDevice => _isTrustedDevice;

  bool _rememberMe = false;
  bool get rememberMe => _rememberMe;

  setRememberMe(bool value) {
    _rememberMe = value;
    reBuildUI();
  }

  emailIsValid() {
    _isValidEmail = emailC.text.isNotEmpty &&
        emailC.text.contains("@") &&
        emailC.text.contains(".") &&
        emailC.text.split('.').last.isNotEmpty;
    reBuildUI();
  }

  btnIsValid() {
    return _isValidEmail && passwordC.text.length > 7;
  }

  Future<ApiResponse> login({String? email}) async {
    printty("<PERSON><PERSON> got called");
    return await performApiCall(
      url: "/auth/login",
      method: apiService.post,
      body: {
        "user_name": email ?? emailC.text.trim(),
        "password": passwordC.text.trim(),
      },
      onSuccess: (data) {
        String token = data["data"]["token"];
        StorageService.storeAccessToken(token);
        _isTrustedDevice = data["data"]["is_trusted_device"];
        clearData();
        return ApiResponse(success: true, data: apiResponse.data);
      },
    );
  }

  Future<ApiResponse> logout() async {
    try {
      setBusy(true);
      await Future.delayed(const Duration(seconds: 1));
      Navigator.pushNamedAndRemoveUntil(
        NavigatorKeys.appNavigatorKey.currentContext!,
        RoutePath.loginScreen,
        (r) => false,
      );
      setBusy(false);
      String url = "/auth/logout";
      apiResponse = await apiService.postWithAuth(body: null, url: url);

      await StorageService.logout();

      printty(apiResponse, level: "Logout Response");
      return apiResponse;
    } catch (e) {
      printty(e.toString(), level: "Logout Error");
      setBusy(false);
      return ApiResponse(success: false, message: e.toString());
    }
  }

  clearData() {
    _isValidEmail = false;
    emailC.clear();
    passwordC.clear();

    reBuildUI();
  }

  @override
  void dispose() {
    printty('Login VM Disposed');
    emailC.dispose();
    passwordC.dispose();

    super.dispose();
  }
}
