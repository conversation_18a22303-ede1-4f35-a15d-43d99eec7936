import 'dart:convert';

import 'package:korrency/core/core.dart';

class WalletVM extends BaseVM {
  // Not in use for Now
  TextEditingController bvnC = TextEditingController();

  List<Wallet> _walletList = [];
  List<Wallet> get walletList => _walletList;
  Wallet? _activeWallet;
  Wallet? get activeWallet => _activeWallet;
  bool _toggleWalletVisibility = false;
  bool get toggleWalletVisibility => _toggleWalletVisibility;

  Wallet? get nairaWallet =>
      _walletList.firstWhere((element) => element.currency?.code == "NGN");
  Wallet? get cadWallet =>
      _walletList.firstWhere((element) => element.currency?.code == "CAD");

  // Return the first inactive wallet _walletList
  Wallet? get inactiveWallet => _walletList.firstWhere(
        (element) => element.id != _activeWallet?.id,
        orElse: () {
          return _walletList.first;
        },
      );

  // return available currency in recipientCurrencies thats not in the active wallet
  Currency? get toCurrency {
    return _activeWallet?.currency?.recipientCurrencies?.firstWhere(
      (element) => element.code != _activeWallet?.currency?.code,
    );
  }

  Wallet? getWalletType(String offerType) {
    return offerType == OfferConst.sellOffer ? nairaWallet : cadWallet;
  }

  // Balance deplayed on the home screen
  String get homeBalance {
    var balance = _activeWallet?.balance?.split(".")[0] ?? "0";
    return "${activeWallet?.currency?.symbol ?? ""} ${AppUtils.formatAmountString(balance)}";
  }

  // Decimal Part
  String get decimalBalance {
    var balance = _activeWallet?.balance?.split(".")[1] ?? "0";
    return balance;
  }

  setActiveWallet(Wallet wallet) {
    _activeWallet = wallet;
    reBuildUI();
  }

  setWalletVisibility() {
    _toggleWalletVisibility = !_toggleWalletVisibility;
    reBuildUI();
  }

  Future<ApiResponse> getWallets() async {
    return await performApiCall(
      url: "/wallets",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var wallets = walletFromJson(json.encode(data["data"]));

        // If the _activeWallet is null, set it to the first wallet in the list
        if (_activeWallet == null) {
          _activeWallet = wallets.first;
        } else {
          // Otherwise, search for the wallet with the same currency code as the current active wallet
          var activeWalletCurrencyCode = _activeWallet?.currency?.code;
          var activeWallet = wallets.firstWhere(
            (element) => element.currency?.code == activeWalletCurrencyCode,
            orElse: () => wallets.first,
          );
          _activeWallet = activeWallet;
        }

        _walletList = wallets;
        printty("_activeWallet: ${_activeWallet?.currency?.code ?? "null"}");
        return apiResponse;
      },
    );
  }

  Future<ApiResponse> createVirtualAccount(
      {required int currencyId, String? bvn}) async {
    return await performApiCall(
      url: "/virtual-accounts/create",
      method: apiService.postWithAuth,
      body: {
        "currency_id": currencyId,
      },
      onSuccess: (data) {
        return apiResponse;
      },
    );
  }

  void resetData() {
    printty("Resetting Wallet VM data ");

    bvnC.clear();
    _walletList = [];
    _activeWallet = null;
    _toggleWalletVisibility = false;
  }

  @override
  void dispose() {
    printty("WalletVM Disposed called");
    bvnC.dispose();

    super.dispose();
  }
}
