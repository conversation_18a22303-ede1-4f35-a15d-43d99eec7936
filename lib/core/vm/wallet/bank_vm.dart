import 'dart:convert';

import 'package:korrency/core/core.dart';

class BankVM extends BaseVM {
  List<Bank> _bankListSTORE = [];
  List<Bank> _bankList = [];
  List<Bank> get bankList => _bankList;
  List<MobileMoney> _mobileMoneyList = [];
  List<MobileMoney> get mobileMoneyList => _mobileMoneyList;
  VerifyBank? _verifiedBank;
  VerifyBank? get verifiedBank => _verifiedBank;
  // int _currentIndex = -1;
  // int get currentIndex => _currentIndex;

  // setcurrentIndex(int index) {
  //   _currentIndex = index;
  //   reBuildUI();
  // }

  Future<ApiResponse> getBanks() async {
    return await performApiCall(
      url: "/wallets/ngn/banks",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var res = bankFromJson(json.encode(data["data"]));
        _bankList = res;
        _bankListSTORE = res;
        return ApiResponse(success: true, data: _bankList);
      },
    );
  }

  Future<ApiResponse> getBanksByCurrencyId(int id) async {
    return await performApiCall(
      url: "/wallets/banks/$id",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var res = bankFromJson(json.encode(data["data"]));
        _bankList = res;
        _bankListSTORE = res;
        return ApiResponse(success: true, data: _bankList);
      },
    );
  }

  Future<ApiResponse> getMNO(int currencyId) async {
    return await performApiCall(
      url: "/wallets/mobile-network-operators/$currencyId",
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var res = mobileMoneyFromJson(json.encode(data["data"]));
        _mobileMoneyList = res;
        return ApiResponse(success: true, data: _bankList);
      },
    );
  }

  void setBankListToStore() {
    _bankList = List.from(_bankListSTORE);
  }

  // Search Banks
  void searchBanks(String query) {
    if (query.trim().isEmpty) {
      setBankListToStore();
    } else {
      final lowerCaseQuery = query.toLowerCase();
      _bankList = _bankListSTORE
          .where((bank) =>
              bank.name?.toLowerCase().contains(lowerCaseQuery) ?? false)
          .toList();
    }
    reBuildUI();
  }

  // Future<ApiResponse> verifyBankAcct({
  //   required String bankId,
  //   required String accountNum,
  //   String? amount,
  // }) async {
  //   return await performApiCall(
  //     url: "/wallets/ngn/verify-bank-account",
  //     method: apiService.postWithAuth,
  //     body: {
  //       "destination_bank_uuid": bankId,
  //       "destination_bank_account_number": accountNum,
  //       "amount": amount,
  //     },
  //     onSuccess: (data) {
  //       printty(data["data"], logLevel: 'verify bank account');
  //       _verifiedBank = verifyBankFromJson(json.encode(data["data"]));

  //       return apiResponse;
  //     },
  //   );
  // }

  resetData() {
    _verifiedBank = null;

    reBuildUI();
  }
}
