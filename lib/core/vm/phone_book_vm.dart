import 'dart:convert';

import 'package:collection/collection.dart';
import 'package:flutter_contacts/flutter_contacts.dart';
import 'package:korrency/core/core.dart';

const String fetchContactsState = 'fetchContactsState';

class PhoneBookVM extends BaseVM {
  Map<String, List<KorrencyUser>> _korrencyUsers = {};
  Map<String, List<KorrencyUser>> get korrencyUsers => _korrencyUsers;

  final List<Contact> _allContacts = [];
  List<Contact> get allContacts => _allContacts;
  bool _permissionDenied = false;
  bool get permissionDenied => _permissionDenied;

  Future<void> fetchContacts() async {
    printty("fetching contacts");
    setBusyForObject(fetchContactsState, true);
    if (!await FlutterContacts.requestPermission(readonly: true)) {
      _permissionDenied = true;
      reBuildUI();
    } else {
      final contacts = await FlutterContacts.getContacts(
          withAccounts: true, withProperties: true);
      List<String> phoneBooks = [];

      if (contacts.isNotEmpty) {
        for (var contact in contacts) {
          if (contact.phones.isNotEmpty) {
            phoneBooks.add(contact.phones.first.number.trim());
          }
        }
      }
      printty(phoneBooks, level: 'phone numbers');
      if (phoneBooks.isNotEmpty) {
        await uploadPhoneBook(phoneBooks: phoneBooks);
      }

      getPhoneBooks();
    }
    setBusyForObject(fetchContactsState, false);
    // printty(_allContacts.length, logLevel: 'contacts');
    // printty(_allContacts.first.phones, logLevel: 'single contact');
  }

  Future<ApiResponse> getPhoneBooks({String? searchQuery}) async {
    var url = searchQuery != null && searchQuery.isNotEmpty
        ? "/phone-book?search=$searchQuery"
        : "/phone-book";
    return await performApiCall(
      url: url,
      busyObjectName: fetchContactsState,
      method: apiService.getWithAuth,
      onSuccess: (data) {
        var kUsers = korrencyUserFromJson(json.encode(data["data"]));
        Map<String, List<KorrencyUser>> korrencyUsers =
            groupByFirstLetter(kUsers);

        _korrencyUsers = korrencyUsers;

        return apiResponse;
      },
    );
  }

  Future<ApiResponse> uploadPhoneBook({
    required List<String> phoneBooks,
  }) async {
    printty(phoneBooks, level: 'phone numbers');
    return await performApiCall(
      url: "/phone-book",
      method: apiService.postWithAuthWithoutFormData,
      body: {
        "phones": phoneBooks,
      },
      onSuccess: (data) {
        printty(data["data"], level: 'verify bank account');
        return apiResponse;
      },
    );
  }

  // Custom grouping function that groups by the first letter of fullName
  Map<String, List<KorrencyUser>> groupByFirstLetter(List<KorrencyUser> users) {
    var groupByFirst = groupBy(
      users,
      (KorrencyUser user) => user.fullName![0]
          .toUpperCase(), // Extracts the first letter and converts it to uppercase
    );

    // Sort the keys in alphabetical order
    var sortedKeys = groupByFirst.keys.toList()..sort();

    // Create a new map with the sorted keys
    return Map.fromEntries(
        sortedKeys.map((key) => MapEntry(key, groupByFirst[key] ?? [])));
  }
}
